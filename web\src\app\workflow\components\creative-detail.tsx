"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "~/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { ArrowLeft, Calendar, Palette, Image, Video,  Subtitles, Loader2, Camera, Edit2, Save, X, Trash2, RefreshCw } from "lucide-react";
import type { Creative, Scene, VideoItem, Subtitle } from "./workflow-main";
import { useVideoAPI } from "../hooks/use-video-api";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import { GenerationButtons } from "./generation-buttons";
import { ProgressBar } from "./progress-bar";
import { CreateSceneDialog } from "./create-scene-dialog";
import { EditCreativeDialog } from "./edit-creative-dialog";

interface CreativeDetailProps {
  creative: Creative;
  onBack: () => void;
  onCreativeUpdated?: (updatedCreative: Creative) => void;
}

export function CreativeDetail({ creative, onBack, onCreativeUpdated }: CreativeDetailProps) {
  const t = useTranslations("workflow.detail");
  const [creatives, setCreatives] = useState<Creative>(creative);
  const [scenes, setScenes] = useState<Scene[]>([]);
  const [videos, setVideos] = useState<VideoItem[]>([]);
  const [subtitles, setSubtitles] = useState<Subtitle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("scenes"); // 默认显示 scenes

  // 编辑状态
  const [editingScenes, setEditingScenes] = useState<Set<number>>(new Set());
  const [editingScenePrompts, setEditingScenePrompts] = useState<Set<number>>(new Set());
  const [editingVideoPrompts, setEditingVideoPrompts] = useState<Set<number>>(new Set());
  const [editingSubtitleTexts, setEditingSubtitleTexts] = useState<Set<number>>(new Set());



  // 重新生成状态
  const [regeneratingScenes, setRegeneratingScenes] = useState<Set<number>>(new Set());
  const [regeneratingSceneVideo, setRegeneratingSceneVideo] = useState<Set<number>>(new Set());
  const [regeneratingVideos, setRegeneratingVideos] = useState<Set<number>>(new Set());
  const [regeneratingVideoSubtitle, setRegeneratingVideoSubtitle] = useState<Set<number>>(new Set()); 
  const [regeneratingAudios, setRegeneratingAudios] = useState<Set<number>>(new Set());
  const [regenerationTaskIds, setRegenerationTaskIds] = useState<{[key: number]: string}>({});

  // 主角图片生成状态
  const [generatingCharacterImage, setGeneratingCharacterImage] = useState(false);
  const [characterImageTaskId, setCharacterImageTaskId] = useState<string | null>(null);

  // 确认对话框状态
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    title: string;
    description: string;
    onConfirm: () => void;
  }>({
    open: false,
    title: "",
    description: "",
    onConfirm: () => {}
  });

  const [editScenesData, setEditScenesData] = useState<{[key: number]: {scene: string, prompt: string}}>({});
  const [editScenePromptsData, setEditScenePromptsData] = useState<{[key: number]: string}>({});
  const [editVideoPromptsData, setEditVideoPromptsData] = useState<{[key: number]: string}>({});
  const [editSubtitleTextsData, setEditSubtitleTextsData] = useState<{[key: number]: string}>({});

  const { fetchCreativeComplete, 
    updateScene,
    deleteScene,
    regenerateSceneImage,
    regenerateSceneVideo,
    regenerateVideoSubtitle,
    updateVideo,
    regenerateVideo,
    updateSubtitle,
    generateCharacterImage,
    regenerateAudio } = useVideoAPI();

  // 确认对话框辅助函数
  const showConfirmDialog = (title: string, description: string, onConfirm: () => void) => {
    setConfirmDialog({
      open: true,
      title,
      description,
      onConfirm
    });
  };

  const closeConfirmDialog = () => {
    setConfirmDialog(prev => ({ ...prev, open: false }));
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // 加载相关数据
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 使用统一接口获取创意的所有相关数据
      const completeData = await fetchCreativeComplete(creative.id);

      // 设置创意数据
      setCreatives(completeData.creative);

      // 设置场景数据
      setScenes(completeData.scenes);

      // 设置视频数据
      setVideos(completeData.videos);

      // 设置字幕数据
      setSubtitles(completeData.subtitles);

    } catch (err) {
      console.error("Failed to load creative details:", err);
      setError("Failed to load creative details. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [creative.id, fetchCreativeComplete]);

  // 删除场景
  const handleDeleteScene = async (sceneId: number) => {
    const confirmAction = () => {
      closeConfirmDialog();
      performDeleteScene(sceneId);
    };

    showConfirmDialog(
      "删除场景",
      "确定要删除这个场景吗？删除后将无法恢复，同时会删除相关的视频和字幕。",
      confirmAction
    );
  };

  const performDeleteScene = async (sceneId: number) => {
    try {
      setLoading(true);
      await deleteScene(sceneId);

      // 重新加载数据
      await loadData();

      // 如果正在编辑这个场景，取消编辑状态
      setEditingScenes(prev => {
        const newSet = new Set(prev);
        newSet.delete(sceneId);
        return newSet;
      });

      // 清除编辑数据
      setEditScenesData(prev => {
        const newData = { ...prev };
        delete newData[sceneId];
        return newData;
      });

    } catch (err) {
      console.error("Failed to delete scene:", err);
      setError("删除场景失败，请重试。");
    } finally {
      setLoading(false);
    }
  };

  // 重新生成场景图片
  const handleRegenerateSceneImage = async (sceneId: number) => {
    const confirmAction = () => {
      closeConfirmDialog();
      performRegenerateSceneImage(sceneId);
    };

    showConfirmDialog(
      "重新生成图片",
      "确定要重新生成这个场景的图片吗？这将替换现有的图片。",
      confirmAction
    );
  };

  const performRegenerateSceneImage = async (sceneId: number) => {
    try {
      setRegeneratingScenes(prev => new Set(prev).add(sceneId));

      const result = await regenerateSceneImage(sceneId);

      if (result.task_id) {
        setRegenerationTaskIds(prev => ({
          ...prev,
          [sceneId]: result.task_id
        }));
      } else {
        // 如果没有任务ID，直接刷新数据
        await loadData();
        setRegeneratingScenes(prev => {
          const newSet = new Set(prev);
          newSet.delete(sceneId);
          return newSet;
        });
      }

    } catch (err) {
      console.error("Failed to regenerate scene image:", err);
      setRegeneratingScenes(prev => {
        const newSet = new Set(prev);
        newSet.delete(sceneId);
        return newSet;
      });
    }
  };

  // 编辑视频 prompt
  const handleEditVideoPrompt = (videoId: number, currentPrompt: string) => {
    setEditingVideoPrompts(prev => new Set(prev).add(videoId));
    setEditVideoPromptsData(prev => ({
      ...prev,
      [videoId]: currentPrompt || ""
    }));
  };

  // 取消编辑视频 prompt
  const handleCancelEditVideoPrompt = (videoId: number) => {
    setEditingVideoPrompts(prev => {
      const newSet = new Set(prev);
      newSet.delete(videoId);
      return newSet;
    });
    setEditVideoPromptsData(prev => {
      const newData = { ...prev };
      delete newData[videoId];
      return newData;
    });
  };

  // 保存视频 prompt
  const handleSaveVideoPrompt = async (videoId: number) => {
    const newPrompt = editVideoPromptsData[videoId];
    if (newPrompt === undefined) return;

    try {
      setLoading(true);
      await updateVideo(videoId, { prompt: newPrompt });
      await loadData();
      handleCancelEditVideoPrompt(videoId);
    } catch (err) {
      console.error("Failed to update video prompt:", err);
      setError("保存视频提示词失败，请重试。");
    } finally {
      setLoading(false);
    }
  };

  // 重新生成视频
  const handleRegenerateVideo = async (videoId: number) => {
    const confirmAction = () => {
      closeConfirmDialog();
      performRegenerateVideo(videoId);
    };

    showConfirmDialog(
      "重新生成视频",
      "确定要重新生成这个视频吗？这将替换现有的视频文件。",
      confirmAction
    );
  };

  const performRegenerateVideo = async (videoId: number) => {
    try {
      setRegeneratingVideos(prev => new Set(prev).add(videoId));
      const result = await regenerateVideo(videoId);

      if (result.task_id) {
        setRegenerationTaskIds(prev => ({
          ...prev,
          [videoId]: result.task_id
        }));
      } else {
        await loadData();
        setRegeneratingVideos(prev => {
          const newSet = new Set(prev);
          newSet.delete(videoId);
          return newSet;
        });
      }
    } catch (err) {
      console.error("Failed to regenerate video:", err);
      setRegeneratingVideos(prev => {
        const newSet = new Set(prev);
        newSet.delete(videoId);
        return newSet;
      });
    }
  };

  // 编辑字幕文本
  const handleEditSubtitleText = (subtitleId: number, currentText: string) => {
    setEditingSubtitleTexts(prev => new Set(prev).add(subtitleId));
    setEditSubtitleTextsData(prev => ({
      ...prev,
      [subtitleId]: currentText || ""
    }));
  };

  // 取消编辑字幕文本
  const handleCancelEditSubtitleText = (subtitleId: number) => {
    setEditingSubtitleTexts(prev => {
      const newSet = new Set(prev);
      newSet.delete(subtitleId);
      return newSet;
    });
    setEditSubtitleTextsData(prev => {
      const newData = { ...prev };
      delete newData[subtitleId];
      return newData;
    });
  };

  // 保存字幕文本
  const handleSaveSubtitleText = async (subtitleId: number) => {
    const newText = editSubtitleTextsData[subtitleId];
    if (newText === undefined) return;

    try {
      setLoading(true);
      await updateSubtitle(subtitleId, { subtitle: newText });
      await loadData();
      handleCancelEditSubtitleText(subtitleId);
    } catch (err) {
      console.error("Failed to update subtitle:", err);
      setError("保存字幕失败，请重试。");
    } finally {
      setLoading(false);
    }
  };

  // 重新生成音频
  const handleRegenerateAudio = async (subtitleId: number) => {
    const confirmAction = () => {
      closeConfirmDialog();
      performRegenerateAudio(subtitleId);
    };

    showConfirmDialog(
      "重新生成音频",
      "确定要重新生成这个字幕的音频吗？这将替换现有的音频文件。",
      confirmAction
    );
  };

  const performRegenerateAudio = async (subtitleId: number) => {
    try {
      setRegeneratingAudios(prev => new Set(prev).add(subtitleId));
      const result = await regenerateAudio(subtitleId);

      if (result.task_id) {
        setRegenerationTaskIds(prev => ({
          ...prev,
          [subtitleId]: result.task_id
        }));
      } else {
        await loadData();
        setRegeneratingAudios(prev => {
          const newSet = new Set(prev);
          newSet.delete(subtitleId);
          return newSet;
        });
      }
    } catch (err) {
      console.error("Failed to regenerate audio:", err);
      setRegeneratingAudios(prev => {
        const newSet = new Set(prev);
        newSet.delete(subtitleId);
        return newSet;
      });
    }
  };

  // 生成主角图片
  const handleGenerateCharacterImage = async () => {
    const confirmAction = () => {
      closeConfirmDialog();
      performGenerateCharacterImage();
    };

    showConfirmDialog(
      "生成主角图片",
      "确定要生成主角图片吗？这将根据主角提示词生成新的图片。",
      confirmAction
    );
  };

  const performGenerateCharacterImage = async () => {
    try {
      setGeneratingCharacterImage(true);
      const result = await generateCharacterImage(creative.id);

      if (result.task_id) {
        setCharacterImageTaskId(result.task_id);
      } else {
        await loadData();
        setGeneratingCharacterImage(false);
      }
    } catch (err) {
      console.error("Failed to generate character image:", err);
      setGeneratingCharacterImage(false);
    }
  };

  // 保存场景更改
  const handleSaveScene = async (sceneId: number) => {
    const sceneData = editScenesData[sceneId];
    if (!sceneData) return;

    try {
      setLoading(true);
      await updateScene(sceneId, {
        scene: sceneData.scene,
        prompt: sceneData.prompt
      });

      // 重新加载数据
      await loadData();

      // 退出编辑模式
      setEditingScenes(prev => {
        const newSet = new Set(prev);
        newSet.delete(sceneId);
        return newSet;
      });

    } catch (err) {
      console.error("Failed to update scene:", err);
      setError("保存场景失败，请重试。");
    } finally {
      setLoading(false);
    }
  };

  // 开始编辑场景 prompt
  const handleEditScenePrompt = (sceneId: number, currentPrompt: string) => {
    setEditingScenePrompts(prev => new Set(prev).add(sceneId));
    setEditScenePromptsData(prev => ({
      ...prev,
      [sceneId]: currentPrompt
    }));
  };

  // 取消编辑场景 prompt
  const handleCancelEditScenePrompt = (sceneId: number) => {
    setEditingScenePrompts(prev => {
      const newSet = new Set(prev);
      newSet.delete(sceneId);
      return newSet;
    });
    setEditScenePromptsData(prev => {
      const newData = { ...prev };
      delete newData[sceneId];
      return newData;
    });
  };

  // 保存场景 prompt 更改
  const handleSaveScenePrompt = async (sceneId: number) => {
    const newPrompt = editScenePromptsData[sceneId];
    if (!newPrompt) return;

    const scene = scenes.find(s => s.id === sceneId);
    if (!scene) return;

    try {
      setLoading(true);
      await updateScene(sceneId, {
        scene: scene.scene,
        prompt: newPrompt
      });

      // 重新加载数据
      await loadData();

      // 退出编辑模式
      handleCancelEditScenePrompt(sceneId);

    } catch (err) {
      console.error("Failed to update scene prompt:", err);
      setError("保存场景提示词失败，请重试。");
    } finally {
      setLoading(false);
    }
  };

  // 重新生成场景视频
  const handleRegenerateSceneVideo = async (sceneId: number) => {
    const confirmAction = () => {
      closeConfirmDialog();
      performRegenerateSceneVideo(sceneId);
    };

    showConfirmDialog(
      "重新生成视频",
      "确定要重新生成这个场景的视频吗？这将替换现有的视频。",
      confirmAction
    );
  };

  const performRegenerateSceneVideo = async (sceneId: number) => {
    try {
      setRegeneratingSceneVideo(prev => new Set(prev).add(sceneId));

      const result = await regenerateSceneVideo(sceneId);

      if (result.task_id) {
        setRegenerationTaskIds(prev => ({
          ...prev,
          [sceneId]: result.task_id
        }));
      } else {
        // 如果没有任务ID，直接刷新数据
        await loadData();
        setRegeneratingSceneVideo(prev => {
          const newSet = new Set(prev);
          newSet.delete(sceneId);
          return newSet;
        });
      }

    } catch (err) {
      console.error("Failed to regenerate scene image:", err);
      setRegeneratingSceneVideo(prev => {
        const newSet = new Set(prev);
        newSet.delete(sceneId);
        return newSet;
      });
    }
  };

  // 重新生成场景视频
  const handleRegenerateVideoSubtitle = async (videoId: number) => {
    const confirmAction = () => {
      closeConfirmDialog();
      performRegenerateVideoSubtitle(videoId);
    };

    showConfirmDialog(
      "重新生成字幕",
      "确定要重新生成这个视频的字幕吗？这将替换现有的字幕。",
      confirmAction
    );
  };

  const performRegenerateVideoSubtitle = async (videoId: number) => {
    try {
      setRegeneratingSceneVideo(prev => new Set(prev).add(videoId));

      const result = await regenerateVideoSubtitle(videoId);

      if (result.task_id) {
        setRegenerationTaskIds(prev => ({
          ...prev,
          [videoId]: result.task_id
        }));
      } else {
        // 如果没有任务ID，直接刷新数据
        await loadData();
        setRegeneratingVideoSubtitle(prev => {
          const newSet = new Set(prev);
          newSet.delete(videoId);
          return newSet;
        });
      }

    } catch (err) {
      console.error("Failed to regenerate scene image:", err);
      setRegeneratingVideoSubtitle(prev => {
        const newSet = new Set(prev);
        newSet.delete(videoId);
        return newSet;
      });
    }
  };

  if (loading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="text-lg">Loading creative details...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-full w-full items-center justify-center p-6">
        <Alert className="max-w-md">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* 头部 */}
      <div className="flex items-center justify-between p-6 border-b bg-muted/30 flex-shrink-0">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
            Back to List
          </Button>
          <CreateSceneDialog
            creativeId={creative.id}
            onSceneCreated={loadData}
          />
          <div>
            <h1 className="text-2xl font-bold line-clamp-2">{creative.creative}</h1>
            <div className="flex items-center gap-4 mt-2">
              <Badge variant="outline">ID: {creative.id}</Badge>
              {creative.art_style && (
                <Badge variant="secondary">
                  <Palette className="h-3 w-3 mr-1" />
                  {creative.art_style}
                </Badge>
              )}
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Calendar className="h-3 w-3" />
                {formatDate(creative.update_time)}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex-1 overflow-auto p-6">
        {/* 创意详细信息卡片 */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                创意详细信息
              </div>
              <EditCreativeDialog
                creative={creatives}
                onCreativeUpdated={loadData}
              />
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* 场景数量 */}
              <div>
                <label className="text-sm font-medium text-muted-foreground">场景数量</label>
                <div className="mt-1">
                  <Badge variant="outline" className="text-sm">
                    {creative.scene_cnt || 5} 个场景
                  </Badge>
                </div>
              </div>

              {/* 主角提示词 */}
              <div className="md:col-span-2">
                <label className="text-sm font-medium text-muted-foreground">主角提示词</label>
                <div className="mt-1">
                  {creative.character_prompt ? (
                    <p className="text-sm bg-muted/50 p-2 rounded border">
                      {creative.character_prompt}
                    </p>
                  ) : (
                    <p className="text-sm text-muted-foreground italic">
                      暂无主角提示词
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* 主角图片 */}
            <div>
              <label className="text-sm font-medium text-muted-foreground">主角图片</label>
              <div className="mt-2 flex items-center gap-4">
                {creative.character_image ? (
                  <div className="relative">
                    <img
                      src={creative.character_image}
                      alt="主角图片"
                      className="w-24 h-24 object-cover rounded-lg border"
                    />
                  </div>
                ) : (
                  <div className="w-24 h-24 bg-muted/50 rounded-lg border border-dashed flex items-center justify-center">
                    <Camera className="h-8 w-8 text-muted-foreground" />
                  </div>
                )}

                <div className="flex flex-col gap-2">
                  {creative.character_prompt && (
                    <Button
                      size="sm"
                      onClick={handleGenerateCharacterImage}
                      disabled={generatingCharacterImage}
                    >
                      {generatingCharacterImage ? (
                        <>
                          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                          生成中...
                        </>
                      ) : (
                        <>
                          <Camera className="h-3 w-3 mr-1" />
                          {creative.character_image ? '重新生成' : '生成主角图片'}
                        </>
                      )}
                    </Button>
                  )}

                  {!creative.character_prompt && (
                    <p className="text-xs text-muted-foreground">
                      请先设置主角提示词
                    </p>
                  )}
                </div>
              </div>

              {/* 主角图片生成进度条 */}
              {characterImageTaskId && (
                <div className="mt-3">
                  <ProgressBar
                    taskId={characterImageTaskId}
                    onComplete={() => {
                      setGeneratingCharacterImage(false);
                      setCharacterImageTaskId(null);
                      loadData(); // 刷新数据
                    }}
                    onError={() => {
                      setGeneratingCharacterImage(false);
                      setCharacterImageTaskId(null);
                    }}
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="scenes">
              {t("scenes")} ({scenes.length})
            </TabsTrigger>
            <TabsTrigger value="images">
              {t("images")} ({scenes.filter(s => s.image).length})
            </TabsTrigger>
            <TabsTrigger value="videos">
              {t("videos")} ({videos.length})
            </TabsTrigger>
            <TabsTrigger value="subtitles">
              {t("subtitles")} ({subtitles.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="scenes" className="mt-6">
            <div className="space-y-4">
              {scenes.length === 0 ? (
                <div className="text-center py-8">
                  <Camera className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No scenes yet</h3>
                  <p className="text-muted-foreground">This creative doesn't have any scenes yet.</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {scenes.map((scene) => {
                    // 获取该场景相关的视频
                    const relatedVideos = videos.filter(video => video.scene_id === scene.id);
                    // 获取该场景相关的字幕（通过视频关联）
                    const relatedSubtitles = subtitles.filter(s =>
                      relatedVideos.some(video => video.id === s.video_id)
                    );
                    const isEditing = editingScenes.has(scene.id);

                    return (
                      <Card key={scene.id}>
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <CardTitle className="text-base">
                              Scene #{scene.id}
                            </CardTitle>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {formatDate(scene.update_time)}
                              </Badge>

                              {/* 重新生图按钮 */}
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRegenerateSceneImage(scene.id)}
                                disabled={regeneratingScenes.has(scene.id)}
                                title={scene.image ? "重新生成图片" : "生成图片"}
                              >
                                {regeneratingScenes.has(scene.id) ? (
                                  <Loader2 className="h-3 w-3 animate-spin" />
                                ) : (
                                  <Camera className="h-3 w-3" />
                                )}
                              </Button>

                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  if (isEditing) {
                                    setEditingScenes(prev => {
                                      const newSet = new Set(prev);
                                      newSet.delete(scene.id);
                                      return newSet;
                                    });
                                  } else {
                                    setEditingScenes(prev => new Set(prev).add(scene.id));
                                    setEditScenesData(prev => ({
                                      ...prev,
                                      [scene.id]: {
                                        scene: scene.scene,
                                        prompt: scene.prompt
                                      }
                                    }));
                                  }
                                }}
                              >
                                {isEditing ? <X className="h-3 w-3" /> : <Edit2 className="h-3 w-3" />}
                              </Button>

                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteScene(scene.id)}
                                className="text-destructive hover:text-destructive"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          {/* 场景描述 */}
                          <div>
                            <label className="text-xs font-medium text-muted-foreground">Scene Title</label>
                            {isEditing ? (
                              <Input
                                value={editScenesData[scene.id]?.scene || scene.scene}
                                onChange={(e) => setEditScenesData(prev => ({
                                  ...prev,
                                  [scene.id]: {
                                    scene: e.target.value,
                                    prompt: prev[scene.id]?.prompt || scene.prompt
                                  }
                                }))}
                                className="mt-1 text-sm"
                              />
                            ) : (
                              <p className="mt-1 text-sm font-medium">{scene.scene}</p>
                            )}
                          </div>

                          {/* 提示词 */}
                          <div>
                            <label className="text-xs font-medium text-muted-foreground">Prompt</label>
                            {isEditing ? (
                              <Textarea
                                value={editScenesData[scene.id]?.prompt || scene.prompt}
                                onChange={(e) => setEditScenesData(prev => ({
                                  ...prev,
                                  [scene.id]: {
                                    scene: prev[scene.id]?.scene || scene.scene,
                                    prompt: e.target.value
                                  }
                                }))}
                                className="mt-1 text-sm"
                                rows={3}
                              />
                            ) : (
                              <p className="mt-1 text-sm text-muted-foreground">{scene.prompt}</p>
                            )}
                          </div>

                          {/* 场景图片预览 */}
                          {scene.image && (
                            <div className="mt-3">
                              <label className="text-xs font-medium text-muted-foreground">Generated Image</label>
                              <div className="mt-1 relative">
                                <img
                                  src={scene.image}
                                  alt={`Scene ${scene.id} image`}
                                  className="w-full h-32 object-cover rounded-md border"
                                />
                                {regeneratingScenes.has(scene.id) && (
                                  <div className="absolute inset-0 bg-black/50 rounded-md flex items-center justify-center">
                                    <div className="text-white text-xs flex items-center gap-2">
                                      <Loader2 className="h-4 w-4 animate-spin" />
                                      重新生成中...
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}

                          {/* 重新生成图片进度条 */}
                          {regeneratingScenes.has(scene.id) && regenerationTaskIds[scene.id] && (
                            <div className="mt-3">
                              <ProgressBar
                                taskId={regenerationTaskIds[scene.id] || null}
                                onComplete={() => {
                                  setRegeneratingScenes(prev => {
                                    const newSet = new Set(prev);
                                    newSet.delete(scene.id);
                                    return newSet;
                                  });
                                  setRegenerationTaskIds(prev => {
                                    const newIds = { ...prev };
                                    delete newIds[scene.id];
                                    return newIds;
                                  });
                                  loadData(); // 刷新数据
                                }}
                                onError={() => {
                                  setRegeneratingScenes(prev => {
                                    const newSet = new Set(prev);
                                    newSet.delete(scene.id);
                                    return newSet;
                                  });
                                  setRegenerationTaskIds(prev => {
                                    const newIds = { ...prev };
                                    delete newIds[scene.id];
                                    return newIds;
                                  });
                                }}
                              />
                            </div>
                          )}

                          {/* 关联的图片、视频、字幕数量 */}
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Image className="h-3 w-3" />
                              <span>{scene.image ? 1 : 0} image</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Video className="h-3 w-3" />
                              <span>{relatedVideos.length} videos</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Subtitles className="h-3 w-3" />
                              <span>{relatedSubtitles.length} subtitles</span>
                            </div>
                          </div>

                          {/* 编辑模式下的保存和取消按钮 */}
                          {isEditing && (
                            <div className="flex gap-2 pt-2">
                              <Button
                                size="sm"
                                onClick={() => handleSaveScene(scene.id)}
                                disabled={loading}
                              >
                                <Save className="h-4 w-4 mr-2" />
                                Save
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setEditingScenes(prev => {
                                    const newSet = new Set(prev);
                                    newSet.delete(scene.id);
                                    return newSet;
                                  });
                                }}
                                disabled={loading}
                              >
                                Cancel
                              </Button>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="images" className="mt-6">
            <div className="space-y-4">
              {scenes.filter(scene => scene.image).length === 0 ? (
                <div className="text-center py-8">
                  <Image className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No images yet</h3>
                  <p className="text-muted-foreground">This creative doesn't have any images yet.</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {scenes.filter(scene => scene.image).map((scene) => {

                    return (
                      <Card key={scene.id} className="overflow-hidden">
                        <div className="aspect-video bg-muted/50 relative">
                          <img
                            src={scene.image}
                            alt={`Scene ${scene.id} Image`}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              const parent = target.parentElement;
                              if (parent) {
                                parent.innerHTML = `
                                  <div class="flex items-center justify-center h-full">
                                    <div class="text-center">
                                      <div class="w-12 h-12 mx-auto mb-2 bg-muted rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                      </div>
                                      <p class="text-xs text-muted-foreground">Image not available</p>
                                    </div>
                                  </div>
                                `;
                              }
                            }}
                          />
                          <div className="absolute top-2 right-2">
                            <Badge variant="secondary" className="text-xs">
                              Scene #{scene.id}
                            </Badge>
                          </div>
                        </div>

                        <CardContent className="p-4">
                          <div className="space-y-2">
                            <div>
                              <p className="text-sm font-medium line-clamp-2">
                                {scene.scene}
                              </p>

                              {/* Prompt 编辑区域 */}
                              {editingScenePrompts.has(scene.id) ? (
                                <div className="mt-2 space-y-2">
                                  <Textarea
                                    value={editScenePromptsData[scene.id] || scene.prompt}
                                    onChange={(e) => setEditScenePromptsData(prev => ({
                                      ...prev,
                                      [scene.id]: e.target.value
                                    }))}
                                    placeholder="输入图片生成提示词..."
                                    className="text-xs min-h-[60px]"
                                  />
                                  <div className="flex gap-1">
                                    <Button
                                      size="sm"
                                      onClick={() => handleSaveScenePrompt(scene.id)}
                                      className="h-6 px-2 text-xs"
                                    >
                                      <Save className="h-3 w-3 mr-1" />
                                      保存
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleCancelEditScenePrompt(scene.id)}
                                      className="h-6 px-2 text-xs"
                                    >
                                      <X className="h-3 w-3 mr-1" />
                                      取消
                                    </Button>
                                  </div>
                                </div>
                              ) : (
                                <div className="mt-1">
                                  <p className="text-xs text-muted-foreground line-clamp-2">
                                    {scene.prompt}
                                  </p>
                                  <div className="flex gap-1 mt-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleEditScenePrompt(scene.id, scene.prompt)}
                                      className="h-6 px-2 text-xs"
                                    >
                                      <Edit2 className="h-3 w-3 mr-1" />
                                      编辑提示词
                                    </Button>

                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleRegenerateSceneImage(scene.id)}
                                      disabled={regeneratingScenes.has(scene.id)}
                                      className="h-6 px-2 text-xs"
                                    >
                                      {regeneratingScenes.has(scene.id) ? (
                                        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                      ) : (
                                        <RefreshCw className="h-3 w-3 mr-1" />
                                      )}
                                      重新生成
                                    </Button>

                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleRegenerateSceneVideo(scene.id)}
                                      disabled={regeneratingSceneVideo.has(scene.id)}
                                      className="h-6 px-2 text-xs"
                                    >
                                      {regeneratingSceneVideo.has(scene.id) ? (
                                        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                      ) : (
                                        <RefreshCw className="h-3 w-3 mr-1" />
                                      )}
                                      重新生成视频
                                    </Button>
                                                                        
                                  </div>
                                </div>
                              )}
                            </div>

                            {/* 重新生成图片进度条 */}
                            {regeneratingScenes.has(scene.id) && regenerationTaskIds[scene.id] && (
                              <div className="mt-3">
                                <ProgressBar
                                  taskId={regenerationTaskIds[scene.id] || null}
                                  onComplete={() => {
                                    setRegeneratingScenes(prev => {
                                      const newSet = new Set(prev);
                                      newSet.delete(scene.id);
                                      return newSet;
                                    });
                                    setRegenerationTaskIds(prev => {
                                      const newIds = { ...prev };
                                      delete newIds[scene.id];
                                      return newIds;
                                    });
                                    loadData(); // 刷新数据
                                  }}
                                  onError={() => {
                                    setRegeneratingScenes(prev => {
                                      const newSet = new Set(prev);
                                      newSet.delete(scene.id);
                                      return newSet;
                                    });
                                    setRegenerationTaskIds(prev => {
                                      const newIds = { ...prev };
                                      delete newIds[scene.id];
                                      return newIds;
                                    });
                                  }}
                                />
                              </div>
                            )}
                            {/* 重新生成视频进度条 */}
                            {regeneratingSceneVideo.has(scene.id) && regenerationTaskIds[scene.id] && (
                              <div className="mt-3">
                                <ProgressBar
                                  taskId={regenerationTaskIds[scene.id] || null}
                                  onComplete={() => {
                                    setRegeneratingSceneVideo(prev => {
                                      const newSet = new Set(prev);
                                      newSet.delete(scene.id);
                                      return newSet;
                                    });
                                    setRegenerationTaskIds(prev => {
                                      const newIds = { ...prev };
                                      delete newIds[scene.id];
                                      return newIds;
                                    });
                                    loadData(); // 刷新数据
                                  }}
                                  onError={() => {
                                    setRegeneratingSceneVideo(prev => {
                                      const newSet = new Set(prev);
                                      newSet.delete(scene.id);
                                      return newSet;
                                    });
                                    setRegenerationTaskIds(prev => {
                                      const newIds = { ...prev };
                                      delete newIds[scene.id];
                                      return newIds;
                                    });
                                  }}
                                />
                              </div>
                            )}

                            <div className="flex items-center justify-between text-xs text-muted-foreground">
                              <span className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {formatDate(scene.update_time)}
                              </span>
                              {videos.some(v => v.scene_id === scene.id) && (
                                <Badge variant="outline" className="text-xs">
                                  Has Video
                                </Badge>
                              )}
                            </div>

                            {/* 图片路径显示 */}
                            <div className="pt-2 border-t">
                              <p className="text-xs text-muted-foreground font-mono break-all">
                                {scene.image}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="videos" className="mt-6">
            <div className="space-y-4">
              {videos.length === 0 ? (
                <div className="text-center py-8">
                  <Video className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No videos yet</h3>
                  <p className="text-muted-foreground">This creative doesn't have any videos yet.</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {videos.map((video) => {
                    const relatedScene = scenes.find(s => s.id === video.scene_id);

                    return (
                      <Card key={video.id} className="overflow-hidden">
                        <div className="aspect-video bg-muted/50 relative">
                          <video
                            src={video.video}
                            className="w-full h-full object-cover"
                            controls
                            onError={(e) => {
                              const target = e.target as HTMLVideoElement;
                              target.style.display = 'none';
                              const parent = target.parentElement;
                              if (parent) {
                                parent.innerHTML = `
                                  <div class="flex items-center justify-center h-full">
                                    <div class="text-center">
                                      <div class="w-12 h-12 mx-auto mb-2 bg-muted rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                      </div>
                                      <p class="text-xs text-muted-foreground">Video not available</p>
                                    </div>
                                  </div>
                                `;
                              }
                            }}
                          />
                          <div className="absolute top-2 right-2">
                            <Badge variant="secondary" className="text-xs">
                              Video #{video.id}
                            </Badge>
                          </div>
                        </div>

                        <CardContent className="p-4">
                          <div className="space-y-2">
                            <div>
                              <p className="text-sm font-medium line-clamp-2">
                                {relatedScene ? relatedScene.scene : `Video #${video.id}`}
                              </p>
                              
                              {/* 视频提示词编辑区域 */}
                              {editingVideoPrompts.has(video.id) ? (
                                <div className="mt-2 space-y-2">
                                  <Textarea
                                    value={editVideoPromptsData[video.id] || video.prompt || ""}
                                    onChange={(e) => setEditVideoPromptsData(prev => ({
                                      ...prev,
                                      [video.id]: e.target.value
                                    }))}
                                    placeholder="输入视频生成提示词..."
                                    className="text-xs min-h-[60px]"
                                  />
                                  <div className="flex gap-1">
                                    <Button
                                      size="sm"
                                      onClick={() => handleSaveVideoPrompt(video.id)}
                                      className="h-6 px-2 text-xs"
                                    >
                                      <Save className="h-3 w-3 mr-1" />
                                      保存
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleCancelEditVideoPrompt(video.id)}
                                      className="h-6 px-2 text-xs"
                                    >
                                      <X className="h-3 w-3 mr-1" />
                                      取消
                                    </Button>
                                  </div>
                                </div>
                              ) : (
                                <div className="mt-1">
                                  {video.prompt && (
                                    <div>
                                      <p className="text-xs text-muted-foreground font-medium">图生视频提示词:</p>
                                      <p className="text-xs text-muted-foreground line-clamp-3 mt-1">
                                        {video.prompt}
                                      </p>
                                    </div>
                                  )}
                                  <div className="flex gap-1 mt-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleEditVideoPrompt(video.id, video.prompt || "")}
                                      className="h-6 px-2 text-xs"
                                    >
                                      <Edit2 className="h-3 w-3 mr-1" />
                                      编辑提示词
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleRegenerateVideo(video.id)}
                                      disabled={regeneratingVideos.has(video.id)}
                                      className="h-6 px-2 text-xs"
                                    >
                                      {regeneratingVideos.has(video.id) ? (
                                        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                      ) : (
                                        <RefreshCw className="h-3 w-3 mr-1" />
                                      )}
                                      重新生成
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleRegenerateVideoSubtitle(video.id)}
                                      disabled={regeneratingVideoSubtitle.has(video.id)}
                                      className="h-6 px-2 text-xs"
                                    >
                                      {regeneratingVideos.has(video.id) ? (
                                        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                      ) : (
                                        <RefreshCw className="h-3 w-3 mr-1" />
                                      )}
                                      重新生成字幕
                                    </Button>
                                  </div>
                                </div>
                              )}
                            </div>

                            {/* 重新生成进度条 */}
                            {regeneratingVideos.has(video.id) && regenerationTaskIds[video.id] && (
                              <div className="mb-2">
                                <ProgressBar
                                  taskId={regenerationTaskIds[video.id] || null}
                                  onComplete={() => {
                                    setRegeneratingVideos(prev => {
                                      const newSet = new Set(prev);
                                      newSet.delete(video.id);
                                      return newSet;
                                    });
                                    setRegenerationTaskIds(prev => {
                                      const newIds = { ...prev };
                                      delete newIds[video.id];
                                      return newIds;
                                    });
                                    loadData(); // 刷新数据
                                  }}
                                  onError={() => {
                                    setRegeneratingVideos(prev => {
                                      const newSet = new Set(prev);
                                      newSet.delete(video.id);
                                      return newSet;
                                    });
                                    setRegenerationTaskIds(prev => {
                                      const newIds = { ...prev };
                                      delete newIds[video.id];
                                      return newIds;
                                    });
                                  }}
                                />
                              </div>
                            )}

                            {/* 重新生成进度条 */}
                            {regeneratingVideoSubtitle.has(video.id) && regenerationTaskIds[video.id] && (
                              <div className="mb-2">
                                <ProgressBar
                                  taskId={regenerationTaskIds[video.id] || null}
                                  onComplete={() => {
                                    setRegeneratingVideoSubtitle(prev => {
                                      const newSet = new Set(prev);
                                      newSet.delete(video.id);
                                      return newSet;
                                    });
                                    setRegenerationTaskIds(prev => {
                                      const newIds = { ...prev };
                                      delete newIds[video.id];
                                      return newIds;
                                    });
                                    loadData(); // 刷新数据
                                  }}
                                  onError={() => {
                                    setRegeneratingVideoSubtitle(prev => {
                                      const newSet = new Set(prev);
                                      newSet.delete(video.id);
                                      return newSet;
                                    });
                                    setRegenerationTaskIds(prev => {
                                      const newIds = { ...prev };
                                      delete newIds[video.id];
                                      return newIds;
                                    });
                                  }}
                                />
                              </div>
                            )}

                            <div className="flex items-center justify-between text-xs text-muted-foreground">
                              <span className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {formatDate(video.update_time)}
                              </span>
                              {relatedScene?.image && (
                                <Badge variant="outline" className="text-xs">
                                  Has Image
                                </Badge>
                              )}
                            </div>

                            {/* 视频路径显示 */}
                            <div className="pt-2 border-t">
                              <p className="text-xs text-muted-foreground font-mono break-all">
                                {video.video}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </div>
          </TabsContent>      

          <TabsContent value="subtitles" className="mt-6">
            <div className="space-y-4">
              {subtitles.length === 0 ? (
                <div className="text-center py-8">
                  <Subtitles className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No subtitles yet</h3>
                  <p className="text-muted-foreground">This creative doesn't have any subtitles yet.</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {subtitles.map((subtitle) => {
                    const relatedVideo = videos.find(video => video.id === subtitle.video_id);
                    const relatedScene = relatedVideo ? scenes.find(s => s.id === relatedVideo.scene_id) : null;
                    const isEditing = editingSubtitleTexts.has(subtitle.id);

                    return (
                      <Card key={subtitle.id} className="overflow-hidden">
                        {/* 字幕预览区域 */}
                        <div className="aspect-video bg-muted/50 relative flex items-center justify-center p-4">
                          <div className="text-center max-w-full">
                            {isEditing ? (
                              <Textarea
                                value={editSubtitleTextsData[subtitle.id] || subtitle.subtitle || ""}
                                onChange={(e) => setEditSubtitleTextsData(prev => ({
                                  ...prev,
                                  [subtitle.id]: e.target.value
                                }))}
                                className="w-full bg-white/90 text-center resize-none"
                                rows={4}
                                placeholder="输入字幕文本..."
                              />
                            ) : (
                              <p className="text-sm font-medium text-center leading-relaxed">
                                {subtitle.subtitle || "暂无字幕文本"}
                              </p>
                            )}
                          </div>

                          {/* 右上角标识 */}
                          <div className="absolute top-2 right-2">
                            <Badge variant="secondary" className="text-xs">
                              Subtitle #{subtitle.id}
                            </Badge>
                          </div>
                        </div>

                        <CardContent className="p-4">
                          <div className="space-y-3">
                            {/* 场景和视频信息 */}
                            <div className="flex items-center justify-between text-xs text-muted-foreground">
                              <div className="flex items-center gap-2">
                                {relatedScene && (
                                  <span>From: {relatedScene.scene}</span>
                                )}
                                <Badge variant="outline" className="text-xs">
                                  Video #{subtitle.video_id}
                                </Badge>
                              </div>
                              <span className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {formatDate(subtitle.update_time)}
                              </span>
                            </div>

                            {/* 音频播放器 */}
                            {subtitle.audio && (
                              <div>
                                <audio
                                  src={subtitle.audio}
                                  controls
                                  className="w-full h-8"
                                  onError={(e) => {
                                    const target = e.target as HTMLAudioElement;
                                    target.style.display = 'none';
                                    const parent = target.parentElement;
                                    if (parent) {
                                      parent.innerHTML = `
                                        <div class="flex items-center justify-center p-2 bg-muted/50 rounded text-xs text-muted-foreground">
                                          Audio not available
                                        </div>
                                      `;
                                    }
                                  }}
                                />
                              </div>
                            )}

                            {/* 操作按钮 */}
                            <div className="flex gap-2 pt-2">
                            {isEditing ? (
                              <>
                                <Button
                                  size="sm"
                                  onClick={() => handleSaveSubtitleText(subtitle.id)}
                                >
                                  <Save className="h-4 w-4 mr-2" />
                                  保存
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleCancelEditSubtitleText(subtitle.id)}
                                >
                                  <X className="h-4 w-4 mr-2" />
                                  取消
                                </Button>
                              </>
                            ) : (
                              <>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleEditSubtitleText(subtitle.id, subtitle.subtitle || "")}
                                >
                                  <Edit2 className="h-4 w-4 mr-2" />
                                  编辑字幕
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleRegenerateAudio(subtitle.id)}
                                  disabled={regeneratingAudios.has(subtitle.id)}
                                >
                                  {regeneratingAudios.has(subtitle.id) ? (
                                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                  ) : (
                                    <RefreshCw className="h-4 w-4 mr-2" />
                                  )}
                                  重新生成音频
                                </Button>
                              </>
                            )}
                          </div>

                          {/* 重新生成音频进度条 */}
                          {regeneratingAudios.has(subtitle.id) && regenerationTaskIds[subtitle.id] && (
                            <div className="mt-4">
                              <ProgressBar
                                taskId={regenerationTaskIds[subtitle.id] || null}
                                onComplete={() => {
                                  setRegeneratingAudios(prev => {
                                    const newSet = new Set(prev);
                                    newSet.delete(subtitle.id);
                                    return newSet;
                                  });
                                  setRegenerationTaskIds(prev => {
                                    const newData = { ...prev };
                                    delete newData[subtitle.id];
                                    return newData;
                                  });
                                  loadData();
                                }}
                                onError={(error) => {
                                  setRegeneratingAudios(prev => {
                                    const newSet = new Set(prev);
                                    newSet.delete(subtitle.id);
                                    return newSet;
                                  });
                                  setError(`音频生成失败: ${error}`);
                                }}
                              />
                            </div>
                          )}
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>  
      {/* 内容区域 */}
      {/* 生成工具 - 在所有标签页下方 */}
      <div className="mt-6 mb-8">
        <GenerationButtons
          creativeId={creative.id}
          onRefresh={loadData}
        />
      </div>

      {/* 确认对话框 */}
      <Dialog open={confirmDialog.open} onOpenChange={closeConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{confirmDialog.title}</DialogTitle>
            <DialogDescription>{confirmDialog.description}</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={closeConfirmDialog}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmDialog.onConfirm}>
              确认
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>


    </div>
  );
}
