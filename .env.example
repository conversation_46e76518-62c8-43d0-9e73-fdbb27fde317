# Application Settings
DEBUG=True
APP_ENV=development

# docker build args
NEXT_PUBLIC_API_URL="http://localhost:8000/api"

AGENT_RECURSION_LIMIT=30

# Search Engine, Supported values: tavily (recommended), duckduckgo, brave_search, arxiv
SEARCH_API=tavily
TAVILY_API_KEY=tvly-xxx
# BRAVE_SEARCH_API_KEY=xxx # Required only if SEARCH_API is brave_search
# JINA_API_KEY=jina_xxx # Optional, default is None

# Optional, RAG provider
# RAG_PROVIDER=vikingdb_knowledge_base
# VIKINGDB_KNOWLEDGE_BASE_API_URL="api-knowledgebase.mlp.cn-beijing.volces.com"
# VIKINGDB_KNOWLEDGE_BASE_API_AK="AKxxx"
# VIKINGDB_KNOWLEDGE_BASE_API_SK=""
# VIKINGDB_KNOWLEDGE_BASE_RETRIEVAL_SIZE=15

# RAG_PROVIDER=ragflow
# RAGFLOW_API_URL="http://localhost:9388"
# RAGFLOW_API_KEY="ragflow-xxx"
# RAGFLOW_RETRIEVAL_SIZE=10

# Optional, volcengine TTS for generating podcast
VOLCENGINE_TTS_APPID=xxx
VOLCENGINE_TTS_ACCESS_TOKEN=xxx
# VOLCENGINE_TTS_CLUSTER=volcano_tts # Optional, default is volcano_tts
# VOLCENGINE_TTS_VOICE_TYPE=BV700_V2_streaming # Optional, default is BV700_V2_streaming

# Option, for langsmith tracing and monitoring
# LANGSMITH_TRACING=true
# LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
# LANGSMITH_API_KEY="xxx"
# LANGSMITH_PROJECT="xxx"

# [!NOTE]
# For model settings and other configurations, please refer to `docs/configuration_guide.md`

OSS_ENDPOINT="oss-rg-china-mainland.aliyuncs.com"
OSS_SECRETKEY="LTAI5tBWs1EknMeactnaxhHK"
OSS_SECRETPASS="******************************"
OSS_BUCKET="byjh-ai-data"

VIDEOFLOW_SERVER=rm-2ze2h0oa62u0cx5w2.mysql.rds.aliyuncs.com
VIDEOFLOW_PORT=3306
VIDEOFLOW_USER=microlens
VIDEOFLOW_PWD=m7i*C#r4o9
VIDEOFLOW_DB=video_flow
CONN_STR_VIDEOFLOW_DB="--default-character-set=utf8 -h$VIDEOFLOW_SERVER -P$VIDEOFLOW_PORT -u$VIDEOFLOW_USER -p$VIDEOFLOW_PWD $VIDEOFLOW_DB"

QWEN_BASE_URL="https://dashscope.aliyuncs.com/compatible-mode/v1"
QWEN_API_KEY="sk-9502dc0b92784939b2f1986d897a41ea"
QWEN_IMAGE_BASE_URL="https://dashscope.aliyuncs.com/api/v1"

# Google Gemini API 配置 (用于字幕生成)
GOOGLE_API_KEY="AIzaSyD5l9EcvN9BbDlzf5rxuJzQw1YaQ3UqD6c"

OSS_ENDPOINT="oss-rg-china-mainland.aliyuncs.com"
OSS_SECRETKEY="LTAI5tBWs1EknMeactnaxhHK"
OSS_SECRETPASS="******************************"
OSS_BUCKET="byjh-ai-data"
OSS_HTTP_PREFIX="https://byjh-ai-data.oss-rg-china-mainland.aliyuncs.com"

# SPARK TTS 本地 API 配置 (用于音频生成)
SPARK_TTS_PREFIX="http://192.168.3.31:7861/tts"
