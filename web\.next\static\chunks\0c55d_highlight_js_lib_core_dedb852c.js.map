{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/highlight.js%4010.7.3/node_modules/highlight.js/lib/core.js"], "sourcesContent": ["function deepFreeze(obj) {\n    if (obj instanceof Map) {\n        obj.clear = obj.delete = obj.set = function () {\n            throw new Error('map is read-only');\n        };\n    } else if (obj instanceof Set) {\n        obj.add = obj.clear = obj.delete = function () {\n            throw new Error('set is read-only');\n        };\n    }\n\n    // Freeze self\n    Object.freeze(obj);\n\n    Object.getOwnPropertyNames(obj).forEach(function (name) {\n        var prop = obj[name];\n\n        // Freeze prop if it is an object\n        if (typeof prop == 'object' && !Object.isFrozen(prop)) {\n            deepFreeze(prop);\n        }\n    });\n\n    return obj;\n}\n\nvar deepFreezeEs6 = deepFreeze;\nvar _default = deepFreeze;\ndeepFreezeEs6.default = _default;\n\n/** @implements CallbackResponse */\nclass Response {\n  /**\n   * @param {CompiledMode} mode\n   */\n  constructor(mode) {\n    // eslint-disable-next-line no-undefined\n    if (mode.data === undefined) mode.data = {};\n\n    this.data = mode.data;\n    this.isMatchIgnored = false;\n  }\n\n  ignoreMatch() {\n    this.isMatchIgnored = true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction escapeHTML(value) {\n  return value\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#x27;');\n}\n\n/**\n * performs a shallow merge of multiple objects into one\n *\n * @template T\n * @param {T} original\n * @param {Record<string,any>[]} objects\n * @returns {T} a single new object\n */\nfunction inherit(original, ...objects) {\n  /** @type Record<string,any> */\n  const result = Object.create(null);\n\n  for (const key in original) {\n    result[key] = original[key];\n  }\n  objects.forEach(function(obj) {\n    for (const key in obj) {\n      result[key] = obj[key];\n    }\n  });\n  return /** @type {T} */ (result);\n}\n\n/**\n * @typedef {object} Renderer\n * @property {(text: string) => void} addText\n * @property {(node: Node) => void} openNode\n * @property {(node: Node) => void} closeNode\n * @property {() => string} value\n */\n\n/** @typedef {{kind?: string, sublanguage?: boolean}} Node */\n/** @typedef {{walk: (r: Renderer) => void}} Tree */\n/** */\n\nconst SPAN_CLOSE = '</span>';\n\n/**\n * Determines if a node needs to be wrapped in <span>\n *\n * @param {Node} node */\nconst emitsWrappingTags = (node) => {\n  return !!node.kind;\n};\n\n/** @type {Renderer} */\nclass HTMLRenderer {\n  /**\n   * Creates a new HTMLRenderer\n   *\n   * @param {Tree} parseTree - the parse tree (must support `walk` API)\n   * @param {{classPrefix: string}} options\n   */\n  constructor(parseTree, options) {\n    this.buffer = \"\";\n    this.classPrefix = options.classPrefix;\n    parseTree.walk(this);\n  }\n\n  /**\n   * Adds texts to the output stream\n   *\n   * @param {string} text */\n  addText(text) {\n    this.buffer += escapeHTML(text);\n  }\n\n  /**\n   * Adds a node open to the output stream (if needed)\n   *\n   * @param {Node} node */\n  openNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    let className = node.kind;\n    if (!node.sublanguage) {\n      className = `${this.classPrefix}${className}`;\n    }\n    this.span(className);\n  }\n\n  /**\n   * Adds a node close to the output stream (if needed)\n   *\n   * @param {Node} node */\n  closeNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    this.buffer += SPAN_CLOSE;\n  }\n\n  /**\n   * returns the accumulated buffer\n  */\n  value() {\n    return this.buffer;\n  }\n\n  // helpers\n\n  /**\n   * Builds a span element\n   *\n   * @param {string} className */\n  span(className) {\n    this.buffer += `<span class=\"${className}\">`;\n  }\n}\n\n/** @typedef {{kind?: string, sublanguage?: boolean, children: Node[]} | string} Node */\n/** @typedef {{kind?: string, sublanguage?: boolean, children: Node[]} } DataNode */\n/**  */\n\nclass TokenTree {\n  constructor() {\n    /** @type DataNode */\n    this.rootNode = { children: [] };\n    this.stack = [this.rootNode];\n  }\n\n  get top() {\n    return this.stack[this.stack.length - 1];\n  }\n\n  get root() { return this.rootNode; }\n\n  /** @param {Node} node */\n  add(node) {\n    this.top.children.push(node);\n  }\n\n  /** @param {string} kind */\n  openNode(kind) {\n    /** @type Node */\n    const node = { kind, children: [] };\n    this.add(node);\n    this.stack.push(node);\n  }\n\n  closeNode() {\n    if (this.stack.length > 1) {\n      return this.stack.pop();\n    }\n    // eslint-disable-next-line no-undefined\n    return undefined;\n  }\n\n  closeAllNodes() {\n    while (this.closeNode());\n  }\n\n  toJSON() {\n    return JSON.stringify(this.rootNode, null, 4);\n  }\n\n  /**\n   * @typedef { import(\"./html_renderer\").Renderer } Renderer\n   * @param {Renderer} builder\n   */\n  walk(builder) {\n    // this does not\n    return this.constructor._walk(builder, this.rootNode);\n    // this works\n    // return TokenTree._walk(builder, this.rootNode);\n  }\n\n  /**\n   * @param {Renderer} builder\n   * @param {Node} node\n   */\n  static _walk(builder, node) {\n    if (typeof node === \"string\") {\n      builder.addText(node);\n    } else if (node.children) {\n      builder.openNode(node);\n      node.children.forEach((child) => this._walk(builder, child));\n      builder.closeNode(node);\n    }\n    return builder;\n  }\n\n  /**\n   * @param {Node} node\n   */\n  static _collapse(node) {\n    if (typeof node === \"string\") return;\n    if (!node.children) return;\n\n    if (node.children.every(el => typeof el === \"string\")) {\n      // node.text = node.children.join(\"\");\n      // delete node.children;\n      node.children = [node.children.join(\"\")];\n    } else {\n      node.children.forEach((child) => {\n        TokenTree._collapse(child);\n      });\n    }\n  }\n}\n\n/**\n  Currently this is all private API, but this is the minimal API necessary\n  that an Emitter must implement to fully support the parser.\n\n  Minimal interface:\n\n  - addKeyword(text, kind)\n  - addText(text)\n  - addSublanguage(emitter, subLanguageName)\n  - finalize()\n  - openNode(kind)\n  - closeNode()\n  - closeAllNodes()\n  - toHTML()\n\n*/\n\n/**\n * @implements {Emitter}\n */\nclass TokenTreeEmitter extends TokenTree {\n  /**\n   * @param {*} options\n   */\n  constructor(options) {\n    super();\n    this.options = options;\n  }\n\n  /**\n   * @param {string} text\n   * @param {string} kind\n   */\n  addKeyword(text, kind) {\n    if (text === \"\") { return; }\n\n    this.openNode(kind);\n    this.addText(text);\n    this.closeNode();\n  }\n\n  /**\n   * @param {string} text\n   */\n  addText(text) {\n    if (text === \"\") { return; }\n\n    this.add(text);\n  }\n\n  /**\n   * @param {Emitter & {root: DataNode}} emitter\n   * @param {string} name\n   */\n  addSublanguage(emitter, name) {\n    /** @type DataNode */\n    const node = emitter.root;\n    node.kind = name;\n    node.sublanguage = true;\n    this.add(node);\n  }\n\n  toHTML() {\n    const renderer = new HTMLRenderer(this, this.options);\n    return renderer.value();\n  }\n\n  finalize() {\n    return true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {RegExp}\n * */\nfunction escape(value) {\n  return new RegExp(value.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'), 'm');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/**\n * @param {RegExp} re\n * @returns {number}\n */\nfunction countMatchGroups(re) {\n  return (new RegExp(re.toString() + '|')).exec('').length - 1;\n}\n\n/**\n * Does lexeme start with a regular expression match at the beginning\n * @param {RegExp} re\n * @param {string} lexeme\n */\nfunction startsWith(re, lexeme) {\n  const match = re && re.exec(lexeme);\n  return match && match.index === 0;\n}\n\n// BACKREF_RE matches an open parenthesis or backreference. To avoid\n// an incorrect parse, it additionally matches the following:\n// - [...] elements, where the meaning of parentheses and escapes change\n// - other escape sequences, so we do not misparse escape sequences as\n//   interesting elements\n// - non-matching or lookahead parentheses, which do not capture. These\n//   follow the '(' with a '?'.\nconst BACKREF_RE = /\\[(?:[^\\\\\\]]|\\\\.)*\\]|\\(\\??|\\\\([1-9][0-9]*)|\\\\./;\n\n// join logically computes regexps.join(separator), but fixes the\n// backreferences so they continue to match.\n// it also places each individual regular expression into it's own\n// match group, keeping track of the sequencing of those match groups\n// is currently an exercise for the caller. :-)\n/**\n * @param {(string | RegExp)[]} regexps\n * @param {string} separator\n * @returns {string}\n */\nfunction join(regexps, separator = \"|\") {\n  let numCaptures = 0;\n\n  return regexps.map((regex) => {\n    numCaptures += 1;\n    const offset = numCaptures;\n    let re = source(regex);\n    let out = '';\n\n    while (re.length > 0) {\n      const match = BACKREF_RE.exec(re);\n      if (!match) {\n        out += re;\n        break;\n      }\n      out += re.substring(0, match.index);\n      re = re.substring(match.index + match[0].length);\n      if (match[0][0] === '\\\\' && match[1]) {\n        // Adjust the backreference.\n        out += '\\\\' + String(Number(match[1]) + offset);\n      } else {\n        out += match[0];\n        if (match[0] === '(') {\n          numCaptures++;\n        }\n      }\n    }\n    return out;\n  }).map(re => `(${re})`).join(separator);\n}\n\n// Common regexps\nconst MATCH_NOTHING_RE = /\\b\\B/;\nconst IDENT_RE = '[a-zA-Z]\\\\w*';\nconst UNDERSCORE_IDENT_RE = '[a-zA-Z_]\\\\w*';\nconst NUMBER_RE = '\\\\b\\\\d+(\\\\.\\\\d+)?';\nconst C_NUMBER_RE = '(-?)(\\\\b0[xX][a-fA-F0-9]+|(\\\\b\\\\d+(\\\\.\\\\d*)?|\\\\.\\\\d+)([eE][-+]?\\\\d+)?)'; // 0x..., 0..., decimal, float\nconst BINARY_NUMBER_RE = '\\\\b(0b[01]+)'; // 0b...\nconst RE_STARTERS_RE = '!|!=|!==|%|%=|&|&&|&=|\\\\*|\\\\*=|\\\\+|\\\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\\\?|\\\\[|\\\\{|\\\\(|\\\\^|\\\\^=|\\\\||\\\\|=|\\\\|\\\\||~';\n\n/**\n* @param { Partial<Mode> & {binary?: string | RegExp} } opts\n*/\nconst SHEBANG = (opts = {}) => {\n  const beginShebang = /^#![ ]*\\//;\n  if (opts.binary) {\n    opts.begin = concat(\n      beginShebang,\n      /.*\\b/,\n      opts.binary,\n      /\\b.*/);\n  }\n  return inherit({\n    className: 'meta',\n    begin: beginShebang,\n    end: /$/,\n    relevance: 0,\n    /** @type {ModeCallback} */\n    \"on:begin\": (m, resp) => {\n      if (m.index !== 0) resp.ignoreMatch();\n    }\n  }, opts);\n};\n\n// Common modes\nconst BACKSLASH_ESCAPE = {\n  begin: '\\\\\\\\[\\\\s\\\\S]', relevance: 0\n};\nconst APOS_STRING_MODE = {\n  className: 'string',\n  begin: '\\'',\n  end: '\\'',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst QUOTE_STRING_MODE = {\n  className: 'string',\n  begin: '\"',\n  end: '\"',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst PHRASAL_WORDS_MODE = {\n  begin: /\\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\\b/\n};\n/**\n * Creates a comment mode\n *\n * @param {string | RegExp} begin\n * @param {string | RegExp} end\n * @param {Mode | {}} [modeOptions]\n * @returns {Partial<Mode>}\n */\nconst COMMENT = function(begin, end, modeOptions = {}) {\n  const mode = inherit(\n    {\n      className: 'comment',\n      begin,\n      end,\n      contains: []\n    },\n    modeOptions\n  );\n  mode.contains.push(PHRASAL_WORDS_MODE);\n  mode.contains.push({\n    className: 'doctag',\n    begin: '(?:TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):',\n    relevance: 0\n  });\n  return mode;\n};\nconst C_LINE_COMMENT_MODE = COMMENT('//', '$');\nconst C_BLOCK_COMMENT_MODE = COMMENT('/\\\\*', '\\\\*/');\nconst HASH_COMMENT_MODE = COMMENT('#', '$');\nconst NUMBER_MODE = {\n  className: 'number',\n  begin: NUMBER_RE,\n  relevance: 0\n};\nconst C_NUMBER_MODE = {\n  className: 'number',\n  begin: C_NUMBER_RE,\n  relevance: 0\n};\nconst BINARY_NUMBER_MODE = {\n  className: 'number',\n  begin: BINARY_NUMBER_RE,\n  relevance: 0\n};\nconst CSS_NUMBER_MODE = {\n  className: 'number',\n  begin: NUMBER_RE + '(' +\n    '%|em|ex|ch|rem' +\n    '|vw|vh|vmin|vmax' +\n    '|cm|mm|in|pt|pc|px' +\n    '|deg|grad|rad|turn' +\n    '|s|ms' +\n    '|Hz|kHz' +\n    '|dpi|dpcm|dppx' +\n    ')?',\n  relevance: 0\n};\nconst REGEXP_MODE = {\n  // this outer rule makes sure we actually have a WHOLE regex and not simply\n  // an expression such as:\n  //\n  //     3 / something\n  //\n  // (which will then blow up when regex's `illegal` sees the newline)\n  begin: /(?=\\/[^/\\n]*\\/)/,\n  contains: [{\n    className: 'regexp',\n    begin: /\\//,\n    end: /\\/[gimuy]*/,\n    illegal: /\\n/,\n    contains: [\n      BACKSLASH_ESCAPE,\n      {\n        begin: /\\[/,\n        end: /\\]/,\n        relevance: 0,\n        contains: [BACKSLASH_ESCAPE]\n      }\n    ]\n  }]\n};\nconst TITLE_MODE = {\n  className: 'title',\n  begin: IDENT_RE,\n  relevance: 0\n};\nconst UNDERSCORE_TITLE_MODE = {\n  className: 'title',\n  begin: UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\nconst METHOD_GUARD = {\n  // excludes method names from keyword processing\n  begin: '\\\\.\\\\s*' + UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\n\n/**\n * Adds end same as begin mechanics to a mode\n *\n * Your mode must include at least a single () match group as that first match\n * group is what is used for comparison\n * @param {Partial<Mode>} mode\n */\nconst END_SAME_AS_BEGIN = function(mode) {\n  return Object.assign(mode,\n    {\n      /** @type {ModeCallback} */\n      'on:begin': (m, resp) => { resp.data._beginMatch = m[1]; },\n      /** @type {ModeCallback} */\n      'on:end': (m, resp) => { if (resp.data._beginMatch !== m[1]) resp.ignoreMatch(); }\n    });\n};\n\nvar MODES = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    MATCH_NOTHING_RE: MATCH_NOTHING_RE,\n    IDENT_RE: IDENT_RE,\n    UNDERSCORE_IDENT_RE: UNDERSCORE_IDENT_RE,\n    NUMBER_RE: NUMBER_RE,\n    C_NUMBER_RE: C_NUMBER_RE,\n    BINARY_NUMBER_RE: BINARY_NUMBER_RE,\n    RE_STARTERS_RE: RE_STARTERS_RE,\n    SHEBANG: SHEBANG,\n    BACKSLASH_ESCAPE: BACKSLASH_ESCAPE,\n    APOS_STRING_MODE: APOS_STRING_MODE,\n    QUOTE_STRING_MODE: QUOTE_STRING_MODE,\n    PHRASAL_WORDS_MODE: PHRASAL_WORDS_MODE,\n    COMMENT: COMMENT,\n    C_LINE_COMMENT_MODE: C_LINE_COMMENT_MODE,\n    C_BLOCK_COMMENT_MODE: C_BLOCK_COMMENT_MODE,\n    HASH_COMMENT_MODE: HASH_COMMENT_MODE,\n    NUMBER_MODE: NUMBER_MODE,\n    C_NUMBER_MODE: C_NUMBER_MODE,\n    BINARY_NUMBER_MODE: BINARY_NUMBER_MODE,\n    CSS_NUMBER_MODE: CSS_NUMBER_MODE,\n    REGEXP_MODE: REGEXP_MODE,\n    TITLE_MODE: TITLE_MODE,\n    UNDERSCORE_TITLE_MODE: UNDERSCORE_TITLE_MODE,\n    METHOD_GUARD: METHOD_GUARD,\n    END_SAME_AS_BEGIN: END_SAME_AS_BEGIN\n});\n\n// Grammar extensions / plugins\n// See: https://github.com/highlightjs/highlight.js/issues/2833\n\n// Grammar extensions allow \"syntactic sugar\" to be added to the grammar modes\n// without requiring any underlying changes to the compiler internals.\n\n// `compileMatch` being the perfect small example of now allowing a grammar\n// author to write `match` when they desire to match a single expression rather\n// than being forced to use `begin`.  The extension then just moves `match` into\n// `begin` when it runs.  Ie, no features have been added, but we've just made\n// the experience of writing (and reading grammars) a little bit nicer.\n\n// ------\n\n// TODO: We need negative look-behind support to do this properly\n/**\n * Skip a match if it has a preceding dot\n *\n * This is used for `beginKeywords` to prevent matching expressions such as\n * `bob.keyword.do()`. The mode compiler automatically wires this up as a\n * special _internal_ 'on:begin' callback for modes with `beginKeywords`\n * @param {RegExpMatchArray} match\n * @param {CallbackResponse} response\n */\nfunction skipIfhasPrecedingDot(match, response) {\n  const before = match.input[match.index - 1];\n  if (before === \".\") {\n    response.ignoreMatch();\n  }\n}\n\n\n/**\n * `beginKeywords` syntactic sugar\n * @type {CompilerExt}\n */\nfunction beginKeywords(mode, parent) {\n  if (!parent) return;\n  if (!mode.beginKeywords) return;\n\n  // for languages with keywords that include non-word characters checking for\n  // a word boundary is not sufficient, so instead we check for a word boundary\n  // or whitespace - this does no harm in any case since our keyword engine\n  // doesn't allow spaces in keywords anyways and we still check for the boundary\n  // first\n  mode.begin = '\\\\b(' + mode.beginKeywords.split(' ').join('|') + ')(?!\\\\.)(?=\\\\b|\\\\s)';\n  mode.__beforeBegin = skipIfhasPrecedingDot;\n  mode.keywords = mode.keywords || mode.beginKeywords;\n  delete mode.beginKeywords;\n\n  // prevents double relevance, the keywords themselves provide\n  // relevance, the mode doesn't need to double it\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 0;\n}\n\n/**\n * Allow `illegal` to contain an array of illegal values\n * @type {CompilerExt}\n */\nfunction compileIllegal(mode, _parent) {\n  if (!Array.isArray(mode.illegal)) return;\n\n  mode.illegal = either(...mode.illegal);\n}\n\n/**\n * `match` to match a single expression for readability\n * @type {CompilerExt}\n */\nfunction compileMatch(mode, _parent) {\n  if (!mode.match) return;\n  if (mode.begin || mode.end) throw new Error(\"begin & end are not supported with match\");\n\n  mode.begin = mode.match;\n  delete mode.match;\n}\n\n/**\n * provides the default 1 relevance to all modes\n * @type {CompilerExt}\n */\nfunction compileRelevance(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 1;\n}\n\n// keywords that should have no default relevance value\nconst COMMON_KEYWORDS = [\n  'of',\n  'and',\n  'for',\n  'in',\n  'not',\n  'or',\n  'if',\n  'then',\n  'parent', // common variable name\n  'list', // common variable name\n  'value' // common variable name\n];\n\nconst DEFAULT_KEYWORD_CLASSNAME = \"keyword\";\n\n/**\n * Given raw keywords from a language definition, compile them.\n *\n * @param {string | Record<string,string|string[]> | Array<string>} rawKeywords\n * @param {boolean} caseInsensitive\n */\nfunction compileKeywords(rawKeywords, caseInsensitive, className = DEFAULT_KEYWORD_CLASSNAME) {\n  /** @type KeywordDict */\n  const compiledKeywords = {};\n\n  // input can be a string of keywords, an array of keywords, or a object with\n  // named keys representing className (which can then point to a string or array)\n  if (typeof rawKeywords === 'string') {\n    compileList(className, rawKeywords.split(\" \"));\n  } else if (Array.isArray(rawKeywords)) {\n    compileList(className, rawKeywords);\n  } else {\n    Object.keys(rawKeywords).forEach(function(className) {\n      // collapse all our objects back into the parent object\n      Object.assign(\n        compiledKeywords,\n        compileKeywords(rawKeywords[className], caseInsensitive, className)\n      );\n    });\n  }\n  return compiledKeywords;\n\n  // ---\n\n  /**\n   * Compiles an individual list of keywords\n   *\n   * Ex: \"for if when while|5\"\n   *\n   * @param {string} className\n   * @param {Array<string>} keywordList\n   */\n  function compileList(className, keywordList) {\n    if (caseInsensitive) {\n      keywordList = keywordList.map(x => x.toLowerCase());\n    }\n    keywordList.forEach(function(keyword) {\n      const pair = keyword.split('|');\n      compiledKeywords[pair[0]] = [className, scoreForKeyword(pair[0], pair[1])];\n    });\n  }\n}\n\n/**\n * Returns the proper score for a given keyword\n *\n * Also takes into account comment keywords, which will be scored 0 UNLESS\n * another score has been manually assigned.\n * @param {string} keyword\n * @param {string} [providedScore]\n */\nfunction scoreForKeyword(keyword, providedScore) {\n  // manual scores always win over common keywords\n  // so you can force a score of 1 if you really insist\n  if (providedScore) {\n    return Number(providedScore);\n  }\n\n  return commonKeyword(keyword) ? 0 : 1;\n}\n\n/**\n * Determines if a given keyword is common or not\n *\n * @param {string} keyword */\nfunction commonKeyword(keyword) {\n  return COMMON_KEYWORDS.includes(keyword.toLowerCase());\n}\n\n// compilation\n\n/**\n * Compiles a language definition result\n *\n * Given the raw result of a language definition (Language), compiles this so\n * that it is ready for highlighting code.\n * @param {Language} language\n * @param {{plugins: HLJSPlugin[]}} opts\n * @returns {CompiledLanguage}\n */\nfunction compileLanguage(language, { plugins }) {\n  /**\n   * Builds a regex with the case sensativility of the current language\n   *\n   * @param {RegExp | string} value\n   * @param {boolean} [global]\n   */\n  function langRe(value, global) {\n    return new RegExp(\n      source(value),\n      'm' + (language.case_insensitive ? 'i' : '') + (global ? 'g' : '')\n    );\n  }\n\n  /**\n    Stores multiple regular expressions and allows you to quickly search for\n    them all in a string simultaneously - returning the first match.  It does\n    this by creating a huge (a|b|c) regex - each individual item wrapped with ()\n    and joined by `|` - using match groups to track position.  When a match is\n    found checking which position in the array has content allows us to figure\n    out which of the original regexes / match groups triggered the match.\n\n    The match object itself (the result of `Regex.exec`) is returned but also\n    enhanced by merging in any meta-data that was registered with the regex.\n    This is how we keep track of which mode matched, and what type of rule\n    (`illegal`, `begin`, end, etc).\n  */\n  class MultiRegex {\n    constructor() {\n      this.matchIndexes = {};\n      // @ts-ignore\n      this.regexes = [];\n      this.matchAt = 1;\n      this.position = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      opts.position = this.position++;\n      // @ts-ignore\n      this.matchIndexes[this.matchAt] = opts;\n      this.regexes.push([opts, re]);\n      this.matchAt += countMatchGroups(re) + 1;\n    }\n\n    compile() {\n      if (this.regexes.length === 0) {\n        // avoids the need to check length every time exec is called\n        // @ts-ignore\n        this.exec = () => null;\n      }\n      const terminators = this.regexes.map(el => el[1]);\n      this.matcherRe = langRe(join(terminators), true);\n      this.lastIndex = 0;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      this.matcherRe.lastIndex = this.lastIndex;\n      const match = this.matcherRe.exec(s);\n      if (!match) { return null; }\n\n      // eslint-disable-next-line no-undefined\n      const i = match.findIndex((el, i) => i > 0 && el !== undefined);\n      // @ts-ignore\n      const matchData = this.matchIndexes[i];\n      // trim off any earlier non-relevant match groups (ie, the other regex\n      // match groups that make up the multi-matcher)\n      match.splice(0, i);\n\n      return Object.assign(match, matchData);\n    }\n  }\n\n  /*\n    Created to solve the key deficiently with MultiRegex - there is no way to\n    test for multiple matches at a single location.  Why would we need to do\n    that?  In the future a more dynamic engine will allow certain matches to be\n    ignored.  An example: if we matched say the 3rd regex in a large group but\n    decided to ignore it - we'd need to started testing again at the 4th\n    regex... but MultiRegex itself gives us no real way to do that.\n\n    So what this class creates MultiRegexs on the fly for whatever search\n    position they are needed.\n\n    NOTE: These additional MultiRegex objects are created dynamically.  For most\n    grammars most of the time we will never actually need anything more than the\n    first MultiRegex - so this shouldn't have too much overhead.\n\n    Say this is our search group, and we match regex3, but wish to ignore it.\n\n      regex1 | regex2 | regex3 | regex4 | regex5    ' ie, startAt = 0\n\n    What we need is a new MultiRegex that only includes the remaining\n    possibilities:\n\n      regex4 | regex5                               ' ie, startAt = 3\n\n    This class wraps all that complexity up in a simple API... `startAt` decides\n    where in the array of expressions to start doing the matching. It\n    auto-increments, so if a match is found at position 2, then startAt will be\n    set to 3.  If the end is reached startAt will return to 0.\n\n    MOST of the time the parser will be setting startAt manually to 0.\n  */\n  class ResumableMultiRegex {\n    constructor() {\n      // @ts-ignore\n      this.rules = [];\n      // @ts-ignore\n      this.multiRegexes = [];\n      this.count = 0;\n\n      this.lastIndex = 0;\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    getMatcher(index) {\n      if (this.multiRegexes[index]) return this.multiRegexes[index];\n\n      const matcher = new MultiRegex();\n      this.rules.slice(index).forEach(([re, opts]) => matcher.addRule(re, opts));\n      matcher.compile();\n      this.multiRegexes[index] = matcher;\n      return matcher;\n    }\n\n    resumingScanAtSamePosition() {\n      return this.regexIndex !== 0;\n    }\n\n    considerAll() {\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      this.rules.push([re, opts]);\n      if (opts.type === \"begin\") this.count++;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      const m = this.getMatcher(this.regexIndex);\n      m.lastIndex = this.lastIndex;\n      let result = m.exec(s);\n\n      // The following is because we have no easy way to say \"resume scanning at the\n      // existing position but also skip the current rule ONLY\". What happens is\n      // all prior rules are also skipped which can result in matching the wrong\n      // thing. Example of matching \"booger\":\n\n      // our matcher is [string, \"booger\", number]\n      //\n      // ....booger....\n\n      // if \"booger\" is ignored then we'd really need a regex to scan from the\n      // SAME position for only: [string, number] but ignoring \"booger\" (if it\n      // was the first match), a simple resume would scan ahead who knows how\n      // far looking only for \"number\", ignoring potential string matches (or\n      // future \"booger\" matches that might be valid.)\n\n      // So what we do: We execute two matchers, one resuming at the same\n      // position, but the second full matcher starting at the position after:\n\n      //     /--- resume first regex match here (for [number])\n      //     |/---- full match here for [string, \"booger\", number]\n      //     vv\n      // ....booger....\n\n      // Which ever results in a match first is then used. So this 3-4 step\n      // process essentially allows us to say \"match at this position, excluding\n      // a prior rule that was ignored\".\n      //\n      // 1. Match \"booger\" first, ignore. Also proves that [string] does non match.\n      // 2. Resume matching for [number]\n      // 3. Match at index + 1 for [string, \"booger\", number]\n      // 4. If #2 and #3 result in matches, which came first?\n      if (this.resumingScanAtSamePosition()) {\n        if (result && result.index === this.lastIndex) ; else { // use the second matcher result\n          const m2 = this.getMatcher(0);\n          m2.lastIndex = this.lastIndex + 1;\n          result = m2.exec(s);\n        }\n      }\n\n      if (result) {\n        this.regexIndex += result.position + 1;\n        if (this.regexIndex === this.count) {\n          // wrap-around to considering all matches again\n          this.considerAll();\n        }\n      }\n\n      return result;\n    }\n  }\n\n  /**\n   * Given a mode, builds a huge ResumableMultiRegex that can be used to walk\n   * the content and find matches.\n   *\n   * @param {CompiledMode} mode\n   * @returns {ResumableMultiRegex}\n   */\n  function buildModeRegex(mode) {\n    const mm = new ResumableMultiRegex();\n\n    mode.contains.forEach(term => mm.addRule(term.begin, { rule: term, type: \"begin\" }));\n\n    if (mode.terminatorEnd) {\n      mm.addRule(mode.terminatorEnd, { type: \"end\" });\n    }\n    if (mode.illegal) {\n      mm.addRule(mode.illegal, { type: \"illegal\" });\n    }\n\n    return mm;\n  }\n\n  /** skip vs abort vs ignore\n   *\n   * @skip   - The mode is still entered and exited normally (and contains rules apply),\n   *           but all content is held and added to the parent buffer rather than being\n   *           output when the mode ends.  Mostly used with `sublanguage` to build up\n   *           a single large buffer than can be parsed by sublanguage.\n   *\n   *             - The mode begin ands ends normally.\n   *             - Content matched is added to the parent mode buffer.\n   *             - The parser cursor is moved forward normally.\n   *\n   * @abort  - A hack placeholder until we have ignore.  Aborts the mode (as if it\n   *           never matched) but DOES NOT continue to match subsequent `contains`\n   *           modes.  Abort is bad/suboptimal because it can result in modes\n   *           farther down not getting applied because an earlier rule eats the\n   *           content but then aborts.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is added to the mode buffer.\n   *             - The parser cursor is moved forward accordingly.\n   *\n   * @ignore - Ignores the mode (as if it never matched) and continues to match any\n   *           subsequent `contains` modes.  Ignore isn't technically possible with\n   *           the current parser implementation.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is ignored.\n   *             - The parser cursor is not moved forward.\n   */\n\n  /**\n   * Compiles an individual mode\n   *\n   * This can raise an error if the mode contains certain detectable known logic\n   * issues.\n   * @param {Mode} mode\n   * @param {CompiledMode | null} [parent]\n   * @returns {CompiledMode | never}\n   */\n  function compileMode(mode, parent) {\n    const cmode = /** @type CompiledMode */ (mode);\n    if (mode.isCompiled) return cmode;\n\n    [\n      // do this early so compiler extensions generally don't have to worry about\n      // the distinction between match/begin\n      compileMatch\n    ].forEach(ext => ext(mode, parent));\n\n    language.compilerExtensions.forEach(ext => ext(mode, parent));\n\n    // __beforeBegin is considered private API, internal use only\n    mode.__beforeBegin = null;\n\n    [\n      beginKeywords,\n      // do this later so compiler extensions that come earlier have access to the\n      // raw array if they wanted to perhaps manipulate it, etc.\n      compileIllegal,\n      // default to 1 relevance if not specified\n      compileRelevance\n    ].forEach(ext => ext(mode, parent));\n\n    mode.isCompiled = true;\n\n    let keywordPattern = null;\n    if (typeof mode.keywords === \"object\") {\n      keywordPattern = mode.keywords.$pattern;\n      delete mode.keywords.$pattern;\n    }\n\n    if (mode.keywords) {\n      mode.keywords = compileKeywords(mode.keywords, language.case_insensitive);\n    }\n\n    // both are not allowed\n    if (mode.lexemes && keywordPattern) {\n      throw new Error(\"ERR: Prefer `keywords.$pattern` to `mode.lexemes`, BOTH are not allowed. (see mode reference) \");\n    }\n\n    // `mode.lexemes` was the old standard before we added and now recommend\n    // using `keywords.$pattern` to pass the keyword pattern\n    keywordPattern = keywordPattern || mode.lexemes || /\\w+/;\n    cmode.keywordPatternRe = langRe(keywordPattern, true);\n\n    if (parent) {\n      if (!mode.begin) mode.begin = /\\B|\\b/;\n      cmode.beginRe = langRe(mode.begin);\n      if (mode.endSameAsBegin) mode.end = mode.begin;\n      if (!mode.end && !mode.endsWithParent) mode.end = /\\B|\\b/;\n      if (mode.end) cmode.endRe = langRe(mode.end);\n      cmode.terminatorEnd = source(mode.end) || '';\n      if (mode.endsWithParent && parent.terminatorEnd) {\n        cmode.terminatorEnd += (mode.end ? '|' : '') + parent.terminatorEnd;\n      }\n    }\n    if (mode.illegal) cmode.illegalRe = langRe(/** @type {RegExp | string} */ (mode.illegal));\n    if (!mode.contains) mode.contains = [];\n\n    mode.contains = [].concat(...mode.contains.map(function(c) {\n      return expandOrCloneMode(c === 'self' ? mode : c);\n    }));\n    mode.contains.forEach(function(c) { compileMode(/** @type Mode */ (c), cmode); });\n\n    if (mode.starts) {\n      compileMode(mode.starts, parent);\n    }\n\n    cmode.matcher = buildModeRegex(cmode);\n    return cmode;\n  }\n\n  if (!language.compilerExtensions) language.compilerExtensions = [];\n\n  // self is not valid at the top-level\n  if (language.contains && language.contains.includes('self')) {\n    throw new Error(\"ERR: contains `self` is not supported at the top-level of a language.  See documentation.\");\n  }\n\n  // we need a null object, which inherit will guarantee\n  language.classNameAliases = inherit(language.classNameAliases || {});\n\n  return compileMode(/** @type Mode */ (language));\n}\n\n/**\n * Determines if a mode has a dependency on it's parent or not\n *\n * If a mode does have a parent dependency then often we need to clone it if\n * it's used in multiple places so that each copy points to the correct parent,\n * where-as modes without a parent can often safely be re-used at the bottom of\n * a mode chain.\n *\n * @param {Mode | null} mode\n * @returns {boolean} - is there a dependency on the parent?\n * */\nfunction dependencyOnParent(mode) {\n  if (!mode) return false;\n\n  return mode.endsWithParent || dependencyOnParent(mode.starts);\n}\n\n/**\n * Expands a mode or clones it if necessary\n *\n * This is necessary for modes with parental dependenceis (see notes on\n * `dependencyOnParent`) and for nodes that have `variants` - which must then be\n * exploded into their own individual modes at compile time.\n *\n * @param {Mode} mode\n * @returns {Mode | Mode[]}\n * */\nfunction expandOrCloneMode(mode) {\n  if (mode.variants && !mode.cachedVariants) {\n    mode.cachedVariants = mode.variants.map(function(variant) {\n      return inherit(mode, { variants: null }, variant);\n    });\n  }\n\n  // EXPAND\n  // if we have variants then essentially \"replace\" the mode with the variants\n  // this happens in compileMode, where this function is called from\n  if (mode.cachedVariants) {\n    return mode.cachedVariants;\n  }\n\n  // CLONE\n  // if we have dependencies on parents then we need a unique\n  // instance of ourselves, so we can be reused with many\n  // different parents without issue\n  if (dependencyOnParent(mode)) {\n    return inherit(mode, { starts: mode.starts ? inherit(mode.starts) : null });\n  }\n\n  if (Object.isFrozen(mode)) {\n    return inherit(mode);\n  }\n\n  // no special dependency issues, just return ourselves\n  return mode;\n}\n\nvar version = \"10.7.3\";\n\n// @ts-nocheck\n\nfunction hasValueOrEmptyAttribute(value) {\n  return Boolean(value || value === \"\");\n}\n\nfunction BuildVuePlugin(hljs) {\n  const Component = {\n    props: [\"language\", \"code\", \"autodetect\"],\n    data: function() {\n      return {\n        detectedLanguage: \"\",\n        unknownLanguage: false\n      };\n    },\n    computed: {\n      className() {\n        if (this.unknownLanguage) return \"\";\n\n        return \"hljs \" + this.detectedLanguage;\n      },\n      highlighted() {\n        // no idea what language to use, return raw code\n        if (!this.autoDetect && !hljs.getLanguage(this.language)) {\n          console.warn(`The language \"${this.language}\" you specified could not be found.`);\n          this.unknownLanguage = true;\n          return escapeHTML(this.code);\n        }\n\n        let result = {};\n        if (this.autoDetect) {\n          result = hljs.highlightAuto(this.code);\n          this.detectedLanguage = result.language;\n        } else {\n          result = hljs.highlight(this.language, this.code, this.ignoreIllegals);\n          this.detectedLanguage = this.language;\n        }\n        return result.value;\n      },\n      autoDetect() {\n        return !this.language || hasValueOrEmptyAttribute(this.autodetect);\n      },\n      ignoreIllegals() {\n        return true;\n      }\n    },\n    // this avoids needing to use a whole Vue compilation pipeline just\n    // to build Highlight.js\n    render(createElement) {\n      return createElement(\"pre\", {}, [\n        createElement(\"code\", {\n          class: this.className,\n          domProps: { innerHTML: this.highlighted }\n        })\n      ]);\n    }\n    // template: `<pre><code :class=\"className\" v-html=\"highlighted\"></code></pre>`\n  };\n\n  const VuePlugin = {\n    install(Vue) {\n      Vue.component('highlightjs', Component);\n    }\n  };\n\n  return { Component, VuePlugin };\n}\n\n/* plugin itself */\n\n/** @type {HLJSPlugin} */\nconst mergeHTMLPlugin = {\n  \"after:highlightElement\": ({ el, result, text }) => {\n    const originalStream = nodeStream(el);\n    if (!originalStream.length) return;\n\n    const resultNode = document.createElement('div');\n    resultNode.innerHTML = result.value;\n    result.value = mergeStreams(originalStream, nodeStream(resultNode), text);\n  }\n};\n\n/* Stream merging support functions */\n\n/**\n * @typedef Event\n * @property {'start'|'stop'} event\n * @property {number} offset\n * @property {Node} node\n */\n\n/**\n * @param {Node} node\n */\nfunction tag(node) {\n  return node.nodeName.toLowerCase();\n}\n\n/**\n * @param {Node} node\n */\nfunction nodeStream(node) {\n  /** @type Event[] */\n  const result = [];\n  (function _nodeStream(node, offset) {\n    for (let child = node.firstChild; child; child = child.nextSibling) {\n      if (child.nodeType === 3) {\n        offset += child.nodeValue.length;\n      } else if (child.nodeType === 1) {\n        result.push({\n          event: 'start',\n          offset: offset,\n          node: child\n        });\n        offset = _nodeStream(child, offset);\n        // Prevent void elements from having an end tag that would actually\n        // double them in the output. There are more void elements in HTML\n        // but we list only those realistically expected in code display.\n        if (!tag(child).match(/br|hr|img|input/)) {\n          result.push({\n            event: 'stop',\n            offset: offset,\n            node: child\n          });\n        }\n      }\n    }\n    return offset;\n  })(node, 0);\n  return result;\n}\n\n/**\n * @param {any} original - the original stream\n * @param {any} highlighted - stream of the highlighted source\n * @param {string} value - the original source itself\n */\nfunction mergeStreams(original, highlighted, value) {\n  let processed = 0;\n  let result = '';\n  const nodeStack = [];\n\n  function selectStream() {\n    if (!original.length || !highlighted.length) {\n      return original.length ? original : highlighted;\n    }\n    if (original[0].offset !== highlighted[0].offset) {\n      return (original[0].offset < highlighted[0].offset) ? original : highlighted;\n    }\n\n    /*\n    To avoid starting the stream just before it should stop the order is\n    ensured that original always starts first and closes last:\n\n    if (event1 == 'start' && event2 == 'start')\n      return original;\n    if (event1 == 'start' && event2 == 'stop')\n      return highlighted;\n    if (event1 == 'stop' && event2 == 'start')\n      return original;\n    if (event1 == 'stop' && event2 == 'stop')\n      return highlighted;\n\n    ... which is collapsed to:\n    */\n    return highlighted[0].event === 'start' ? original : highlighted;\n  }\n\n  /**\n   * @param {Node} node\n   */\n  function open(node) {\n    /** @param {Attr} attr */\n    function attributeString(attr) {\n      return ' ' + attr.nodeName + '=\"' + escapeHTML(attr.value) + '\"';\n    }\n    // @ts-ignore\n    result += '<' + tag(node) + [].map.call(node.attributes, attributeString).join('') + '>';\n  }\n\n  /**\n   * @param {Node} node\n   */\n  function close(node) {\n    result += '</' + tag(node) + '>';\n  }\n\n  /**\n   * @param {Event} event\n   */\n  function render(event) {\n    (event.event === 'start' ? open : close)(event.node);\n  }\n\n  while (original.length || highlighted.length) {\n    let stream = selectStream();\n    result += escapeHTML(value.substring(processed, stream[0].offset));\n    processed = stream[0].offset;\n    if (stream === original) {\n      /*\n      On any opening or closing tag of the original markup we first close\n      the entire highlighted node stack, then render the original tag along\n      with all the following original tags at the same offset and then\n      reopen all the tags on the highlighted stack.\n      */\n      nodeStack.reverse().forEach(close);\n      do {\n        render(stream.splice(0, 1)[0]);\n        stream = selectStream();\n      } while (stream === original && stream.length && stream[0].offset === processed);\n      nodeStack.reverse().forEach(open);\n    } else {\n      if (stream[0].event === 'start') {\n        nodeStack.push(stream[0].node);\n      } else {\n        nodeStack.pop();\n      }\n      render(stream.splice(0, 1)[0]);\n    }\n  }\n  return result + escapeHTML(value.substr(processed));\n}\n\n/*\n\nFor the reasoning behind this please see:\nhttps://github.com/highlightjs/highlight.js/issues/2880#issuecomment-747275419\n\n*/\n\n/**\n * @type {Record<string, boolean>}\n */\nconst seenDeprecations = {};\n\n/**\n * @param {string} message\n */\nconst error = (message) => {\n  console.error(message);\n};\n\n/**\n * @param {string} message\n * @param {any} args\n */\nconst warn = (message, ...args) => {\n  console.log(`WARN: ${message}`, ...args);\n};\n\n/**\n * @param {string} version\n * @param {string} message\n */\nconst deprecated = (version, message) => {\n  if (seenDeprecations[`${version}/${message}`]) return;\n\n  console.log(`Deprecated as of ${version}. ${message}`);\n  seenDeprecations[`${version}/${message}`] = true;\n};\n\n/*\nSyntax highlighting with language autodetection.\nhttps://highlightjs.org/\n*/\n\nconst escape$1 = escapeHTML;\nconst inherit$1 = inherit;\nconst NO_MATCH = Symbol(\"nomatch\");\n\n/**\n * @param {any} hljs - object that is extended (legacy)\n * @returns {HLJSApi}\n */\nconst HLJS = function(hljs) {\n  // Global internal variables used within the highlight.js library.\n  /** @type {Record<string, Language>} */\n  const languages = Object.create(null);\n  /** @type {Record<string, string>} */\n  const aliases = Object.create(null);\n  /** @type {HLJSPlugin[]} */\n  const plugins = [];\n\n  // safe/production mode - swallows more errors, tries to keep running\n  // even if a single syntax or parse hits a fatal error\n  let SAFE_MODE = true;\n  const fixMarkupRe = /(^(<[^>]+>|\\t|)+|\\n)/gm;\n  const LANGUAGE_NOT_FOUND = \"Could not find the language '{}', did you forget to load/include a language module?\";\n  /** @type {Language} */\n  const PLAINTEXT_LANGUAGE = { disableAutodetect: true, name: 'Plain text', contains: [] };\n\n  // Global options used when within external APIs. This is modified when\n  // calling the `hljs.configure` function.\n  /** @type HLJSOptions */\n  let options = {\n    noHighlightRe: /^(no-?highlight)$/i,\n    languageDetectRe: /\\blang(?:uage)?-([\\w-]+)\\b/i,\n    classPrefix: 'hljs-',\n    tabReplace: null,\n    useBR: false,\n    languages: null,\n    // beta configuration options, subject to change, welcome to discuss\n    // https://github.com/highlightjs/highlight.js/issues/1086\n    __emitter: TokenTreeEmitter\n  };\n\n  /* Utility functions */\n\n  /**\n   * Tests a language name to see if highlighting should be skipped\n   * @param {string} languageName\n   */\n  function shouldNotHighlight(languageName) {\n    return options.noHighlightRe.test(languageName);\n  }\n\n  /**\n   * @param {HighlightedHTMLElement} block - the HTML element to determine language for\n   */\n  function blockLanguage(block) {\n    let classes = block.className + ' ';\n\n    classes += block.parentNode ? block.parentNode.className : '';\n\n    // language-* takes precedence over non-prefixed class names.\n    const match = options.languageDetectRe.exec(classes);\n    if (match) {\n      const language = getLanguage(match[1]);\n      if (!language) {\n        warn(LANGUAGE_NOT_FOUND.replace(\"{}\", match[1]));\n        warn(\"Falling back to no-highlight mode for this block.\", block);\n      }\n      return language ? match[1] : 'no-highlight';\n    }\n\n    return classes\n      .split(/\\s+/)\n      .find((_class) => shouldNotHighlight(_class) || getLanguage(_class));\n  }\n\n  /**\n   * Core highlighting function.\n   *\n   * OLD API\n   * highlight(lang, code, ignoreIllegals, continuation)\n   *\n   * NEW API\n   * highlight(code, {lang, ignoreIllegals})\n   *\n   * @param {string} codeOrlanguageName - the language to use for highlighting\n   * @param {string | HighlightOptions} optionsOrCode - the code to highlight\n   * @param {boolean} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   * @param {CompiledMode} [continuation] - current continuation mode, if any\n   *\n   * @returns {HighlightResult} Result - an object that represents the result\n   * @property {string} language - the language name\n   * @property {number} relevance - the relevance score\n   * @property {string} value - the highlighted HTML code\n   * @property {string} code - the original raw code\n   * @property {CompiledMode} top - top of the current mode stack\n   * @property {boolean} illegal - indicates whether any illegal matches were found\n  */\n  function highlight(codeOrlanguageName, optionsOrCode, ignoreIllegals, continuation) {\n    let code = \"\";\n    let languageName = \"\";\n    if (typeof optionsOrCode === \"object\") {\n      code = codeOrlanguageName;\n      ignoreIllegals = optionsOrCode.ignoreIllegals;\n      languageName = optionsOrCode.language;\n      // continuation not supported at all via the new API\n      // eslint-disable-next-line no-undefined\n      continuation = undefined;\n    } else {\n      // old API\n      deprecated(\"10.7.0\", \"highlight(lang, code, ...args) has been deprecated.\");\n      deprecated(\"10.7.0\", \"Please use highlight(code, options) instead.\\nhttps://github.com/highlightjs/highlight.js/issues/2277\");\n      languageName = codeOrlanguageName;\n      code = optionsOrCode;\n    }\n\n    /** @type {BeforeHighlightContext} */\n    const context = {\n      code,\n      language: languageName\n    };\n    // the plugin can change the desired language or the code to be highlighted\n    // just be changing the object it was passed\n    fire(\"before:highlight\", context);\n\n    // a before plugin can usurp the result completely by providing it's own\n    // in which case we don't even need to call highlight\n    const result = context.result\n      ? context.result\n      : _highlight(context.language, context.code, ignoreIllegals, continuation);\n\n    result.code = context.code;\n    // the plugin can change anything in result to suite it\n    fire(\"after:highlight\", result);\n\n    return result;\n  }\n\n  /**\n   * private highlight that's used internally and does not fire callbacks\n   *\n   * @param {string} languageName - the language to use for highlighting\n   * @param {string} codeToHighlight - the code to highlight\n   * @param {boolean?} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   * @param {CompiledMode?} [continuation] - current continuation mode, if any\n   * @returns {HighlightResult} - result of the highlight operation\n  */\n  function _highlight(languageName, codeToHighlight, ignoreIllegals, continuation) {\n    /**\n     * Return keyword data if a match is a keyword\n     * @param {CompiledMode} mode - current mode\n     * @param {RegExpMatchArray} match - regexp match data\n     * @returns {KeywordData | false}\n     */\n    function keywordData(mode, match) {\n      const matchText = language.case_insensitive ? match[0].toLowerCase() : match[0];\n      return Object.prototype.hasOwnProperty.call(mode.keywords, matchText) && mode.keywords[matchText];\n    }\n\n    function processKeywords() {\n      if (!top.keywords) {\n        emitter.addText(modeBuffer);\n        return;\n      }\n\n      let lastIndex = 0;\n      top.keywordPatternRe.lastIndex = 0;\n      let match = top.keywordPatternRe.exec(modeBuffer);\n      let buf = \"\";\n\n      while (match) {\n        buf += modeBuffer.substring(lastIndex, match.index);\n        const data = keywordData(top, match);\n        if (data) {\n          const [kind, keywordRelevance] = data;\n          emitter.addText(buf);\n          buf = \"\";\n\n          relevance += keywordRelevance;\n          if (kind.startsWith(\"_\")) {\n            // _ implied for relevance only, do not highlight\n            // by applying a class name\n            buf += match[0];\n          } else {\n            const cssClass = language.classNameAliases[kind] || kind;\n            emitter.addKeyword(match[0], cssClass);\n          }\n        } else {\n          buf += match[0];\n        }\n        lastIndex = top.keywordPatternRe.lastIndex;\n        match = top.keywordPatternRe.exec(modeBuffer);\n      }\n      buf += modeBuffer.substr(lastIndex);\n      emitter.addText(buf);\n    }\n\n    function processSubLanguage() {\n      if (modeBuffer === \"\") return;\n      /** @type HighlightResult */\n      let result = null;\n\n      if (typeof top.subLanguage === 'string') {\n        if (!languages[top.subLanguage]) {\n          emitter.addText(modeBuffer);\n          return;\n        }\n        result = _highlight(top.subLanguage, modeBuffer, true, continuations[top.subLanguage]);\n        continuations[top.subLanguage] = /** @type {CompiledMode} */ (result.top);\n      } else {\n        result = highlightAuto(modeBuffer, top.subLanguage.length ? top.subLanguage : null);\n      }\n\n      // Counting embedded language score towards the host language may be disabled\n      // with zeroing the containing mode relevance. Use case in point is Markdown that\n      // allows XML everywhere and makes every XML snippet to have a much larger Markdown\n      // score.\n      if (top.relevance > 0) {\n        relevance += result.relevance;\n      }\n      emitter.addSublanguage(result.emitter, result.language);\n    }\n\n    function processBuffer() {\n      if (top.subLanguage != null) {\n        processSubLanguage();\n      } else {\n        processKeywords();\n      }\n      modeBuffer = '';\n    }\n\n    /**\n     * @param {Mode} mode - new mode to start\n     */\n    function startNewMode(mode) {\n      if (mode.className) {\n        emitter.openNode(language.classNameAliases[mode.className] || mode.className);\n      }\n      top = Object.create(mode, { parent: { value: top } });\n      return top;\n    }\n\n    /**\n     * @param {CompiledMode } mode - the mode to potentially end\n     * @param {RegExpMatchArray} match - the latest match\n     * @param {string} matchPlusRemainder - match plus remainder of content\n     * @returns {CompiledMode | void} - the next mode, or if void continue on in current mode\n     */\n    function endOfMode(mode, match, matchPlusRemainder) {\n      let matched = startsWith(mode.endRe, matchPlusRemainder);\n\n      if (matched) {\n        if (mode[\"on:end\"]) {\n          const resp = new Response(mode);\n          mode[\"on:end\"](match, resp);\n          if (resp.isMatchIgnored) matched = false;\n        }\n\n        if (matched) {\n          while (mode.endsParent && mode.parent) {\n            mode = mode.parent;\n          }\n          return mode;\n        }\n      }\n      // even if on:end fires an `ignore` it's still possible\n      // that we might trigger the end node because of a parent mode\n      if (mode.endsWithParent) {\n        return endOfMode(mode.parent, match, matchPlusRemainder);\n      }\n    }\n\n    /**\n     * Handle matching but then ignoring a sequence of text\n     *\n     * @param {string} lexeme - string containing full match text\n     */\n    function doIgnore(lexeme) {\n      if (top.matcher.regexIndex === 0) {\n        // no more regexs to potentially match here, so we move the cursor forward one\n        // space\n        modeBuffer += lexeme[0];\n        return 1;\n      } else {\n        // no need to move the cursor, we still have additional regexes to try and\n        // match at this very spot\n        resumeScanAtSamePosition = true;\n        return 0;\n      }\n    }\n\n    /**\n     * Handle the start of a new potential mode match\n     *\n     * @param {EnhancedMatch} match - the current match\n     * @returns {number} how far to advance the parse cursor\n     */\n    function doBeginMatch(match) {\n      const lexeme = match[0];\n      const newMode = match.rule;\n\n      const resp = new Response(newMode);\n      // first internal before callbacks, then the public ones\n      const beforeCallbacks = [newMode.__beforeBegin, newMode[\"on:begin\"]];\n      for (const cb of beforeCallbacks) {\n        if (!cb) continue;\n        cb(match, resp);\n        if (resp.isMatchIgnored) return doIgnore(lexeme);\n      }\n\n      if (newMode && newMode.endSameAsBegin) {\n        newMode.endRe = escape(lexeme);\n      }\n\n      if (newMode.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (newMode.excludeBegin) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (!newMode.returnBegin && !newMode.excludeBegin) {\n          modeBuffer = lexeme;\n        }\n      }\n      startNewMode(newMode);\n      // if (mode[\"after:begin\"]) {\n      //   let resp = new Response(mode);\n      //   mode[\"after:begin\"](match, resp);\n      // }\n      return newMode.returnBegin ? 0 : lexeme.length;\n    }\n\n    /**\n     * Handle the potential end of mode\n     *\n     * @param {RegExpMatchArray} match - the current match\n     */\n    function doEndMatch(match) {\n      const lexeme = match[0];\n      const matchPlusRemainder = codeToHighlight.substr(match.index);\n\n      const endMode = endOfMode(top, match, matchPlusRemainder);\n      if (!endMode) { return NO_MATCH; }\n\n      const origin = top;\n      if (origin.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (!(origin.returnEnd || origin.excludeEnd)) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (origin.excludeEnd) {\n          modeBuffer = lexeme;\n        }\n      }\n      do {\n        if (top.className) {\n          emitter.closeNode();\n        }\n        if (!top.skip && !top.subLanguage) {\n          relevance += top.relevance;\n        }\n        top = top.parent;\n      } while (top !== endMode.parent);\n      if (endMode.starts) {\n        if (endMode.endSameAsBegin) {\n          endMode.starts.endRe = endMode.endRe;\n        }\n        startNewMode(endMode.starts);\n      }\n      return origin.returnEnd ? 0 : lexeme.length;\n    }\n\n    function processContinuations() {\n      const list = [];\n      for (let current = top; current !== language; current = current.parent) {\n        if (current.className) {\n          list.unshift(current.className);\n        }\n      }\n      list.forEach(item => emitter.openNode(item));\n    }\n\n    /** @type {{type?: MatchType, index?: number, rule?: Mode}}} */\n    let lastMatch = {};\n\n    /**\n     *  Process an individual match\n     *\n     * @param {string} textBeforeMatch - text preceeding the match (since the last match)\n     * @param {EnhancedMatch} [match] - the match itself\n     */\n    function processLexeme(textBeforeMatch, match) {\n      const lexeme = match && match[0];\n\n      // add non-matched text to the current mode buffer\n      modeBuffer += textBeforeMatch;\n\n      if (lexeme == null) {\n        processBuffer();\n        return 0;\n      }\n\n      // we've found a 0 width match and we're stuck, so we need to advance\n      // this happens when we have badly behaved rules that have optional matchers to the degree that\n      // sometimes they can end up matching nothing at all\n      // Ref: https://github.com/highlightjs/highlight.js/issues/2140\n      if (lastMatch.type === \"begin\" && match.type === \"end\" && lastMatch.index === match.index && lexeme === \"\") {\n        // spit the \"skipped\" character that our regex choked on back into the output sequence\n        modeBuffer += codeToHighlight.slice(match.index, match.index + 1);\n        if (!SAFE_MODE) {\n          /** @type {AnnotatedError} */\n          const err = new Error('0 width match regex');\n          err.languageName = languageName;\n          err.badRule = lastMatch.rule;\n          throw err;\n        }\n        return 1;\n      }\n      lastMatch = match;\n\n      if (match.type === \"begin\") {\n        return doBeginMatch(match);\n      } else if (match.type === \"illegal\" && !ignoreIllegals) {\n        // illegal match, we do not continue processing\n        /** @type {AnnotatedError} */\n        const err = new Error('Illegal lexeme \"' + lexeme + '\" for mode \"' + (top.className || '<unnamed>') + '\"');\n        err.mode = top;\n        throw err;\n      } else if (match.type === \"end\") {\n        const processed = doEndMatch(match);\n        if (processed !== NO_MATCH) {\n          return processed;\n        }\n      }\n\n      // edge case for when illegal matches $ (end of line) which is technically\n      // a 0 width match but not a begin/end match so it's not caught by the\n      // first handler (when ignoreIllegals is true)\n      if (match.type === \"illegal\" && lexeme === \"\") {\n        // advance so we aren't stuck in an infinite loop\n        return 1;\n      }\n\n      // infinite loops are BAD, this is a last ditch catch all. if we have a\n      // decent number of iterations yet our index (cursor position in our\n      // parsing) still 3x behind our index then something is very wrong\n      // so we bail\n      if (iterations > 100000 && iterations > match.index * 3) {\n        const err = new Error('potential infinite loop, way more iterations than matches');\n        throw err;\n      }\n\n      /*\n      Why might be find ourselves here?  Only one occasion now.  An end match that was\n      triggered but could not be completed.  When might this happen?  When an `endSameasBegin`\n      rule sets the end rule to a specific match.  Since the overall mode termination rule that's\n      being used to scan the text isn't recompiled that means that any match that LOOKS like\n      the end (but is not, because it is not an exact match to the beginning) will\n      end up here.  A definite end match, but when `doEndMatch` tries to \"reapply\"\n      the end rule and fails to match, we wind up here, and just silently ignore the end.\n\n      This causes no real harm other than stopping a few times too many.\n      */\n\n      modeBuffer += lexeme;\n      return lexeme.length;\n    }\n\n    const language = getLanguage(languageName);\n    if (!language) {\n      error(LANGUAGE_NOT_FOUND.replace(\"{}\", languageName));\n      throw new Error('Unknown language: \"' + languageName + '\"');\n    }\n\n    const md = compileLanguage(language, { plugins });\n    let result = '';\n    /** @type {CompiledMode} */\n    let top = continuation || md;\n    /** @type Record<string,CompiledMode> */\n    const continuations = {}; // keep continuations for sub-languages\n    const emitter = new options.__emitter(options);\n    processContinuations();\n    let modeBuffer = '';\n    let relevance = 0;\n    let index = 0;\n    let iterations = 0;\n    let resumeScanAtSamePosition = false;\n\n    try {\n      top.matcher.considerAll();\n\n      for (;;) {\n        iterations++;\n        if (resumeScanAtSamePosition) {\n          // only regexes not matched previously will now be\n          // considered for a potential match\n          resumeScanAtSamePosition = false;\n        } else {\n          top.matcher.considerAll();\n        }\n        top.matcher.lastIndex = index;\n\n        const match = top.matcher.exec(codeToHighlight);\n        // console.log(\"match\", match[0], match.rule && match.rule.begin)\n\n        if (!match) break;\n\n        const beforeMatch = codeToHighlight.substring(index, match.index);\n        const processedCount = processLexeme(beforeMatch, match);\n        index = match.index + processedCount;\n      }\n      processLexeme(codeToHighlight.substr(index));\n      emitter.closeAllNodes();\n      emitter.finalize();\n      result = emitter.toHTML();\n\n      return {\n        // avoid possible breakage with v10 clients expecting\n        // this to always be an integer\n        relevance: Math.floor(relevance),\n        value: result,\n        language: languageName,\n        illegal: false,\n        emitter: emitter,\n        top: top\n      };\n    } catch (err) {\n      if (err.message && err.message.includes('Illegal')) {\n        return {\n          illegal: true,\n          illegalBy: {\n            msg: err.message,\n            context: codeToHighlight.slice(index - 100, index + 100),\n            mode: err.mode\n          },\n          sofar: result,\n          relevance: 0,\n          value: escape$1(codeToHighlight),\n          emitter: emitter\n        };\n      } else if (SAFE_MODE) {\n        return {\n          illegal: false,\n          relevance: 0,\n          value: escape$1(codeToHighlight),\n          emitter: emitter,\n          language: languageName,\n          top: top,\n          errorRaised: err\n        };\n      } else {\n        throw err;\n      }\n    }\n  }\n\n  /**\n   * returns a valid highlight result, without actually doing any actual work,\n   * auto highlight starts with this and it's possible for small snippets that\n   * auto-detection may not find a better match\n   * @param {string} code\n   * @returns {HighlightResult}\n   */\n  function justTextHighlightResult(code) {\n    const result = {\n      relevance: 0,\n      emitter: new options.__emitter(options),\n      value: escape$1(code),\n      illegal: false,\n      top: PLAINTEXT_LANGUAGE\n    };\n    result.emitter.addText(code);\n    return result;\n  }\n\n  /**\n  Highlighting with language detection. Accepts a string with the code to\n  highlight. Returns an object with the following properties:\n\n  - language (detected language)\n  - relevance (int)\n  - value (an HTML string with highlighting markup)\n  - second_best (object with the same structure for second-best heuristically\n    detected language, may be absent)\n\n    @param {string} code\n    @param {Array<string>} [languageSubset]\n    @returns {AutoHighlightResult}\n  */\n  function highlightAuto(code, languageSubset) {\n    languageSubset = languageSubset || options.languages || Object.keys(languages);\n    const plaintext = justTextHighlightResult(code);\n\n    const results = languageSubset.filter(getLanguage).filter(autoDetection).map(name =>\n      _highlight(name, code, false)\n    );\n    results.unshift(plaintext); // plaintext is always an option\n\n    const sorted = results.sort((a, b) => {\n      // sort base on relevance\n      if (a.relevance !== b.relevance) return b.relevance - a.relevance;\n\n      // always award the tie to the base language\n      // ie if C++ and Arduino are tied, it's more likely to be C++\n      if (a.language && b.language) {\n        if (getLanguage(a.language).supersetOf === b.language) {\n          return 1;\n        } else if (getLanguage(b.language).supersetOf === a.language) {\n          return -1;\n        }\n      }\n\n      // otherwise say they are equal, which has the effect of sorting on\n      // relevance while preserving the original ordering - which is how ties\n      // have historically been settled, ie the language that comes first always\n      // wins in the case of a tie\n      return 0;\n    });\n\n    const [best, secondBest] = sorted;\n\n    /** @type {AutoHighlightResult} */\n    const result = best;\n    result.second_best = secondBest;\n\n    return result;\n  }\n\n  /**\n  Post-processing of the highlighted markup:\n\n  - replace TABs with something more useful\n  - replace real line-breaks with '<br>' for non-pre containers\n\n    @param {string} html\n    @returns {string}\n  */\n  function fixMarkup(html) {\n    if (!(options.tabReplace || options.useBR)) {\n      return html;\n    }\n\n    return html.replace(fixMarkupRe, match => {\n      if (match === '\\n') {\n        return options.useBR ? '<br>' : match;\n      } else if (options.tabReplace) {\n        return match.replace(/\\t/g, options.tabReplace);\n      }\n      return match;\n    });\n  }\n\n  /**\n   * Builds new class name for block given the language name\n   *\n   * @param {HTMLElement} element\n   * @param {string} [currentLang]\n   * @param {string} [resultLang]\n   */\n  function updateClassName(element, currentLang, resultLang) {\n    const language = currentLang ? aliases[currentLang] : resultLang;\n\n    element.classList.add(\"hljs\");\n    if (language) element.classList.add(language);\n  }\n\n  /** @type {HLJSPlugin} */\n  const brPlugin = {\n    \"before:highlightElement\": ({ el }) => {\n      if (options.useBR) {\n        el.innerHTML = el.innerHTML.replace(/\\n/g, '').replace(/<br[ /]*>/g, '\\n');\n      }\n    },\n    \"after:highlightElement\": ({ result }) => {\n      if (options.useBR) {\n        result.value = result.value.replace(/\\n/g, \"<br>\");\n      }\n    }\n  };\n\n  const TAB_REPLACE_RE = /^(<[^>]+>|\\t)+/gm;\n  /** @type {HLJSPlugin} */\n  const tabReplacePlugin = {\n    \"after:highlightElement\": ({ result }) => {\n      if (options.tabReplace) {\n        result.value = result.value.replace(TAB_REPLACE_RE, (m) =>\n          m.replace(/\\t/g, options.tabReplace)\n        );\n      }\n    }\n  };\n\n  /**\n   * Applies highlighting to a DOM node containing code. Accepts a DOM node and\n   * two optional parameters for fixMarkup.\n   *\n   * @param {HighlightedHTMLElement} element - the HTML element to highlight\n  */\n  function highlightElement(element) {\n    /** @type HTMLElement */\n    let node = null;\n    const language = blockLanguage(element);\n\n    if (shouldNotHighlight(language)) return;\n\n    // support for v10 API\n    fire(\"before:highlightElement\",\n      { el: element, language: language });\n\n    node = element;\n    const text = node.textContent;\n    const result = language ? highlight(text, { language, ignoreIllegals: true }) : highlightAuto(text);\n\n    // support for v10 API\n    fire(\"after:highlightElement\", { el: element, result, text });\n\n    element.innerHTML = result.value;\n    updateClassName(element, language, result.language);\n    element.result = {\n      language: result.language,\n      // TODO: remove with version 11.0\n      re: result.relevance,\n      relavance: result.relevance\n    };\n    if (result.second_best) {\n      element.second_best = {\n        language: result.second_best.language,\n        // TODO: remove with version 11.0\n        re: result.second_best.relevance,\n        relavance: result.second_best.relevance\n      };\n    }\n  }\n\n  /**\n   * Updates highlight.js global options with the passed options\n   *\n   * @param {Partial<HLJSOptions>} userOptions\n   */\n  function configure(userOptions) {\n    if (userOptions.useBR) {\n      deprecated(\"10.3.0\", \"'useBR' will be removed entirely in v11.0\");\n      deprecated(\"10.3.0\", \"Please see https://github.com/highlightjs/highlight.js/issues/2559\");\n    }\n    options = inherit$1(options, userOptions);\n  }\n\n  /**\n   * Highlights to all <pre><code> blocks on a page\n   *\n   * @type {Function & {called?: boolean}}\n   */\n  // TODO: remove v12, deprecated\n  const initHighlighting = () => {\n    if (initHighlighting.called) return;\n    initHighlighting.called = true;\n\n    deprecated(\"10.6.0\", \"initHighlighting() is deprecated.  Use highlightAll() instead.\");\n\n    const blocks = document.querySelectorAll('pre code');\n    blocks.forEach(highlightElement);\n  };\n\n  // Higlights all when DOMContentLoaded fires\n  // TODO: remove v12, deprecated\n  function initHighlightingOnLoad() {\n    deprecated(\"10.6.0\", \"initHighlightingOnLoad() is deprecated.  Use highlightAll() instead.\");\n    wantsHighlight = true;\n  }\n\n  let wantsHighlight = false;\n\n  /**\n   * auto-highlights all pre>code elements on the page\n   */\n  function highlightAll() {\n    // if we are called too early in the loading process\n    if (document.readyState === \"loading\") {\n      wantsHighlight = true;\n      return;\n    }\n\n    const blocks = document.querySelectorAll('pre code');\n    blocks.forEach(highlightElement);\n  }\n\n  function boot() {\n    // if a highlight was requested before DOM was loaded, do now\n    if (wantsHighlight) highlightAll();\n  }\n\n  // make sure we are in the browser environment\n  if (typeof window !== 'undefined' && window.addEventListener) {\n    window.addEventListener('DOMContentLoaded', boot, false);\n  }\n\n  /**\n   * Register a language grammar module\n   *\n   * @param {string} languageName\n   * @param {LanguageFn} languageDefinition\n   */\n  function registerLanguage(languageName, languageDefinition) {\n    let lang = null;\n    try {\n      lang = languageDefinition(hljs);\n    } catch (error$1) {\n      error(\"Language definition for '{}' could not be registered.\".replace(\"{}\", languageName));\n      // hard or soft error\n      if (!SAFE_MODE) { throw error$1; } else { error(error$1); }\n      // languages that have serious errors are replaced with essentially a\n      // \"plaintext\" stand-in so that the code blocks will still get normal\n      // css classes applied to them - and one bad language won't break the\n      // entire highlighter\n      lang = PLAINTEXT_LANGUAGE;\n    }\n    // give it a temporary name if it doesn't have one in the meta-data\n    if (!lang.name) lang.name = languageName;\n    languages[languageName] = lang;\n    lang.rawDefinition = languageDefinition.bind(null, hljs);\n\n    if (lang.aliases) {\n      registerAliases(lang.aliases, { languageName });\n    }\n  }\n\n  /**\n   * Remove a language grammar module\n   *\n   * @param {string} languageName\n   */\n  function unregisterLanguage(languageName) {\n    delete languages[languageName];\n    for (const alias of Object.keys(aliases)) {\n      if (aliases[alias] === languageName) {\n        delete aliases[alias];\n      }\n    }\n  }\n\n  /**\n   * @returns {string[]} List of language internal names\n   */\n  function listLanguages() {\n    return Object.keys(languages);\n  }\n\n  /**\n    intended usage: When one language truly requires another\n\n    Unlike `getLanguage`, this will throw when the requested language\n    is not available.\n\n    @param {string} name - name of the language to fetch/require\n    @returns {Language | never}\n  */\n  function requireLanguage(name) {\n    deprecated(\"10.4.0\", \"requireLanguage will be removed entirely in v11.\");\n    deprecated(\"10.4.0\", \"Please see https://github.com/highlightjs/highlight.js/pull/2844\");\n\n    const lang = getLanguage(name);\n    if (lang) { return lang; }\n\n    const err = new Error('The \\'{}\\' language is required, but not loaded.'.replace('{}', name));\n    throw err;\n  }\n\n  /**\n   * @param {string} name - name of the language to retrieve\n   * @returns {Language | undefined}\n   */\n  function getLanguage(name) {\n    name = (name || '').toLowerCase();\n    return languages[name] || languages[aliases[name]];\n  }\n\n  /**\n   *\n   * @param {string|string[]} aliasList - single alias or list of aliases\n   * @param {{languageName: string}} opts\n   */\n  function registerAliases(aliasList, { languageName }) {\n    if (typeof aliasList === 'string') {\n      aliasList = [aliasList];\n    }\n    aliasList.forEach(alias => { aliases[alias.toLowerCase()] = languageName; });\n  }\n\n  /**\n   * Determines if a given language has auto-detection enabled\n   * @param {string} name - name of the language\n   */\n  function autoDetection(name) {\n    const lang = getLanguage(name);\n    return lang && !lang.disableAutodetect;\n  }\n\n  /**\n   * Upgrades the old highlightBlock plugins to the new\n   * highlightElement API\n   * @param {HLJSPlugin} plugin\n   */\n  function upgradePluginAPI(plugin) {\n    // TODO: remove with v12\n    if (plugin[\"before:highlightBlock\"] && !plugin[\"before:highlightElement\"]) {\n      plugin[\"before:highlightElement\"] = (data) => {\n        plugin[\"before:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n    if (plugin[\"after:highlightBlock\"] && !plugin[\"after:highlightElement\"]) {\n      plugin[\"after:highlightElement\"] = (data) => {\n        plugin[\"after:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function addPlugin(plugin) {\n    upgradePluginAPI(plugin);\n    plugins.push(plugin);\n  }\n\n  /**\n   *\n   * @param {PluginEvent} event\n   * @param {any} args\n   */\n  function fire(event, args) {\n    const cb = event;\n    plugins.forEach(function(plugin) {\n      if (plugin[cb]) {\n        plugin[cb](args);\n      }\n    });\n  }\n\n  /**\n  Note: fixMarkup is deprecated and will be removed entirely in v11\n\n  @param {string} arg\n  @returns {string}\n  */\n  function deprecateFixMarkup(arg) {\n    deprecated(\"10.2.0\", \"fixMarkup will be removed entirely in v11.0\");\n    deprecated(\"10.2.0\", \"Please see https://github.com/highlightjs/highlight.js/issues/2534\");\n\n    return fixMarkup(arg);\n  }\n\n  /**\n   *\n   * @param {HighlightedHTMLElement} el\n   */\n  function deprecateHighlightBlock(el) {\n    deprecated(\"10.7.0\", \"highlightBlock will be removed entirely in v12.0\");\n    deprecated(\"10.7.0\", \"Please use highlightElement now.\");\n\n    return highlightElement(el);\n  }\n\n  /* Interface definition */\n  Object.assign(hljs, {\n    highlight,\n    highlightAuto,\n    highlightAll,\n    fixMarkup: deprecateFixMarkup,\n    highlightElement,\n    // TODO: Remove with v12 API\n    highlightBlock: deprecateHighlightBlock,\n    configure,\n    initHighlighting,\n    initHighlightingOnLoad,\n    registerLanguage,\n    unregisterLanguage,\n    listLanguages,\n    getLanguage,\n    registerAliases,\n    requireLanguage,\n    autoDetection,\n    inherit: inherit$1,\n    addPlugin,\n    // plugins for frameworks\n    vuePlugin: BuildVuePlugin(hljs).VuePlugin\n  });\n\n  hljs.debugMode = function() { SAFE_MODE = false; };\n  hljs.safeMode = function() { SAFE_MODE = true; };\n  hljs.versionString = version;\n\n  for (const key in MODES) {\n    // @ts-ignore\n    if (typeof MODES[key] === \"object\") {\n      // @ts-ignore\n      deepFreezeEs6(MODES[key]);\n    }\n  }\n\n  // merge all the modes/regexs into our main object\n  Object.assign(hljs, MODES);\n\n  // built-in plugins, likely to be moved out of core in the future\n  hljs.addPlugin(brPlugin); // slated to be removed in v11\n  hljs.addPlugin(mergeHTMLPlugin);\n  hljs.addPlugin(tabReplacePlugin);\n  return hljs;\n};\n\n// export an \"instance\" of the highlighter\nvar highlight = HLJS({});\n\nmodule.exports = highlight;\n"], "names": [], "mappings": "AAAA,SAAS,WAAW,GAAG;IACnB,IAAI,eAAe,KAAK;QACpB,IAAI,KAAK,GAAG,IAAI,MAAM,GAAG,IAAI,GAAG,GAAG;YAC/B,MAAM,IAAI,MAAM;QACpB;IACJ,OAAO,IAAI,eAAe,KAAK;QAC3B,IAAI,GAAG,GAAG,IAAI,KAAK,GAAG,IAAI,MAAM,GAAG;YAC/B,MAAM,IAAI,MAAM;QACpB;IACJ;IAEA,cAAc;IACd,OAAO,MAAM,CAAC;IAEd,OAAO,mBAAmB,CAAC,KAAK,OAAO,CAAC,SAAU,IAAI;QAClD,IAAI,OAAO,GAAG,CAAC,KAAK;QAEpB,iCAAiC;QACjC,IAAI,OAAO,QAAQ,YAAY,CAAC,OAAO,QAAQ,CAAC,OAAO;YACnD,WAAW;QACf;IACJ;IAEA,OAAO;AACX;AAEA,IAAI,gBAAgB;AACpB,IAAI,WAAW;AACf,cAAc,OAAO,GAAG;AAExB,iCAAiC,GACjC,MAAM;IACJ;;GAEC,GACD,YAAY,IAAI,CAAE;QAChB,wCAAwC;QACxC,IAAI,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,GAAG,CAAC;QAE1C,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;QACrB,IAAI,CAAC,cAAc,GAAG;IACxB;IAEA,cAAc;QACZ,IAAI,CAAC,cAAc,GAAG;IACxB;AACF;AAEA;;;CAGC,GACD,SAAS,WAAW,KAAK;IACvB,OAAO,MACJ,OAAO,CAAC,MAAM,SACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,UACd,OAAO,CAAC,MAAM;AACnB;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,QAAQ,EAAE,GAAG,OAAO;IACnC,6BAA6B,GAC7B,MAAM,SAAS,OAAO,MAAM,CAAC;IAE7B,IAAK,MAAM,OAAO,SAAU;QAC1B,MAAM,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI;IAC7B;IACA,QAAQ,OAAO,CAAC,SAAS,GAAG;QAC1B,IAAK,MAAM,OAAO,IAAK;YACrB,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QACxB;IACF;IACA,OAAyB;AAC3B;AAEA;;;;;;CAMC,GAED,2DAA2D,GAC3D,kDAAkD,GAClD,IAAI,GAEJ,MAAM,aAAa;AAEnB;;;sBAGsB,GACtB,MAAM,oBAAoB,CAAC;IACzB,OAAO,CAAC,CAAC,KAAK,IAAI;AACpB;AAEA,qBAAqB,GACrB,MAAM;IACJ;;;;;GAKC,GACD,YAAY,SAAS,EAAE,OAAO,CAAE;QAC9B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,WAAW,GAAG,QAAQ,WAAW;QACtC,UAAU,IAAI,CAAC,IAAI;IACrB;IAEA;;;0BAGwB,GACxB,QAAQ,IAAI,EAAE;QACZ,IAAI,CAAC,MAAM,IAAI,WAAW;IAC5B;IAEA;;;wBAGsB,GACtB,SAAS,IAAI,EAAE;QACb,IAAI,CAAC,kBAAkB,OAAO;QAE9B,IAAI,YAAY,KAAK,IAAI;QACzB,IAAI,CAAC,KAAK,WAAW,EAAE;YACrB,YAAY,GAAG,IAAI,CAAC,WAAW,GAAG,WAAW;QAC/C;QACA,IAAI,CAAC,IAAI,CAAC;IACZ;IAEA;;;wBAGsB,GACtB,UAAU,IAAI,EAAE;QACd,IAAI,CAAC,kBAAkB,OAAO;QAE9B,IAAI,CAAC,MAAM,IAAI;IACjB;IAEA;;EAEA,GACA,QAAQ;QACN,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,UAAU;IAEV;;;+BAG6B,GAC7B,KAAK,SAAS,EAAE;QACd,IAAI,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,UAAU,EAAE,CAAC;IAC9C;AACF;AAEA,sFAAsF,GACtF,kFAAkF,GAClF,KAAK,GAEL,MAAM;IACJ,aAAc;QACZ,mBAAmB,GACnB,IAAI,CAAC,QAAQ,GAAG;YAAE,UAAU,EAAE;QAAC;QAC/B,IAAI,CAAC,KAAK,GAAG;YAAC,IAAI,CAAC,QAAQ;SAAC;IAC9B;IAEA,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAC1C;IAEA,IAAI,OAAO;QAAE,OAAO,IAAI,CAAC,QAAQ;IAAE;IAEnC,uBAAuB,GACvB,IAAI,IAAI,EAAE;QACR,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;IACzB;IAEA,yBAAyB,GACzB,SAAS,IAAI,EAAE;QACb,eAAe,GACf,MAAM,OAAO;YAAE;YAAM,UAAU,EAAE;QAAC;QAClC,IAAI,CAAC,GAAG,CAAC;QACT,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IAClB;IAEA,YAAY;QACV,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YACzB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG;QACvB;QACA,wCAAwC;QACxC,OAAO;IACT;IAEA,gBAAgB;QACd,MAAO,IAAI,CAAC,SAAS;IACvB;IAEA,SAAS;QACP,OAAO,KAAK,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM;IAC7C;IAEA;;;GAGC,GACD,KAAK,OAAO,EAAE;QACZ,gBAAgB;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,QAAQ;IACpD,aAAa;IACb,kDAAkD;IACpD;IAEA;;;GAGC,GACD,OAAO,MAAM,OAAO,EAAE,IAAI,EAAE;QAC1B,IAAI,OAAO,SAAS,UAAU;YAC5B,QAAQ,OAAO,CAAC;QAClB,OAAO,IAAI,KAAK,QAAQ,EAAE;YACxB,QAAQ,QAAQ,CAAC;YACjB,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAU,IAAI,CAAC,KAAK,CAAC,SAAS;YACrD,QAAQ,SAAS,CAAC;QACpB;QACA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,UAAU,IAAI,EAAE;QACrB,IAAI,OAAO,SAAS,UAAU;QAC9B,IAAI,CAAC,KAAK,QAAQ,EAAE;QAEpB,IAAI,KAAK,QAAQ,CAAC,KAAK,CAAC,CAAA,KAAM,OAAO,OAAO,WAAW;YACrD,sCAAsC;YACtC,wBAAwB;YACxB,KAAK,QAAQ,GAAG;gBAAC,KAAK,QAAQ,CAAC,IAAI,CAAC;aAAI;QAC1C,OAAO;YACL,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACrB,UAAU,SAAS,CAAC;YACtB;QACF;IACF;AACF;AAEA;;;;;;;;;;;;;;;AAeA,GAEA;;CAEC,GACD,MAAM,yBAAyB;IAC7B;;GAEC,GACD,YAAY,OAAO,CAAE;QACnB,KAAK;QACL,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA;;;GAGC,GACD,WAAW,IAAI,EAAE,IAAI,EAAE;QACrB,IAAI,SAAS,IAAI;YAAE;QAAQ;QAE3B,IAAI,CAAC,QAAQ,CAAC;QACd,IAAI,CAAC,OAAO,CAAC;QACb,IAAI,CAAC,SAAS;IAChB;IAEA;;GAEC,GACD,QAAQ,IAAI,EAAE;QACZ,IAAI,SAAS,IAAI;YAAE;QAAQ;QAE3B,IAAI,CAAC,GAAG,CAAC;IACX;IAEA;;;GAGC,GACD,eAAe,OAAO,EAAE,IAAI,EAAE;QAC5B,mBAAmB,GACnB,MAAM,OAAO,QAAQ,IAAI;QACzB,KAAK,IAAI,GAAG;QACZ,KAAK,WAAW,GAAG;QACnB,IAAI,CAAC,GAAG,CAAC;IACX;IAEA,SAAS;QACP,MAAM,WAAW,IAAI,aAAa,IAAI,EAAE,IAAI,CAAC,OAAO;QACpD,OAAO,SAAS,KAAK;IACvB;IAEA,WAAW;QACT,OAAO;IACT;AACF;AAEA;;;GAGG,GACH,SAAS,OAAO,KAAK;IACnB,OAAO,IAAI,OAAO,MAAM,OAAO,CAAC,yBAAyB,SAAS;AACpE;AAEA;;;CAGC,GACD,SAAS,OAAO,EAAE;IAChB,IAAI,CAAC,IAAI,OAAO;IAChB,IAAI,OAAO,OAAO,UAAU,OAAO;IAEnC,OAAO,GAAG,MAAM;AAClB;AAEA;;;CAGC,GACD,SAAS,OAAO,GAAG,IAAI;IACrB,MAAM,SAAS,KAAK,GAAG,CAAC,CAAC,IAAM,OAAO,IAAI,IAAI,CAAC;IAC/C,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,OAAO,GAAG,IAAI;IACrB,MAAM,SAAS,MAAM,KAAK,GAAG,CAAC,CAAC,IAAM,OAAO,IAAI,IAAI,CAAC,OAAO;IAC5D,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,iBAAiB,EAAE;IAC1B,OAAO,AAAC,IAAI,OAAO,GAAG,QAAQ,KAAK,KAAM,IAAI,CAAC,IAAI,MAAM,GAAG;AAC7D;AAEA;;;;CAIC,GACD,SAAS,WAAW,EAAE,EAAE,MAAM;IAC5B,MAAM,QAAQ,MAAM,GAAG,IAAI,CAAC;IAC5B,OAAO,SAAS,MAAM,KAAK,KAAK;AAClC;AAEA,oEAAoE;AACpE,6DAA6D;AAC7D,wEAAwE;AACxE,sEAAsE;AACtE,yBAAyB;AACzB,uEAAuE;AACvE,+BAA+B;AAC/B,MAAM,aAAa;AAEnB,iEAAiE;AACjE,4CAA4C;AAC5C,kEAAkE;AAClE,qEAAqE;AACrE,+CAA+C;AAC/C;;;;CAIC,GACD,SAAS,KAAK,OAAO,EAAE,YAAY,GAAG;IACpC,IAAI,cAAc;IAElB,OAAO,QAAQ,GAAG,CAAC,CAAC;QAClB,eAAe;QACf,MAAM,SAAS;QACf,IAAI,KAAK,OAAO;QAChB,IAAI,MAAM;QAEV,MAAO,GAAG,MAAM,GAAG,EAAG;YACpB,MAAM,QAAQ,WAAW,IAAI,CAAC;YAC9B,IAAI,CAAC,OAAO;gBACV,OAAO;gBACP;YACF;YACA,OAAO,GAAG,SAAS,CAAC,GAAG,MAAM,KAAK;YAClC,KAAK,GAAG,SAAS,CAAC,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;YAC/C,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,QAAQ,KAAK,CAAC,EAAE,EAAE;gBACpC,4BAA4B;gBAC5B,OAAO,OAAO,OAAO,OAAO,KAAK,CAAC,EAAE,IAAI;YAC1C,OAAO;gBACL,OAAO,KAAK,CAAC,EAAE;gBACf,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK;oBACpB;gBACF;YACF;QACF;QACA,OAAO;IACT,GAAG,GAAG,CAAC,CAAA,KAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;AAC/B;AAEA,iBAAiB;AACjB,MAAM,mBAAmB;AACzB,MAAM,WAAW;AACjB,MAAM,sBAAsB;AAC5B,MAAM,YAAY;AAClB,MAAM,cAAc,0EAA0E,8BAA8B;AAC5H,MAAM,mBAAmB,gBAAgB,QAAQ;AACjD,MAAM,iBAAiB;AAEvB;;AAEA,GACA,MAAM,UAAU,CAAC,OAAO,CAAC,CAAC;IACxB,MAAM,eAAe;IACrB,IAAI,KAAK,MAAM,EAAE;QACf,KAAK,KAAK,GAAG,OACX,cACA,QACA,KAAK,MAAM,EACX;IACJ;IACA,OAAO,QAAQ;QACb,WAAW;QACX,OAAO;QACP,KAAK;QACL,WAAW;QACX,yBAAyB,GACzB,YAAY,CAAC,GAAG;YACd,IAAI,EAAE,KAAK,KAAK,GAAG,KAAK,WAAW;QACrC;IACF,GAAG;AACL;AAEA,eAAe;AACf,MAAM,mBAAmB;IACvB,OAAO;IAAgB,WAAW;AACpC;AACA,MAAM,mBAAmB;IACvB,WAAW;IACX,OAAO;IACP,KAAK;IACL,SAAS;IACT,UAAU;QAAC;KAAiB;AAC9B;AACA,MAAM,oBAAoB;IACxB,WAAW;IACX,OAAO;IACP,KAAK;IACL,SAAS;IACT,UAAU;QAAC;KAAiB;AAC9B;AACA,MAAM,qBAAqB;IACzB,OAAO;AACT;AACA;;;;;;;CAOC,GACD,MAAM,UAAU,SAAS,KAAK,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;IACnD,MAAM,OAAO,QACX;QACE,WAAW;QACX;QACA;QACA,UAAU,EAAE;IACd,GACA;IAEF,KAAK,QAAQ,CAAC,IAAI,CAAC;IACnB,KAAK,QAAQ,CAAC,IAAI,CAAC;QACjB,WAAW;QACX,OAAO;QACP,WAAW;IACb;IACA,OAAO;AACT;AACA,MAAM,sBAAsB,QAAQ,MAAM;AAC1C,MAAM,uBAAuB,QAAQ,QAAQ;AAC7C,MAAM,oBAAoB,QAAQ,KAAK;AACvC,MAAM,cAAc;IAClB,WAAW;IACX,OAAO;IACP,WAAW;AACb;AACA,MAAM,gBAAgB;IACpB,WAAW;IACX,OAAO;IACP,WAAW;AACb;AACA,MAAM,qBAAqB;IACzB,WAAW;IACX,OAAO;IACP,WAAW;AACb;AACA,MAAM,kBAAkB;IACtB,WAAW;IACX,OAAO,YAAY,MACjB,mBACA,qBACA,uBACA,uBACA,UACA,YACA,mBACA;IACF,WAAW;AACb;AACA,MAAM,cAAc;IAClB,2EAA2E;IAC3E,yBAAyB;IACzB,EAAE;IACF,oBAAoB;IACpB,EAAE;IACF,oEAAoE;IACpE,OAAO;IACP,UAAU;QAAC;YACT,WAAW;YACX,OAAO;YACP,KAAK;YACL,SAAS;YACT,UAAU;gBACR;gBACA;oBACE,OAAO;oBACP,KAAK;oBACL,WAAW;oBACX,UAAU;wBAAC;qBAAiB;gBAC9B;aACD;QACH;KAAE;AACJ;AACA,MAAM,aAAa;IACjB,WAAW;IACX,OAAO;IACP,WAAW;AACb;AACA,MAAM,wBAAwB;IAC5B,WAAW;IACX,OAAO;IACP,WAAW;AACb;AACA,MAAM,eAAe;IACnB,gDAAgD;IAChD,OAAO,YAAY;IACnB,WAAW;AACb;AAEA;;;;;;CAMC,GACD,MAAM,oBAAoB,SAAS,IAAI;IACrC,OAAO,OAAO,MAAM,CAAC,MACnB;QACE,yBAAyB,GACzB,YAAY,CAAC,GAAG;YAAW,KAAK,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;QAAE;QACzD,yBAAyB,GACzB,UAAU,CAAC,GAAG;YAAW,IAAI,KAAK,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,WAAW;QAAI;IACnF;AACJ;AAEA,IAAI,QAAQ,WAAW,GAAE,OAAO,MAAM,CAAC;IACnC,WAAW;IACX,kBAAkB;IAClB,UAAU;IACV,qBAAqB;IACrB,WAAW;IACX,aAAa;IACb,kBAAkB;IAClB,gBAAgB;IAChB,SAAS;IACT,kBAAkB;IAClB,kBAAkB;IAClB,mBAAmB;IACnB,oBAAoB;IACpB,SAAS;IACT,qBAAqB;IACrB,sBAAsB;IACtB,mBAAmB;IACnB,aAAa;IACb,eAAe;IACf,oBAAoB;IACpB,iBAAiB;IACjB,aAAa;IACb,YAAY;IACZ,uBAAuB;IACvB,cAAc;IACd,mBAAmB;AACvB;AAEA,+BAA+B;AAC/B,+DAA+D;AAE/D,8EAA8E;AAC9E,sEAAsE;AAEtE,2EAA2E;AAC3E,+EAA+E;AAC/E,gFAAgF;AAChF,8EAA8E;AAC9E,uEAAuE;AAEvE,SAAS;AAET,iEAAiE;AACjE;;;;;;;;CAQC,GACD,SAAS,sBAAsB,KAAK,EAAE,QAAQ;IAC5C,MAAM,SAAS,MAAM,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE;IAC3C,IAAI,WAAW,KAAK;QAClB,SAAS,WAAW;IACtB;AACF;AAGA;;;CAGC,GACD,SAAS,cAAc,IAAI,EAAE,MAAM;IACjC,IAAI,CAAC,QAAQ;IACb,IAAI,CAAC,KAAK,aAAa,EAAE;IAEzB,4EAA4E;IAC5E,6EAA6E;IAC7E,yEAAyE;IACzE,+EAA+E;IAC/E,QAAQ;IACR,KAAK,KAAK,GAAG,SAAS,KAAK,aAAa,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO;IAChE,KAAK,aAAa,GAAG;IACrB,KAAK,QAAQ,GAAG,KAAK,QAAQ,IAAI,KAAK,aAAa;IACnD,OAAO,KAAK,aAAa;IAEzB,6DAA6D;IAC7D,gDAAgD;IAChD,wCAAwC;IACxC,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,SAAS,GAAG;AACrD;AAEA;;;CAGC,GACD,SAAS,eAAe,IAAI,EAAE,OAAO;IACnC,IAAI,CAAC,MAAM,OAAO,CAAC,KAAK,OAAO,GAAG;IAElC,KAAK,OAAO,GAAG,UAAU,KAAK,OAAO;AACvC;AAEA;;;CAGC,GACD,SAAS,aAAa,IAAI,EAAE,OAAO;IACjC,IAAI,CAAC,KAAK,KAAK,EAAE;IACjB,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,EAAE,MAAM,IAAI,MAAM;IAE5C,KAAK,KAAK,GAAG,KAAK,KAAK;IACvB,OAAO,KAAK,KAAK;AACnB;AAEA;;;CAGC,GACD,SAAS,iBAAiB,IAAI,EAAE,OAAO;IACrC,wCAAwC;IACxC,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,SAAS,GAAG;AACrD;AAEA,uDAAuD;AACvD,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ,uBAAuB;CAChC;AAED,MAAM,4BAA4B;AAElC;;;;;CAKC,GACD,SAAS,gBAAgB,WAAW,EAAE,eAAe,EAAE,YAAY,yBAAyB;IAC1F,sBAAsB,GACtB,MAAM,mBAAmB,CAAC;IAE1B,4EAA4E;IAC5E,gFAAgF;IAChF,IAAI,OAAO,gBAAgB,UAAU;QACnC,YAAY,WAAW,YAAY,KAAK,CAAC;IAC3C,OAAO,IAAI,MAAM,OAAO,CAAC,cAAc;QACrC,YAAY,WAAW;IACzB,OAAO;QACL,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,SAAS,SAAS;YACjD,uDAAuD;YACvD,OAAO,MAAM,CACX,kBACA,gBAAgB,WAAW,CAAC,UAAU,EAAE,iBAAiB;QAE7D;IACF;IACA,OAAO;;IAEP,MAAM;IAEN;;;;;;;GAOC,GACD,SAAS,YAAY,SAAS,EAAE,WAAW;QACzC,IAAI,iBAAiB;YACnB,cAAc,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW;QAClD;QACA,YAAY,OAAO,CAAC,SAAS,OAAO;YAClC,MAAM,OAAO,QAAQ,KAAK,CAAC;YAC3B,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG;gBAAC;gBAAW,gBAAgB,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;aAAE;QAC5E;IACF;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,gBAAgB,OAAO,EAAE,aAAa;IAC7C,gDAAgD;IAChD,qDAAqD;IACrD,IAAI,eAAe;QACjB,OAAO,OAAO;IAChB;IAEA,OAAO,cAAc,WAAW,IAAI;AACtC;AAEA;;;2BAG2B,GAC3B,SAAS,cAAc,OAAO;IAC5B,OAAO,gBAAgB,QAAQ,CAAC,QAAQ,WAAW;AACrD;AAEA,cAAc;AAEd;;;;;;;;CAQC,GACD,SAAS,gBAAgB,QAAQ,EAAE,EAAE,OAAO,EAAE;IAC5C;;;;;GAKC,GACD,SAAS,OAAO,KAAK,EAAE,MAAM;QAC3B,OAAO,IAAI,OACT,OAAO,QACP,MAAM,CAAC,SAAS,gBAAgB,GAAG,MAAM,EAAE,IAAI,CAAC,SAAS,MAAM,EAAE;IAErE;IAEA;;;;;;;;;;;;EAYA,GACA,MAAM;QACJ,aAAc;YACZ,IAAI,CAAC,YAAY,GAAG,CAAC;YACrB,aAAa;YACb,IAAI,CAAC,OAAO,GAAG,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,QAAQ,GAAG;QAClB;QAEA,aAAa;QACb,QAAQ,EAAE,EAAE,IAAI,EAAE;YAChB,KAAK,QAAQ,GAAG,IAAI,CAAC,QAAQ;YAC7B,aAAa;YACb,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG;YAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAAC;gBAAM;aAAG;YAC5B,IAAI,CAAC,OAAO,IAAI,iBAAiB,MAAM;QACzC;QAEA,UAAU;YACR,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,GAAG;gBAC7B,4DAA4D;gBAC5D,aAAa;gBACb,IAAI,CAAC,IAAI,GAAG,IAAM;YACpB;YACA,MAAM,cAAc,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,KAAM,EAAE,CAAC,EAAE;YAChD,IAAI,CAAC,SAAS,GAAG,OAAO,KAAK,cAAc;YAC3C,IAAI,CAAC,SAAS,GAAG;QACnB;QAEA,sBAAsB,GACtB,KAAK,CAAC,EAAE;YACN,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS;YACzC,MAAM,QAAQ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAClC,IAAI,CAAC,OAAO;gBAAE,OAAO;YAAM;YAE3B,wCAAwC;YACxC,MAAM,IAAI,MAAM,SAAS,CAAC,CAAC,IAAI,IAAM,IAAI,KAAK,OAAO;YACrD,aAAa;YACb,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC,EAAE;YACtC,sEAAsE;YACtE,+CAA+C;YAC/C,MAAM,MAAM,CAAC,GAAG;YAEhB,OAAO,OAAO,MAAM,CAAC,OAAO;QAC9B;IACF;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BA,GACA,MAAM;QACJ,aAAc;YACZ,aAAa;YACb,IAAI,CAAC,KAAK,GAAG,EAAE;YACf,aAAa;YACb,IAAI,CAAC,YAAY,GAAG,EAAE;YACtB,IAAI,CAAC,KAAK,GAAG;YAEb,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,UAAU,GAAG;QACpB;QAEA,aAAa;QACb,WAAW,KAAK,EAAE;YAChB,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;YAE7D,MAAM,UAAU,IAAI;YACpB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,GAAK,QAAQ,OAAO,CAAC,IAAI;YACpE,QAAQ,OAAO;YACf,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;YAC3B,OAAO;QACT;QAEA,6BAA6B;YAC3B,OAAO,IAAI,CAAC,UAAU,KAAK;QAC7B;QAEA,cAAc;YACZ,IAAI,CAAC,UAAU,GAAG;QACpB;QAEA,aAAa;QACb,QAAQ,EAAE,EAAE,IAAI,EAAE;YAChB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gBAAC;gBAAI;aAAK;YAC1B,IAAI,KAAK,IAAI,KAAK,SAAS,IAAI,CAAC,KAAK;QACvC;QAEA,sBAAsB,GACtB,KAAK,CAAC,EAAE;YACN,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU;YACzC,EAAE,SAAS,GAAG,IAAI,CAAC,SAAS;YAC5B,IAAI,SAAS,EAAE,IAAI,CAAC;YAEpB,8EAA8E;YAC9E,0EAA0E;YAC1E,0EAA0E;YAC1E,uCAAuC;YAEvC,4CAA4C;YAC5C,EAAE;YACF,iBAAiB;YAEjB,wEAAwE;YACxE,wEAAwE;YACxE,uEAAuE;YACvE,uEAAuE;YACvE,gDAAgD;YAEhD,mEAAmE;YACnE,wEAAwE;YAExE,wDAAwD;YACxD,4DAA4D;YAC5D,SAAS;YACT,iBAAiB;YAEjB,qEAAqE;YACrE,0EAA0E;YAC1E,kCAAkC;YAClC,EAAE;YACF,6EAA6E;YAC7E,kCAAkC;YAClC,uDAAuD;YACvD,uDAAuD;YACvD,IAAI,IAAI,CAAC,0BAA0B,IAAI;gBACrC,IAAI,UAAU,OAAO,KAAK,KAAK,IAAI,CAAC,SAAS;qBAAS;oBACpD,MAAM,KAAK,IAAI,CAAC,UAAU,CAAC;oBAC3B,GAAG,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG;oBAChC,SAAS,GAAG,IAAI,CAAC;gBACnB;YACF;YAEA,IAAI,QAAQ;gBACV,IAAI,CAAC,UAAU,IAAI,OAAO,QAAQ,GAAG;gBACrC,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,KAAK,EAAE;oBAClC,+CAA+C;oBAC/C,IAAI,CAAC,WAAW;gBAClB;YACF;YAEA,OAAO;QACT;IACF;IAEA;;;;;;GAMC,GACD,SAAS,eAAe,IAAI;QAC1B,MAAM,KAAK,IAAI;QAEf,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAA,OAAQ,GAAG,OAAO,CAAC,KAAK,KAAK,EAAE;gBAAE,MAAM;gBAAM,MAAM;YAAQ;QAEjF,IAAI,KAAK,aAAa,EAAE;YACtB,GAAG,OAAO,CAAC,KAAK,aAAa,EAAE;gBAAE,MAAM;YAAM;QAC/C;QACA,IAAI,KAAK,OAAO,EAAE;YAChB,GAAG,OAAO,CAAC,KAAK,OAAO,EAAE;gBAAE,MAAM;YAAU;QAC7C;QAEA,OAAO;IACT;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BC,GAED;;;;;;;;GAQC,GACD,SAAS,YAAY,IAAI,EAAE,MAAM;QAC/B,MAAM,QAAmC;QACzC,IAAI,KAAK,UAAU,EAAE,OAAO;QAE5B;YACE,2EAA2E;YAC3E,sCAAsC;YACtC;SACD,CAAC,OAAO,CAAC,CAAA,MAAO,IAAI,MAAM;QAE3B,SAAS,kBAAkB,CAAC,OAAO,CAAC,CAAA,MAAO,IAAI,MAAM;QAErD,6DAA6D;QAC7D,KAAK,aAAa,GAAG;QAErB;YACE;YACA,4EAA4E;YAC5E,0DAA0D;YAC1D;YACA,0CAA0C;YAC1C;SACD,CAAC,OAAO,CAAC,CAAA,MAAO,IAAI,MAAM;QAE3B,KAAK,UAAU,GAAG;QAElB,IAAI,iBAAiB;QACrB,IAAI,OAAO,KAAK,QAAQ,KAAK,UAAU;YACrC,iBAAiB,KAAK,QAAQ,CAAC,QAAQ;YACvC,OAAO,KAAK,QAAQ,CAAC,QAAQ;QAC/B;QAEA,IAAI,KAAK,QAAQ,EAAE;YACjB,KAAK,QAAQ,GAAG,gBAAgB,KAAK,QAAQ,EAAE,SAAS,gBAAgB;QAC1E;QAEA,uBAAuB;QACvB,IAAI,KAAK,OAAO,IAAI,gBAAgB;YAClC,MAAM,IAAI,MAAM;QAClB;QAEA,wEAAwE;QACxE,wDAAwD;QACxD,iBAAiB,kBAAkB,KAAK,OAAO,IAAI;QACnD,MAAM,gBAAgB,GAAG,OAAO,gBAAgB;QAEhD,IAAI,QAAQ;YACV,IAAI,CAAC,KAAK,KAAK,EAAE,KAAK,KAAK,GAAG;YAC9B,MAAM,OAAO,GAAG,OAAO,KAAK,KAAK;YACjC,IAAI,KAAK,cAAc,EAAE,KAAK,GAAG,GAAG,KAAK,KAAK;YAC9C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,cAAc,EAAE,KAAK,GAAG,GAAG;YAClD,IAAI,KAAK,GAAG,EAAE,MAAM,KAAK,GAAG,OAAO,KAAK,GAAG;YAC3C,MAAM,aAAa,GAAG,OAAO,KAAK,GAAG,KAAK;YAC1C,IAAI,KAAK,cAAc,IAAI,OAAO,aAAa,EAAE;gBAC/C,MAAM,aAAa,IAAI,CAAC,KAAK,GAAG,GAAG,MAAM,EAAE,IAAI,OAAO,aAAa;YACrE;QACF;QACA,IAAI,KAAK,OAAO,EAAE,MAAM,SAAS,GAAG,OAAuC,KAAK,OAAO;QACvF,IAAI,CAAC,KAAK,QAAQ,EAAE,KAAK,QAAQ,GAAG,EAAE;QAEtC,KAAK,QAAQ,GAAG,EAAE,CAAC,MAAM,IAAI,KAAK,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC;YACvD,OAAO,kBAAkB,MAAM,SAAS,OAAO;QACjD;QACA,KAAK,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;YAAI,YAA+B,GAAI;QAAQ;QAE/E,IAAI,KAAK,MAAM,EAAE;YACf,YAAY,KAAK,MAAM,EAAE;QAC3B;QAEA,MAAM,OAAO,GAAG,eAAe;QAC/B,OAAO;IACT;IAEA,IAAI,CAAC,SAAS,kBAAkB,EAAE,SAAS,kBAAkB,GAAG,EAAE;IAElE,qCAAqC;IACrC,IAAI,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,QAAQ,CAAC,SAAS;QAC3D,MAAM,IAAI,MAAM;IAClB;IAEA,sDAAsD;IACtD,SAAS,gBAAgB,GAAG,QAAQ,SAAS,gBAAgB,IAAI,CAAC;IAElE,OAAO,YAA+B;AACxC;AAEA;;;;;;;;;;GAUG,GACH,SAAS,mBAAmB,IAAI;IAC9B,IAAI,CAAC,MAAM,OAAO;IAElB,OAAO,KAAK,cAAc,IAAI,mBAAmB,KAAK,MAAM;AAC9D;AAEA;;;;;;;;;GASG,GACH,SAAS,kBAAkB,IAAI;IAC7B,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,cAAc,EAAE;QACzC,KAAK,cAAc,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,SAAS,OAAO;YACtD,OAAO,QAAQ,MAAM;gBAAE,UAAU;YAAK,GAAG;QAC3C;IACF;IAEA,SAAS;IACT,4EAA4E;IAC5E,kEAAkE;IAClE,IAAI,KAAK,cAAc,EAAE;QACvB,OAAO,KAAK,cAAc;IAC5B;IAEA,QAAQ;IACR,2DAA2D;IAC3D,uDAAuD;IACvD,kCAAkC;IAClC,IAAI,mBAAmB,OAAO;QAC5B,OAAO,QAAQ,MAAM;YAAE,QAAQ,KAAK,MAAM,GAAG,QAAQ,KAAK,MAAM,IAAI;QAAK;IAC3E;IAEA,IAAI,OAAO,QAAQ,CAAC,OAAO;QACzB,OAAO,QAAQ;IACjB;IAEA,sDAAsD;IACtD,OAAO;AACT;AAEA,IAAI,UAAU;AAEd,cAAc;AAEd,SAAS,yBAAyB,KAAK;IACrC,OAAO,QAAQ,SAAS,UAAU;AACpC;AAEA,SAAS,eAAe,IAAI;IAC1B,MAAM,YAAY;QAChB,OAAO;YAAC;YAAY;YAAQ;SAAa;QACzC,MAAM;YACJ,OAAO;gBACL,kBAAkB;gBAClB,iBAAiB;YACnB;QACF;QACA,UAAU;YACR;gBACE,IAAI,IAAI,CAAC,eAAe,EAAE,OAAO;gBAEjC,OAAO,UAAU,IAAI,CAAC,gBAAgB;YACxC;YACA;gBACE,gDAAgD;gBAChD,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,WAAW,CAAC,IAAI,CAAC,QAAQ,GAAG;oBACxD,QAAQ,IAAI,CAAC,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,mCAAmC,CAAC;oBAChF,IAAI,CAAC,eAAe,GAAG;oBACvB,OAAO,WAAW,IAAI,CAAC,IAAI;gBAC7B;gBAEA,IAAI,SAAS,CAAC;gBACd,IAAI,IAAI,CAAC,UAAU,EAAE;oBACnB,SAAS,KAAK,aAAa,CAAC,IAAI,CAAC,IAAI;oBACrC,IAAI,CAAC,gBAAgB,GAAG,OAAO,QAAQ;gBACzC,OAAO;oBACL,SAAS,KAAK,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc;oBACrE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ;gBACvC;gBACA,OAAO,OAAO,KAAK;YACrB;YACA;gBACE,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,yBAAyB,IAAI,CAAC,UAAU;YACnE;YACA;gBACE,OAAO;YACT;QACF;QACA,mEAAmE;QACnE,wBAAwB;QACxB,QAAO,aAAa;YAClB,OAAO,cAAc,OAAO,CAAC,GAAG;gBAC9B,cAAc,QAAQ;oBACpB,OAAO,IAAI,CAAC,SAAS;oBACrB,UAAU;wBAAE,WAAW,IAAI,CAAC,WAAW;oBAAC;gBAC1C;aACD;QACH;IAEF;IAEA,MAAM,YAAY;QAChB,SAAQ,GAAG;YACT,IAAI,SAAS,CAAC,eAAe;QAC/B;IACF;IAEA,OAAO;QAAE;QAAW;IAAU;AAChC;AAEA,iBAAiB,GAEjB,uBAAuB,GACvB,MAAM,kBAAkB;IACtB,0BAA0B,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;QAC7C,MAAM,iBAAiB,WAAW;QAClC,IAAI,CAAC,eAAe,MAAM,EAAE;QAE5B,MAAM,aAAa,SAAS,aAAa,CAAC;QAC1C,WAAW,SAAS,GAAG,OAAO,KAAK;QACnC,OAAO,KAAK,GAAG,aAAa,gBAAgB,WAAW,aAAa;IACtE;AACF;AAEA,oCAAoC,GAEpC;;;;;CAKC,GAED;;CAEC,GACD,SAAS,IAAI,IAAI;IACf,OAAO,KAAK,QAAQ,CAAC,WAAW;AAClC;AAEA;;CAEC,GACD,SAAS,WAAW,IAAI;IACtB,kBAAkB,GAClB,MAAM,SAAS,EAAE;IACjB,CAAC,SAAS,YAAY,IAAI,EAAE,MAAM;QAChC,IAAK,IAAI,QAAQ,KAAK,UAAU,EAAE,OAAO,QAAQ,MAAM,WAAW,CAAE;YAClE,IAAI,MAAM,QAAQ,KAAK,GAAG;gBACxB,UAAU,MAAM,SAAS,CAAC,MAAM;YAClC,OAAO,IAAI,MAAM,QAAQ,KAAK,GAAG;gBAC/B,OAAO,IAAI,CAAC;oBACV,OAAO;oBACP,QAAQ;oBACR,MAAM;gBACR;gBACA,SAAS,YAAY,OAAO;gBAC5B,mEAAmE;gBACnE,kEAAkE;gBAClE,iEAAiE;gBACjE,IAAI,CAAC,IAAI,OAAO,KAAK,CAAC,oBAAoB;oBACxC,OAAO,IAAI,CAAC;wBACV,OAAO;wBACP,QAAQ;wBACR,MAAM;oBACR;gBACF;YACF;QACF;QACA,OAAO;IACT,CAAC,EAAE,MAAM;IACT,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,aAAa,QAAQ,EAAE,WAAW,EAAE,KAAK;IAChD,IAAI,YAAY;IAChB,IAAI,SAAS;IACb,MAAM,YAAY,EAAE;IAEpB,SAAS;QACP,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,YAAY,MAAM,EAAE;YAC3C,OAAO,SAAS,MAAM,GAAG,WAAW;QACtC;QACA,IAAI,QAAQ,CAAC,EAAE,CAAC,MAAM,KAAK,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE;YAChD,OAAO,AAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,GAAG,WAAW,CAAC,EAAE,CAAC,MAAM,GAAI,WAAW;QACnE;QAEA;;;;;;;;;;;;;;IAcA,GACA,OAAO,WAAW,CAAC,EAAE,CAAC,KAAK,KAAK,UAAU,WAAW;IACvD;IAEA;;GAEC,GACD,SAAS,KAAK,IAAI;QAChB,uBAAuB,GACvB,SAAS,gBAAgB,IAAI;YAC3B,OAAO,MAAM,KAAK,QAAQ,GAAG,OAAO,WAAW,KAAK,KAAK,IAAI;QAC/D;QACA,aAAa;QACb,UAAU,MAAM,IAAI,QAAQ,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,UAAU,EAAE,iBAAiB,IAAI,CAAC,MAAM;IACvF;IAEA;;GAEC,GACD,SAAS,MAAM,IAAI;QACjB,UAAU,OAAO,IAAI,QAAQ;IAC/B;IAEA;;GAEC,GACD,SAAS,OAAO,KAAK;QACnB,CAAC,MAAM,KAAK,KAAK,UAAU,OAAO,KAAK,EAAE,MAAM,IAAI;IACrD;IAEA,MAAO,SAAS,MAAM,IAAI,YAAY,MAAM,CAAE;QAC5C,IAAI,SAAS;QACb,UAAU,WAAW,MAAM,SAAS,CAAC,WAAW,MAAM,CAAC,EAAE,CAAC,MAAM;QAChE,YAAY,MAAM,CAAC,EAAE,CAAC,MAAM;QAC5B,IAAI,WAAW,UAAU;YACvB;;;;;MAKA,GACA,UAAU,OAAO,GAAG,OAAO,CAAC;YAC5B,GAAG;gBACD,OAAO,OAAO,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE;gBAC7B,SAAS;YACX,QAAS,WAAW,YAAY,OAAO,MAAM,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,KAAK,UAAW;YACjF,UAAU,OAAO,GAAG,OAAO,CAAC;QAC9B,OAAO;YACL,IAAI,MAAM,CAAC,EAAE,CAAC,KAAK,KAAK,SAAS;gBAC/B,UAAU,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI;YAC/B,OAAO;gBACL,UAAU,GAAG;YACf;YACA,OAAO,OAAO,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE;QAC/B;IACF;IACA,OAAO,SAAS,WAAW,MAAM,MAAM,CAAC;AAC1C;AAEA;;;;;AAKA,GAEA;;CAEC,GACD,MAAM,mBAAmB,CAAC;AAE1B;;CAEC,GACD,MAAM,QAAQ,CAAC;IACb,QAAQ,KAAK,CAAC;AAChB;AAEA;;;CAGC,GACD,MAAM,OAAO,CAAC,SAAS,GAAG;IACxB,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,KAAK;AACrC;AAEA;;;CAGC,GACD,MAAM,aAAa,CAAC,SAAS;IAC3B,IAAI,gBAAgB,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE;IAE/C,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,QAAQ,EAAE,EAAE,SAAS;IACrD,gBAAgB,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS,CAAC,GAAG;AAC9C;AAEA;;;AAGA,GAEA,MAAM,WAAW;AACjB,MAAM,YAAY;AAClB,MAAM,WAAW,OAAO;AAExB;;;CAGC,GACD,MAAM,OAAO,SAAS,IAAI;IACxB,kEAAkE;IAClE,qCAAqC,GACrC,MAAM,YAAY,OAAO,MAAM,CAAC;IAChC,mCAAmC,GACnC,MAAM,UAAU,OAAO,MAAM,CAAC;IAC9B,yBAAyB,GACzB,MAAM,UAAU,EAAE;IAElB,qEAAqE;IACrE,sDAAsD;IACtD,IAAI,YAAY;IAChB,MAAM,cAAc;IACpB,MAAM,qBAAqB;IAC3B,qBAAqB,GACrB,MAAM,qBAAqB;QAAE,mBAAmB;QAAM,MAAM;QAAc,UAAU,EAAE;IAAC;IAEvF,uEAAuE;IACvE,yCAAyC;IACzC,sBAAsB,GACtB,IAAI,UAAU;QACZ,eAAe;QACf,kBAAkB;QAClB,aAAa;QACb,YAAY;QACZ,OAAO;QACP,WAAW;QACX,oEAAoE;QACpE,0DAA0D;QAC1D,WAAW;IACb;IAEA,qBAAqB,GAErB;;;GAGC,GACD,SAAS,mBAAmB,YAAY;QACtC,OAAO,QAAQ,aAAa,CAAC,IAAI,CAAC;IACpC;IAEA;;GAEC,GACD,SAAS,cAAc,KAAK;QAC1B,IAAI,UAAU,MAAM,SAAS,GAAG;QAEhC,WAAW,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,SAAS,GAAG;QAE3D,6DAA6D;QAC7D,MAAM,QAAQ,QAAQ,gBAAgB,CAAC,IAAI,CAAC;QAC5C,IAAI,OAAO;YACT,MAAM,WAAW,YAAY,KAAK,CAAC,EAAE;YACrC,IAAI,CAAC,UAAU;gBACb,KAAK,mBAAmB,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC9C,KAAK,qDAAqD;YAC5D;YACA,OAAO,WAAW,KAAK,CAAC,EAAE,GAAG;QAC/B;QAEA,OAAO,QACJ,KAAK,CAAC,OACN,IAAI,CAAC,CAAC,SAAW,mBAAmB,WAAW,YAAY;IAChE;IAEA;;;;;;;;;;;;;;;;;;;;;EAqBA,GACA,SAAS,UAAU,kBAAkB,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY;QAChF,IAAI,OAAO;QACX,IAAI,eAAe;QACnB,IAAI,OAAO,kBAAkB,UAAU;YACrC,OAAO;YACP,iBAAiB,cAAc,cAAc;YAC7C,eAAe,cAAc,QAAQ;YACrC,oDAAoD;YACpD,wCAAwC;YACxC,eAAe;QACjB,OAAO;YACL,UAAU;YACV,WAAW,UAAU;YACrB,WAAW,UAAU;YACrB,eAAe;YACf,OAAO;QACT;QAEA,mCAAmC,GACnC,MAAM,UAAU;YACd;YACA,UAAU;QACZ;QACA,2EAA2E;QAC3E,4CAA4C;QAC5C,KAAK,oBAAoB;QAEzB,wEAAwE;QACxE,qDAAqD;QACrD,MAAM,SAAS,QAAQ,MAAM,GACzB,QAAQ,MAAM,GACd,WAAW,QAAQ,QAAQ,EAAE,QAAQ,IAAI,EAAE,gBAAgB;QAE/D,OAAO,IAAI,GAAG,QAAQ,IAAI;QAC1B,uDAAuD;QACvD,KAAK,mBAAmB;QAExB,OAAO;IACT;IAEA;;;;;;;;EAQA,GACA,SAAS,WAAW,YAAY,EAAE,eAAe,EAAE,cAAc,EAAE,YAAY;QAC7E;;;;;KAKC,GACD,SAAS,YAAY,IAAI,EAAE,KAAK;YAC9B,MAAM,YAAY,SAAS,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,WAAW,KAAK,KAAK,CAAC,EAAE;YAC/E,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE,cAAc,KAAK,QAAQ,CAAC,UAAU;QACnG;QAEA,SAAS;YACP,IAAI,CAAC,IAAI,QAAQ,EAAE;gBACjB,QAAQ,OAAO,CAAC;gBAChB;YACF;YAEA,IAAI,YAAY;YAChB,IAAI,gBAAgB,CAAC,SAAS,GAAG;YACjC,IAAI,QAAQ,IAAI,gBAAgB,CAAC,IAAI,CAAC;YACtC,IAAI,MAAM;YAEV,MAAO,MAAO;gBACZ,OAAO,WAAW,SAAS,CAAC,WAAW,MAAM,KAAK;gBAClD,MAAM,OAAO,YAAY,KAAK;gBAC9B,IAAI,MAAM;oBACR,MAAM,CAAC,MAAM,iBAAiB,GAAG;oBACjC,QAAQ,OAAO,CAAC;oBAChB,MAAM;oBAEN,aAAa;oBACb,IAAI,KAAK,UAAU,CAAC,MAAM;wBACxB,iDAAiD;wBACjD,2BAA2B;wBAC3B,OAAO,KAAK,CAAC,EAAE;oBACjB,OAAO;wBACL,MAAM,WAAW,SAAS,gBAAgB,CAAC,KAAK,IAAI;wBACpD,QAAQ,UAAU,CAAC,KAAK,CAAC,EAAE,EAAE;oBAC/B;gBACF,OAAO;oBACL,OAAO,KAAK,CAAC,EAAE;gBACjB;gBACA,YAAY,IAAI,gBAAgB,CAAC,SAAS;gBAC1C,QAAQ,IAAI,gBAAgB,CAAC,IAAI,CAAC;YACpC;YACA,OAAO,WAAW,MAAM,CAAC;YACzB,QAAQ,OAAO,CAAC;QAClB;QAEA,SAAS;YACP,IAAI,eAAe,IAAI;YACvB,0BAA0B,GAC1B,IAAI,SAAS;YAEb,IAAI,OAAO,IAAI,WAAW,KAAK,UAAU;gBACvC,IAAI,CAAC,SAAS,CAAC,IAAI,WAAW,CAAC,EAAE;oBAC/B,QAAQ,OAAO,CAAC;oBAChB;gBACF;gBACA,SAAS,WAAW,IAAI,WAAW,EAAE,YAAY,MAAM,aAAa,CAAC,IAAI,WAAW,CAAC;gBACrF,aAAa,CAAC,IAAI,WAAW,CAAC,GAAgC,OAAO,GAAG;YAC1E,OAAO;gBACL,SAAS,cAAc,YAAY,IAAI,WAAW,CAAC,MAAM,GAAG,IAAI,WAAW,GAAG;YAChF;YAEA,6EAA6E;YAC7E,iFAAiF;YACjF,mFAAmF;YACnF,SAAS;YACT,IAAI,IAAI,SAAS,GAAG,GAAG;gBACrB,aAAa,OAAO,SAAS;YAC/B;YACA,QAAQ,cAAc,CAAC,OAAO,OAAO,EAAE,OAAO,QAAQ;QACxD;QAEA,SAAS;YACP,IAAI,IAAI,WAAW,IAAI,MAAM;gBAC3B;YACF,OAAO;gBACL;YACF;YACA,aAAa;QACf;QAEA;;KAEC,GACD,SAAS,aAAa,IAAI;YACxB,IAAI,KAAK,SAAS,EAAE;gBAClB,QAAQ,QAAQ,CAAC,SAAS,gBAAgB,CAAC,KAAK,SAAS,CAAC,IAAI,KAAK,SAAS;YAC9E;YACA,MAAM,OAAO,MAAM,CAAC,MAAM;gBAAE,QAAQ;oBAAE,OAAO;gBAAI;YAAE;YACnD,OAAO;QACT;QAEA;;;;;KAKC,GACD,SAAS,UAAU,IAAI,EAAE,KAAK,EAAE,kBAAkB;YAChD,IAAI,UAAU,WAAW,KAAK,KAAK,EAAE;YAErC,IAAI,SAAS;gBACX,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,MAAM,OAAO,IAAI,SAAS;oBAC1B,IAAI,CAAC,SAAS,CAAC,OAAO;oBACtB,IAAI,KAAK,cAAc,EAAE,UAAU;gBACrC;gBAEA,IAAI,SAAS;oBACX,MAAO,KAAK,UAAU,IAAI,KAAK,MAAM,CAAE;wBACrC,OAAO,KAAK,MAAM;oBACpB;oBACA,OAAO;gBACT;YACF;YACA,uDAAuD;YACvD,8DAA8D;YAC9D,IAAI,KAAK,cAAc,EAAE;gBACvB,OAAO,UAAU,KAAK,MAAM,EAAE,OAAO;YACvC;QACF;QAEA;;;;KAIC,GACD,SAAS,SAAS,MAAM;YACtB,IAAI,IAAI,OAAO,CAAC,UAAU,KAAK,GAAG;gBAChC,8EAA8E;gBAC9E,QAAQ;gBACR,cAAc,MAAM,CAAC,EAAE;gBACvB,OAAO;YACT,OAAO;gBACL,0EAA0E;gBAC1E,0BAA0B;gBAC1B,2BAA2B;gBAC3B,OAAO;YACT;QACF;QAEA;;;;;KAKC,GACD,SAAS,aAAa,KAAK;YACzB,MAAM,SAAS,KAAK,CAAC,EAAE;YACvB,MAAM,UAAU,MAAM,IAAI;YAE1B,MAAM,OAAO,IAAI,SAAS;YAC1B,wDAAwD;YACxD,MAAM,kBAAkB;gBAAC,QAAQ,aAAa;gBAAE,OAAO,CAAC,WAAW;aAAC;YACpE,KAAK,MAAM,MAAM,gBAAiB;gBAChC,IAAI,CAAC,IAAI;gBACT,GAAG,OAAO;gBACV,IAAI,KAAK,cAAc,EAAE,OAAO,SAAS;YAC3C;YAEA,IAAI,WAAW,QAAQ,cAAc,EAAE;gBACrC,QAAQ,KAAK,GAAG,OAAO;YACzB;YAEA,IAAI,QAAQ,IAAI,EAAE;gBAChB,cAAc;YAChB,OAAO;gBACL,IAAI,QAAQ,YAAY,EAAE;oBACxB,cAAc;gBAChB;gBACA;gBACA,IAAI,CAAC,QAAQ,WAAW,IAAI,CAAC,QAAQ,YAAY,EAAE;oBACjD,aAAa;gBACf;YACF;YACA,aAAa;YACb,6BAA6B;YAC7B,mCAAmC;YACnC,sCAAsC;YACtC,IAAI;YACJ,OAAO,QAAQ,WAAW,GAAG,IAAI,OAAO,MAAM;QAChD;QAEA;;;;KAIC,GACD,SAAS,WAAW,KAAK;YACvB,MAAM,SAAS,KAAK,CAAC,EAAE;YACvB,MAAM,qBAAqB,gBAAgB,MAAM,CAAC,MAAM,KAAK;YAE7D,MAAM,UAAU,UAAU,KAAK,OAAO;YACtC,IAAI,CAAC,SAAS;gBAAE,OAAO;YAAU;YAEjC,MAAM,SAAS;YACf,IAAI,OAAO,IAAI,EAAE;gBACf,cAAc;YAChB,OAAO;gBACL,IAAI,CAAC,CAAC,OAAO,SAAS,IAAI,OAAO,UAAU,GAAG;oBAC5C,cAAc;gBAChB;gBACA;gBACA,IAAI,OAAO,UAAU,EAAE;oBACrB,aAAa;gBACf;YACF;YACA,GAAG;gBACD,IAAI,IAAI,SAAS,EAAE;oBACjB,QAAQ,SAAS;gBACnB;gBACA,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,WAAW,EAAE;oBACjC,aAAa,IAAI,SAAS;gBAC5B;gBACA,MAAM,IAAI,MAAM;YAClB,QAAS,QAAQ,QAAQ,MAAM,CAAE;YACjC,IAAI,QAAQ,MAAM,EAAE;gBAClB,IAAI,QAAQ,cAAc,EAAE;oBAC1B,QAAQ,MAAM,CAAC,KAAK,GAAG,QAAQ,KAAK;gBACtC;gBACA,aAAa,QAAQ,MAAM;YAC7B;YACA,OAAO,OAAO,SAAS,GAAG,IAAI,OAAO,MAAM;QAC7C;QAEA,SAAS;YACP,MAAM,OAAO,EAAE;YACf,IAAK,IAAI,UAAU,KAAK,YAAY,UAAU,UAAU,QAAQ,MAAM,CAAE;gBACtE,IAAI,QAAQ,SAAS,EAAE;oBACrB,KAAK,OAAO,CAAC,QAAQ,SAAS;gBAChC;YACF;YACA,KAAK,OAAO,CAAC,CAAA,OAAQ,QAAQ,QAAQ,CAAC;QACxC;QAEA,6DAA6D,GAC7D,IAAI,YAAY,CAAC;QAEjB;;;;;KAKC,GACD,SAAS,cAAc,eAAe,EAAE,KAAK;YAC3C,MAAM,SAAS,SAAS,KAAK,CAAC,EAAE;YAEhC,kDAAkD;YAClD,cAAc;YAEd,IAAI,UAAU,MAAM;gBAClB;gBACA,OAAO;YACT;YAEA,qEAAqE;YACrE,+FAA+F;YAC/F,oDAAoD;YACpD,+DAA+D;YAC/D,IAAI,UAAU,IAAI,KAAK,WAAW,MAAM,IAAI,KAAK,SAAS,UAAU,KAAK,KAAK,MAAM,KAAK,IAAI,WAAW,IAAI;gBAC1G,sFAAsF;gBACtF,cAAc,gBAAgB,KAAK,CAAC,MAAM,KAAK,EAAE,MAAM,KAAK,GAAG;gBAC/D,IAAI,CAAC,WAAW;oBACd,2BAA2B,GAC3B,MAAM,MAAM,IAAI,MAAM;oBACtB,IAAI,YAAY,GAAG;oBACnB,IAAI,OAAO,GAAG,UAAU,IAAI;oBAC5B,MAAM;gBACR;gBACA,OAAO;YACT;YACA,YAAY;YAEZ,IAAI,MAAM,IAAI,KAAK,SAAS;gBAC1B,OAAO,aAAa;YACtB,OAAO,IAAI,MAAM,IAAI,KAAK,aAAa,CAAC,gBAAgB;gBACtD,+CAA+C;gBAC/C,2BAA2B,GAC3B,MAAM,MAAM,IAAI,MAAM,qBAAqB,SAAS,iBAAiB,CAAC,IAAI,SAAS,IAAI,WAAW,IAAI;gBACtG,IAAI,IAAI,GAAG;gBACX,MAAM;YACR,OAAO,IAAI,MAAM,IAAI,KAAK,OAAO;gBAC/B,MAAM,YAAY,WAAW;gBAC7B,IAAI,cAAc,UAAU;oBAC1B,OAAO;gBACT;YACF;YAEA,0EAA0E;YAC1E,sEAAsE;YACtE,8CAA8C;YAC9C,IAAI,MAAM,IAAI,KAAK,aAAa,WAAW,IAAI;gBAC7C,iDAAiD;gBACjD,OAAO;YACT;YAEA,uEAAuE;YACvE,oEAAoE;YACpE,kEAAkE;YAClE,aAAa;YACb,IAAI,aAAa,UAAU,aAAa,MAAM,KAAK,GAAG,GAAG;gBACvD,MAAM,MAAM,IAAI,MAAM;gBACtB,MAAM;YACR;YAEA;;;;;;;;;;MAUA,GAEA,cAAc;YACd,OAAO,OAAO,MAAM;QACtB;QAEA,MAAM,WAAW,YAAY;QAC7B,IAAI,CAAC,UAAU;YACb,MAAM,mBAAmB,OAAO,CAAC,MAAM;YACvC,MAAM,IAAI,MAAM,wBAAwB,eAAe;QACzD;QAEA,MAAM,KAAK,gBAAgB,UAAU;YAAE;QAAQ;QAC/C,IAAI,SAAS;QACb,yBAAyB,GACzB,IAAI,MAAM,gBAAgB;QAC1B,sCAAsC,GACtC,MAAM,gBAAgB,CAAC,GAAG,uCAAuC;QACjE,MAAM,UAAU,IAAI,QAAQ,SAAS,CAAC;QACtC;QACA,IAAI,aAAa;QACjB,IAAI,YAAY;QAChB,IAAI,QAAQ;QACZ,IAAI,aAAa;QACjB,IAAI,2BAA2B;QAE/B,IAAI;YACF,IAAI,OAAO,CAAC,WAAW;YAEvB,OAAS;gBACP;gBACA,IAAI,0BAA0B;oBAC5B,kDAAkD;oBAClD,mCAAmC;oBACnC,2BAA2B;gBAC7B,OAAO;oBACL,IAAI,OAAO,CAAC,WAAW;gBACzB;gBACA,IAAI,OAAO,CAAC,SAAS,GAAG;gBAExB,MAAM,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;gBAC/B,iEAAiE;gBAEjE,IAAI,CAAC,OAAO;gBAEZ,MAAM,cAAc,gBAAgB,SAAS,CAAC,OAAO,MAAM,KAAK;gBAChE,MAAM,iBAAiB,cAAc,aAAa;gBAClD,QAAQ,MAAM,KAAK,GAAG;YACxB;YACA,cAAc,gBAAgB,MAAM,CAAC;YACrC,QAAQ,aAAa;YACrB,QAAQ,QAAQ;YAChB,SAAS,QAAQ,MAAM;YAEvB,OAAO;gBACL,qDAAqD;gBACrD,+BAA+B;gBAC/B,WAAW,KAAK,KAAK,CAAC;gBACtB,OAAO;gBACP,UAAU;gBACV,SAAS;gBACT,SAAS;gBACT,KAAK;YACP;QACF,EAAE,OAAO,KAAK;YACZ,IAAI,IAAI,OAAO,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY;gBAClD,OAAO;oBACL,SAAS;oBACT,WAAW;wBACT,KAAK,IAAI,OAAO;wBAChB,SAAS,gBAAgB,KAAK,CAAC,QAAQ,KAAK,QAAQ;wBACpD,MAAM,IAAI,IAAI;oBAChB;oBACA,OAAO;oBACP,WAAW;oBACX,OAAO,SAAS;oBAChB,SAAS;gBACX;YACF,OAAO,IAAI,WAAW;gBACpB,OAAO;oBACL,SAAS;oBACT,WAAW;oBACX,OAAO,SAAS;oBAChB,SAAS;oBACT,UAAU;oBACV,KAAK;oBACL,aAAa;gBACf;YACF,OAAO;gBACL,MAAM;YACR;QACF;IACF;IAEA;;;;;;GAMC,GACD,SAAS,wBAAwB,IAAI;QACnC,MAAM,SAAS;YACb,WAAW;YACX,SAAS,IAAI,QAAQ,SAAS,CAAC;YAC/B,OAAO,SAAS;YAChB,SAAS;YACT,KAAK;QACP;QACA,OAAO,OAAO,CAAC,OAAO,CAAC;QACvB,OAAO;IACT;IAEA;;;;;;;;;;;;;EAaA,GACA,SAAS,cAAc,IAAI,EAAE,cAAc;QACzC,iBAAiB,kBAAkB,QAAQ,SAAS,IAAI,OAAO,IAAI,CAAC;QACpE,MAAM,YAAY,wBAAwB;QAE1C,MAAM,UAAU,eAAe,MAAM,CAAC,aAAa,MAAM,CAAC,eAAe,GAAG,CAAC,CAAA,OAC3E,WAAW,MAAM,MAAM;QAEzB,QAAQ,OAAO,CAAC,YAAY,gCAAgC;QAE5D,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAC,GAAG;YAC9B,yBAAyB;YACzB,IAAI,EAAE,SAAS,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,GAAG,EAAE,SAAS;YAEjE,4CAA4C;YAC5C,6DAA6D;YAC7D,IAAI,EAAE,QAAQ,IAAI,EAAE,QAAQ,EAAE;gBAC5B,IAAI,YAAY,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE;oBACrD,OAAO;gBACT,OAAO,IAAI,YAAY,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE;oBAC5D,OAAO,CAAC;gBACV;YACF;YAEA,mEAAmE;YACnE,uEAAuE;YACvE,0EAA0E;YAC1E,4BAA4B;YAC5B,OAAO;QACT;QAEA,MAAM,CAAC,MAAM,WAAW,GAAG;QAE3B,gCAAgC,GAChC,MAAM,SAAS;QACf,OAAO,WAAW,GAAG;QAErB,OAAO;IACT;IAEA;;;;;;;;EAQA,GACA,SAAS,UAAU,IAAI;QACrB,IAAI,CAAC,CAAC,QAAQ,UAAU,IAAI,QAAQ,KAAK,GAAG;YAC1C,OAAO;QACT;QAEA,OAAO,KAAK,OAAO,CAAC,aAAa,CAAA;YAC/B,IAAI,UAAU,MAAM;gBAClB,OAAO,QAAQ,KAAK,GAAG,SAAS;YAClC,OAAO,IAAI,QAAQ,UAAU,EAAE;gBAC7B,OAAO,MAAM,OAAO,CAAC,OAAO,QAAQ,UAAU;YAChD;YACA,OAAO;QACT;IACF;IAEA;;;;;;GAMC,GACD,SAAS,gBAAgB,OAAO,EAAE,WAAW,EAAE,UAAU;QACvD,MAAM,WAAW,cAAc,OAAO,CAAC,YAAY,GAAG;QAEtD,QAAQ,SAAS,CAAC,GAAG,CAAC;QACtB,IAAI,UAAU,QAAQ,SAAS,CAAC,GAAG,CAAC;IACtC;IAEA,uBAAuB,GACvB,MAAM,WAAW;QACf,2BAA2B,CAAC,EAAE,EAAE,EAAE;YAChC,IAAI,QAAQ,KAAK,EAAE;gBACjB,GAAG,SAAS,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,cAAc;YACvE;QACF;QACA,0BAA0B,CAAC,EAAE,MAAM,EAAE;YACnC,IAAI,QAAQ,KAAK,EAAE;gBACjB,OAAO,KAAK,GAAG,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO;YAC7C;QACF;IACF;IAEA,MAAM,iBAAiB;IACvB,uBAAuB,GACvB,MAAM,mBAAmB;QACvB,0BAA0B,CAAC,EAAE,MAAM,EAAE;YACnC,IAAI,QAAQ,UAAU,EAAE;gBACtB,OAAO,KAAK,GAAG,OAAO,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,IACnD,EAAE,OAAO,CAAC,OAAO,QAAQ,UAAU;YAEvC;QACF;IACF;IAEA;;;;;EAKA,GACA,SAAS,iBAAiB,OAAO;QAC/B,sBAAsB,GACtB,IAAI,OAAO;QACX,MAAM,WAAW,cAAc;QAE/B,IAAI,mBAAmB,WAAW;QAElC,sBAAsB;QACtB,KAAK,2BACH;YAAE,IAAI;YAAS,UAAU;QAAS;QAEpC,OAAO;QACP,MAAM,OAAO,KAAK,WAAW;QAC7B,MAAM,SAAS,WAAW,UAAU,MAAM;YAAE;YAAU,gBAAgB;QAAK,KAAK,cAAc;QAE9F,sBAAsB;QACtB,KAAK,0BAA0B;YAAE,IAAI;YAAS;YAAQ;QAAK;QAE3D,QAAQ,SAAS,GAAG,OAAO,KAAK;QAChC,gBAAgB,SAAS,UAAU,OAAO,QAAQ;QAClD,QAAQ,MAAM,GAAG;YACf,UAAU,OAAO,QAAQ;YACzB,iCAAiC;YACjC,IAAI,OAAO,SAAS;YACpB,WAAW,OAAO,SAAS;QAC7B;QACA,IAAI,OAAO,WAAW,EAAE;YACtB,QAAQ,WAAW,GAAG;gBACpB,UAAU,OAAO,WAAW,CAAC,QAAQ;gBACrC,iCAAiC;gBACjC,IAAI,OAAO,WAAW,CAAC,SAAS;gBAChC,WAAW,OAAO,WAAW,CAAC,SAAS;YACzC;QACF;IACF;IAEA;;;;GAIC,GACD,SAAS,UAAU,WAAW;QAC5B,IAAI,YAAY,KAAK,EAAE;YACrB,WAAW,UAAU;YACrB,WAAW,UAAU;QACvB;QACA,UAAU,UAAU,SAAS;IAC/B;IAEA;;;;GAIC,GACD,+BAA+B;IAC/B,MAAM,mBAAmB;QACvB,IAAI,iBAAiB,MAAM,EAAE;QAC7B,iBAAiB,MAAM,GAAG;QAE1B,WAAW,UAAU;QAErB,MAAM,SAAS,SAAS,gBAAgB,CAAC;QACzC,OAAO,OAAO,CAAC;IACjB;IAEA,4CAA4C;IAC5C,+BAA+B;IAC/B,SAAS;QACP,WAAW,UAAU;QACrB,iBAAiB;IACnB;IAEA,IAAI,iBAAiB;IAErB;;GAEC,GACD,SAAS;QACP,oDAAoD;QACpD,IAAI,SAAS,UAAU,KAAK,WAAW;YACrC,iBAAiB;YACjB;QACF;QAEA,MAAM,SAAS,SAAS,gBAAgB,CAAC;QACzC,OAAO,OAAO,CAAC;IACjB;IAEA,SAAS;QACP,6DAA6D;QAC7D,IAAI,gBAAgB;IACtB;IAEA,8CAA8C;IAC9C,IAAI,OAAO,WAAW,eAAe,OAAO,gBAAgB,EAAE;QAC5D,OAAO,gBAAgB,CAAC,oBAAoB,MAAM;IACpD;IAEA;;;;;GAKC,GACD,SAAS,iBAAiB,YAAY,EAAE,kBAAkB;QACxD,IAAI,OAAO;QACX,IAAI;YACF,OAAO,mBAAmB;QAC5B,EAAE,OAAO,SAAS;YAChB,MAAM,wDAAwD,OAAO,CAAC,MAAM;YAC5E,qBAAqB;YACrB,IAAI,CAAC,WAAW;gBAAE,MAAM;YAAS,OAAO;gBAAE,MAAM;YAAU;YAC1D,qEAAqE;YACrE,qEAAqE;YACrE,qEAAqE;YACrE,qBAAqB;YACrB,OAAO;QACT;QACA,mEAAmE;QACnE,IAAI,CAAC,KAAK,IAAI,EAAE,KAAK,IAAI,GAAG;QAC5B,SAAS,CAAC,aAAa,GAAG;QAC1B,KAAK,aAAa,GAAG,mBAAmB,IAAI,CAAC,MAAM;QAEnD,IAAI,KAAK,OAAO,EAAE;YAChB,gBAAgB,KAAK,OAAO,EAAE;gBAAE;YAAa;QAC/C;IACF;IAEA;;;;GAIC,GACD,SAAS,mBAAmB,YAAY;QACtC,OAAO,SAAS,CAAC,aAAa;QAC9B,KAAK,MAAM,SAAS,OAAO,IAAI,CAAC,SAAU;YACxC,IAAI,OAAO,CAAC,MAAM,KAAK,cAAc;gBACnC,OAAO,OAAO,CAAC,MAAM;YACvB;QACF;IACF;IAEA;;GAEC,GACD,SAAS;QACP,OAAO,OAAO,IAAI,CAAC;IACrB;IAEA;;;;;;;;EAQA,GACA,SAAS,gBAAgB,IAAI;QAC3B,WAAW,UAAU;QACrB,WAAW,UAAU;QAErB,MAAM,OAAO,YAAY;QACzB,IAAI,MAAM;YAAE,OAAO;QAAM;QAEzB,MAAM,MAAM,IAAI,MAAM,mDAAmD,OAAO,CAAC,MAAM;QACvF,MAAM;IACR;IAEA;;;GAGC,GACD,SAAS,YAAY,IAAI;QACvB,OAAO,CAAC,QAAQ,EAAE,EAAE,WAAW;QAC/B,OAAO,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC;IACpD;IAEA;;;;GAIC,GACD,SAAS,gBAAgB,SAAS,EAAE,EAAE,YAAY,EAAE;QAClD,IAAI,OAAO,cAAc,UAAU;YACjC,YAAY;gBAAC;aAAU;QACzB;QACA,UAAU,OAAO,CAAC,CAAA;YAAW,OAAO,CAAC,MAAM,WAAW,GAAG,GAAG;QAAc;IAC5E;IAEA;;;GAGC,GACD,SAAS,cAAc,IAAI;QACzB,MAAM,OAAO,YAAY;QACzB,OAAO,QAAQ,CAAC,KAAK,iBAAiB;IACxC;IAEA;;;;GAIC,GACD,SAAS,iBAAiB,MAAM;QAC9B,wBAAwB;QACxB,IAAI,MAAM,CAAC,wBAAwB,IAAI,CAAC,MAAM,CAAC,0BAA0B,EAAE;YACzE,MAAM,CAAC,0BAA0B,GAAG,CAAC;gBACnC,MAAM,CAAC,wBAAwB,CAC7B,OAAO,MAAM,CAAC;oBAAE,OAAO,KAAK,EAAE;gBAAC,GAAG;YAEtC;QACF;QACA,IAAI,MAAM,CAAC,uBAAuB,IAAI,CAAC,MAAM,CAAC,yBAAyB,EAAE;YACvE,MAAM,CAAC,yBAAyB,GAAG,CAAC;gBAClC,MAAM,CAAC,uBAAuB,CAC5B,OAAO,MAAM,CAAC;oBAAE,OAAO,KAAK,EAAE;gBAAC,GAAG;YAEtC;QACF;IACF;IAEA;;GAEC,GACD,SAAS,UAAU,MAAM;QACvB,iBAAiB;QACjB,QAAQ,IAAI,CAAC;IACf;IAEA;;;;GAIC,GACD,SAAS,KAAK,KAAK,EAAE,IAAI;QACvB,MAAM,KAAK;QACX,QAAQ,OAAO,CAAC,SAAS,MAAM;YAC7B,IAAI,MAAM,CAAC,GAAG,EAAE;gBACd,MAAM,CAAC,GAAG,CAAC;YACb;QACF;IACF;IAEA;;;;;EAKA,GACA,SAAS,mBAAmB,GAAG;QAC7B,WAAW,UAAU;QACrB,WAAW,UAAU;QAErB,OAAO,UAAU;IACnB;IAEA;;;GAGC,GACD,SAAS,wBAAwB,EAAE;QACjC,WAAW,UAAU;QACrB,WAAW,UAAU;QAErB,OAAO,iBAAiB;IAC1B;IAEA,wBAAwB,GACxB,OAAO,MAAM,CAAC,MAAM;QAClB;QACA;QACA;QACA,WAAW;QACX;QACA,4BAA4B;QAC5B,gBAAgB;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,SAAS;QACT;QACA,yBAAyB;QACzB,WAAW,eAAe,MAAM,SAAS;IAC3C;IAEA,KAAK,SAAS,GAAG;QAAa,YAAY;IAAO;IACjD,KAAK,QAAQ,GAAG;QAAa,YAAY;IAAM;IAC/C,KAAK,aAAa,GAAG;IAErB,IAAK,MAAM,OAAO,MAAO;QACvB,aAAa;QACb,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU;YAClC,aAAa;YACb,cAAc,KAAK,CAAC,IAAI;QAC1B;IACF;IAEA,kDAAkD;IAClD,OAAO,MAAM,CAAC,MAAM;IAEpB,iEAAiE;IACjE,KAAK,SAAS,CAAC,WAAW,8BAA8B;IACxD,KAAK,SAAS,CAAC;IACf,KAAK,SAAS,CAAC;IACf,OAAO;AACT;AAEA,0CAA0C;AAC1C,IAAI,YAAY,KAAK,CAAC;AAEtB,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}]}