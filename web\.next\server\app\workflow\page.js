const CHUNK_PUBLIC_PATH = "server/app/workflow/page.js";
const runtime = require("../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/32708_next_dist_e5c81a21._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__4c58c055._.js");
runtime.loadChunk("server/chunks/ssr/src_app_a4430781._.js");
runtime.loadChunk("server/chunks/ssr/messages_8bbaac7a._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_038a93e4._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__8131d02e._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_a1a0be1b._.js");
runtime.loadChunk("server/chunks/ssr/32708_next_dist_client_components_forbidden-error_e9b7bd4f.js");
runtime.loadChunk("server/chunks/ssr/32708_next_dist_client_components_unauthorized-error_af4164b8.js");
runtime.loadChunk("server/chunks/ssr/32708_next_dist_781f55de._.js");
runtime.loadChunk("server/chunks/ssr/_e08d1933._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/workflow/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/esm/build/templates/app-page.js?page=/workflow/page { METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/src/app/workflow/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/esm/build/templates/app-page.js?page=/workflow/page { METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/src/app/workflow/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
