"""
后台任务队列管理器
用于管理 LLM 生成任务的顺序执行
"""

import asyncio
import logging
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Callable, Any
from uuid import uuid4
from dataclasses import dataclass, field
from .llm_api import generate_scenes_for_api, generate_images_for_api, generate_videos_for_api, generate_subtitle_with_gemini, generate_audio_from_text, delete_from_oss 

logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 执行失败
    CANCELLED = "cancelled"  # 已取消

class TaskType(Enum):
    GENERATE_SCENES = "generate_scenes"
    GENERATE_IMAGES = "generate_images"
    GENERATE_VIDEOS = "generate_videos"
    GENERATE_SUBTITLES = "generate_subtitles"
    GENERATE_CHARACTER_IMAGE = "generate_character_image"
    REGENERATE_SCENE_IMAGE = "regenerate_scene_image"
    REGENERATE_VIDEO = "regenerate_video"
    REGENERATE_AUDIO = "regenerate_audio"

@dataclass
class Task:
    """任务数据结构"""
    task_id: str
    task_type: TaskType
    params: Dict[str, Any]
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    result: Optional[Any] = None
    progress_callback: Optional[Callable] = None

class TaskQueue:
    """任务队列管理器"""
    
    def __init__(self):
        self.tasks: Dict[str, Task] = {}
        self.pending_queue: List[str] = []  # 待执行任务队列
        self.running_task: Optional[str] = None  # 当前执行的任务
        self.is_processing = False
        self.progress_store: Dict[str, Dict] = {}  # 进度存储
        
    async def submit_task(
        self, 
        task_type: TaskType, 
        params: Dict[str, Any],
        progress_callback: Optional[Callable] = None
    ) -> str:
        """提交任务到队列"""
        task_id = str(uuid4())
        
        task = Task(
            task_id=task_id,
            task_type=task_type,
            params=params,
            progress_callback=progress_callback
        )
        
        self.tasks[task_id] = task
        self.pending_queue.append(task_id)
        
        # 初始化进度
        self.progress_store[task_id] = {
            "step": 0,
            "total": 1,
            "message": "任务已提交，等待执行...",
            "timestamp": datetime.now().isoformat(),
            "status": TaskStatus.PENDING.value
        }
        
        logger.info(f"Task {task_id} ({task_type.value}) submitted to queue")
        
        # 启动队列处理
        if not self.is_processing:
            asyncio.create_task(self._process_queue())
            
        return task_id
    
    async def _process_queue(self):
        """处理队列中的任务"""
        if self.is_processing:
            return
            
        self.is_processing = True
        logger.info("Task queue processing started")
        
        try:
            while self.pending_queue:
                task_id = self.pending_queue.pop(0)
                task = self.tasks.get(task_id)
                
                if not task:
                    continue
                    
                self.running_task = task_id
                await self._execute_task(task)
                self.running_task = None
                
        except Exception as e:
            logger.error(f"Error in queue processing: {str(e)}")
        finally:
            self.is_processing = False
            logger.info("Task queue processing stopped")
    
    async def _execute_task(self, task: Task):
        """执行单个任务"""
        task_id = task.task_id
        
        try:
            # 更新任务状态为运行中
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            
            # 更新进度
            await self._update_progress(task_id, 0, 1, "任务开始执行...")
            
            logger.info(f"Executing task {task_id} ({task.task_type.value})")
            
            # 根据任务类型执行相应的处理
            result = await self._dispatch_task(task)
            
            # 任务完成
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.result = result
            
            # 更新进度为完成
            await self._update_progress(task_id, 1, 1, "任务执行完成！", completed=True)
            
            logger.info(f"Task {task_id} completed successfully")
            
        except Exception as e:
            # 任务失败
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error_message = str(e)
            
            # 更新进度为失败
            await self._update_progress(task_id, 0, 1, f"任务执行失败: {str(e)}", error=str(e))
            
            logger.error(f"Task {task_id} failed: {str(e)}")
    
    async def _dispatch_task(self, task: Task) -> Any:
        """根据任务类型分发执行"""

        
        task_type = task.task_type
        params = task.params
        
        # 创建进度回调函数
        async def progress_callback(step: int, total: int, message: str):
            await self._update_progress(task.task_id, step, total, message)
        
        if task_type == TaskType.GENERATE_SCENES:
            return await self._execute_generate_scenes(params, progress_callback)
        elif task_type == TaskType.GENERATE_IMAGES:
            return await self._execute_generate_images(params, progress_callback)
        elif task_type == TaskType.GENERATE_VIDEOS:
            return await self._execute_generate_videos(params, progress_callback)
        elif task_type == TaskType.GENERATE_SUBTITLES:
            return await self._execute_generate_subtitles(params, progress_callback)
        elif task_type == TaskType.GENERATE_CHARACTER_IMAGE:
            return await self._execute_generate_character_image(params, progress_callback)
        elif task_type == TaskType.REGENERATE_SCENE_IMAGE:
            return await self._execute_regenerate_scene_image(params, progress_callback)
        elif task_type == TaskType.REGENERATE_VIDEO:
            return await self._execute_regenerate_video(params, progress_callback)
        elif task_type == TaskType.REGENERATE_AUDIO:
            return await self._execute_regenerate_audio(params, progress_callback)
        else:
            raise ValueError(f"Unknown task type: {task_type}")
    
    async def _update_progress(
        self, 
        task_id: str, 
        step: int, 
        total: int, 
        message: str, 
        completed: bool = False,
        error: Optional[str] = None
    ):
        """更新任务进度"""
        progress_data = {
            "step": step,
            "total": total,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        
        if completed:
            progress_data["type"] = "complete"
        
        if error:
            progress_data["error"] = error
            
        self.progress_store[task_id] = progress_data
        
        # 如果任务有自定义进度回调，也调用它
        task = self.tasks.get(task_id)
        if task and task.progress_callback:
            try:
                await task.progress_callback(step, total, message)
            except Exception as e:
                logger.error(f"Error in custom progress callback: {str(e)}")
    
    def get_task_status(self, task_id: str) -> Optional[Task]:
        """获取任务状态"""
        return self.tasks.get(task_id)
    
    def get_progress(self, task_id: str) -> Optional[Dict]:
        """获取任务进度"""
        return self.progress_store.get(task_id)
    
    def get_queue_status(self) -> Dict:
        """获取队列状态"""
        return {
            "pending_count": len(self.pending_queue),
            "running_task": self.running_task,
            "is_processing": self.is_processing,
            "total_tasks": len(self.tasks)
        }
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        task = self.tasks.get(task_id)
        if not task:
            return False
            
        if task.status == TaskStatus.PENDING:
            # 从队列中移除
            if task_id in self.pending_queue:
                self.pending_queue.remove(task_id)
            task.status = TaskStatus.CANCELLED
            await self._update_progress(task_id, 0, 1, "任务已取消", error="任务已取消")
            return True
        elif task.status == TaskStatus.RUNNING:
            # 正在运行的任务无法取消
            return False
        else:
            # 已完成或已失败的任务无法取消
            return False

    # ==================== 具体任务执行方法 ====================

    async def _execute_generate_scenes(self, params: Dict[str, Any], progress_callback: Callable) -> Any:
        """执行生成场景任务"""
        from .database import get_db
        from .models import VideoCreative, VideoScene
        from .schemas import VideoSceneResponse
        from sqlalchemy.orm import Session

        creative_id = params["creative_id"]

        # 获取数据库会话
        db = next(get_db())

        try:
            await progress_callback(1, 4, "正在获取创意信息...")

            # 获取创意信息
            creative = db.query(VideoCreative).filter(VideoCreative.id == creative_id).first()
            if not creative:
                raise Exception("视频创意不存在")

            await progress_callback(2, 4, "正在生成场景描述...")

            # 调用场景生成 API
            from .llm_api import generate_scenes_for_api
            scenes_data = await generate_scenes_for_api(
                creative.creative,
                creative.art_style,
                creative.scene_cnt,
                creative.character_prompt
            )

            await progress_callback(3, 4, "正在保存场景数据...")

            # 将生成的场景存入数据库
            created_scenes = []
            for scene_data in scenes_data:
                # 创建场景记录
                db_scene = VideoScene(
                    creative_id=creative_id,
                    scene=scene_data.get("scene", ""),
                    prompt=scene_data.get("prompt", "")
                )
                db.add(db_scene)
                db.commit()
                db.refresh(db_scene)
                created_scenes.append(db_scene)

            await progress_callback(4, 4, "场景生成完成！")

            return {
                "message": f"成功生成 {len(created_scenes)} 个场景",
                "scenes": [VideoSceneResponse.model_validate(scene).model_dump() for scene in created_scenes]
            }

        finally:
            db.close()

    async def _execute_generate_images(self, params: Dict[str, Any], progress_callback: Callable) -> Any:
        """执行生成图片任务"""
        from .database import get_db
        from .models import VideoCreative, VideoScene
        from sqlalchemy.orm import Session

        creative_id = params["creative_id"]

        # 获取数据库会话
        db = next(get_db())

        try:
            # 获取该创意的所有场景
            scenes = db.query(VideoScene).filter(VideoScene.creative_id == creative_id).all()

            if not scenes:
                raise Exception("该创意还没有场景，请先生成场景")

            updated_scenes = []
            for i, scene in enumerate(scenes):
                await progress_callback(i + 1, len(scenes), f"正在生成第 {i+1}/{len(scenes)} 张图片...")

                # 调用图片生成 API
                image_path = await generate_images_for_api(
                    prompt=scene.prompt,
                    scene_id=scene.id
                )

                # 更新场景的图片路径
                scene.image = image_path
                db.commit()
                db.refresh(scene)
                updated_scenes.append(scene)

            return {"message": f"成功生成 {len(updated_scenes)} 个图片", "scenes": updated_scenes}

        finally:
            db.close()

    async def _execute_generate_videos(self, params: Dict[str, Any], progress_callback: Callable) -> Any:
        """执行生成视频任务"""
        from .database import get_db
        from .models import VideoCreative, VideoScene, VideoVideo
        from sqlalchemy.orm import Session

        creative_id = params["creative_id"]

        # 获取数据库会话
        db = next(get_db())

        try:
            # 获取该创意的所有场景（需要有图片路径）
            scenes = db.query(VideoScene).filter(
                VideoScene.creative_id == creative_id,
                VideoScene.image.isnot(None)
            ).all()

            if not scenes:
                raise Exception("该创意还没有图片，请先生成图片")

            created_videos = []
            for i, scene in enumerate(scenes):
                await progress_callback(i + 1, len(scenes), f"正在生成第 {i+1}/{len(scenes)} 个视频...")

                # 调用视频生成 API
                video_path = await generate_videos_for_api(
                    prompt=scene.prompt,
                    img_url=scene.image,
                    video_id=scene.id
                )

                # 创建或更新视频记录
                existing_video = db.query(VideoVideo).filter(VideoVideo.scene_id == scene.id).first()
                if existing_video:
                    existing_video.prompt = scene.prompt
                    existing_video.video = video_path
                    db.commit()
                    db.refresh(existing_video)
                    created_videos.append(existing_video)
                else:
                    db_video = VideoVideo(
                        scene_id=scene.id,
                        prompt=scene.prompt,
                        video=video_path
                    )
                    db.add(db_video)
                    db.commit()
                    db.refresh(db_video)
                    created_videos.append(db_video)

            return {"message": f"成功生成 {len(created_videos)} 个视频", "videos": created_videos}

        finally:
            db.close()

    async def _execute_generate_subtitles(self, params: Dict[str, Any], progress_callback: Callable) -> Any:
        """执行生成字幕任务"""
        from .database import get_db
        from .models import VideoCreative, VideoScene, VideoVideo, VideoSubtitle
        from sqlalchemy.orm import Session

        creative_id = params["creative_id"]

        # 获取数据库会话
        db = next(get_db())

        try:
            # 获取该创意的所有视频
            videos = db.query(VideoVideo).join(VideoScene).filter(
                VideoScene.creative_id == creative_id
            ).all()

            if not videos:
                raise Exception("该创意还没有视频，请先生成视频")

            created_subtitles = []
            for i, video in enumerate(videos):
                # 生成字幕
                overall_step = i * 2 + 1
                overall_total = len(videos) * 2
                await progress_callback(overall_step, overall_total, f"正在生成第 {i+1}/{len(videos)} 个字幕...")

                # 获取关联的场景信息
                scene = db.query(VideoScene).filter(VideoScene.id == video.scene_id).first()
                scene_description = scene.scene if scene else ""

                # 使用 Gemini 生成字幕文本
                subtitle_text = await generate_subtitle_with_gemini(
                    video.prompt or "",
                    scene_description
                )

                # 创建或更新字幕记录（先不设置音频）
                existing_subtitle = db.query(VideoSubtitle).filter(VideoSubtitle.video_id == video.id).first()
                if existing_subtitle:
                    # 删除旧音频文件
                    if existing_subtitle.audio:
                        delete_from_oss(existing_subtitle.audio)

                    existing_subtitle.subtitle = subtitle_text
                    existing_subtitle.audio = None
                    db.commit()
                    db.refresh(existing_subtitle)

                    # 生成音频
                    audio_step = i * 2 + 2
                    await progress_callback(audio_step, overall_total, f"正在生成第 {i+1}/{len(videos)} 个音频...")

                    audio_url = await generate_audio_from_text(subtitle_text, existing_subtitle.id)
                    existing_subtitle.audio = audio_url
                    db.commit()
                    db.refresh(existing_subtitle)
                    created_subtitles.append(existing_subtitle)
                else:
                    # 创建新的字幕记录
                    db_subtitle = VideoSubtitle(
                        video_id=video.id,
                        subtitle=subtitle_text,
                        audio=None
                    )
                    db.add(db_subtitle)
                    db.commit()
                    db.refresh(db_subtitle)

                    # 生成音频
                    audio_step = i * 2 + 2
                    await progress_callback(audio_step, overall_total, f"正在生成第 {i+1}/{len(videos)} 个音频...")

                    audio_url = await generate_audio_from_text(subtitle_text, db_subtitle.id)
                    db_subtitle.audio = audio_url
                    db.commit()
                    db.refresh(db_subtitle)
                    created_subtitles.append(db_subtitle)

            return {"message": f"成功生成 {len(created_subtitles)} 个字幕", "subtitles": created_subtitles}

        finally:
            db.close()

    async def _execute_regenerate_scene_image(self, params: Dict[str, Any], progress_callback: Callable) -> Any:
        """执行重新生成场景图片任务"""
        from .database import get_db
        from .models import VideoScene
        from sqlalchemy.orm import Session

        scene_id = params["scene_id"]

        # 获取数据库会话
        db = next(get_db())

        try:
            await progress_callback(1, 3, "正在准备重新生成图片...")

            scene = db.query(VideoScene).filter(VideoScene.id == scene_id).first()
            if not scene:
                raise Exception("场景不存在")

            await progress_callback(2, 3, "正在生成新图片...")

            # 删除旧图片
            if scene.image:
                delete_from_oss(scene.image)

            new_image_path = await generate_images_for_api(
                prompt=scene.prompt,
                scene_id=scene.id
            )

            # 更新场景
            scene.image = new_image_path
            db.commit()
            db.refresh(scene)

            await progress_callback(3, 3, "图片重新生成完成！")

            return {"message": "成功重新生成图片", "scene": scene}

        finally:
            db.close()

    async def _execute_regenerate_video(self, params: Dict[str, Any], progress_callback: Callable) -> Any:
        """执行重新生成视频任务"""
        from .database import get_db
        from .models import VideoVideo, VideoScene
        from sqlalchemy.orm import Session

        video_id = params["video_id"]

        # 获取数据库会话
        db = next(get_db())

        try:
            await progress_callback(1, 3, "正在准备重新生成视频...")

            video = db.query(VideoVideo).filter(VideoVideo.id == video_id).first()
            if not video:
                raise Exception("视频不存在")

            scene = db.query(VideoScene).filter(VideoScene.id == video.scene_id).first()
            if not scene or not scene.image:
                raise Exception("关联的场景或图片不存在")

            await progress_callback(2, 3, "正在生成新视频...")

            # 删除旧视频
            if video.video:
                delete_from_oss(video.video)

            new_video_path = await generate_videos_for_api(
                prompt=video.prompt or scene.prompt,
                img_url=scene.image,
                video_id=video.id
            )

            # 更新视频
            video.video = new_video_path
            db.commit()
            db.refresh(video)

            await progress_callback(3, 3, "视频重新生成完成！")

            return {"message": "成功重新生成视频", "video": video}

        finally:
            db.close()

    async def _execute_regenerate_audio(self, params: Dict[str, Any], progress_callback: Callable) -> Any:
        """执行重新生成音频任务"""
        from .database import get_db
        from .models import VideoSubtitle
        from sqlalchemy.orm import Session

        subtitle_id = params["subtitle_id"]

        # 获取数据库会话
        db = next(get_db())

        try:
            await progress_callback(1, 3, "正在准备重新生成音频...")

            subtitle = db.query(VideoSubtitle).filter(VideoSubtitle.id == subtitle_id).first()
            if not subtitle:
                raise Exception("字幕不存在")

            await progress_callback(2, 3, "正在调用 SPARK TTS API 生成音频...")

            # 删除旧音频
            if subtitle.audio:
                delete_from_oss(subtitle.audio)

            # 使用字幕ID生成新音频
            subtitle_text = subtitle.subtitle or "默认字幕内容"

            new_audio_url = await generate_audio_from_text(subtitle_text, subtitle.id)

            # 更新字幕的音频路径
            subtitle.audio = new_audio_url
            db.commit()
            db.refresh(subtitle)

            await progress_callback(3, 3, "音频重新生成完成！")

            return {"message": "成功重新生成音频", "subtitle": subtitle}

        finally:
            db.close()

    async def _execute_generate_character_image(self, params: Dict[str, Any], progress_callback: Callable) -> Any:
        """执行生成主角图片任务"""
        from .database import get_db
        from .models import VideoCreative
        from sqlalchemy.orm import Session

        creative_id = params["creative_id"]

        # 获取数据库会话
        db = next(get_db())

        try:
            await progress_callback(1, 3, "正在获取创意信息...")

            creative = db.query(VideoCreative).filter(VideoCreative.id == creative_id).first()
            if not creative:
                raise Exception("视频创意不存在")

            if not creative.character_prompt:
                raise Exception("主角提示词不能为空")

            await progress_callback(2, 3, "正在生成主角图片...")

            # 调用主角图片生成 API
            from .llm_api import generate_character_image_for_api
            character_image_url = await generate_character_image_for_api(
                creative.character_prompt,
                creative_id
            )

            if not character_image_url:
                raise Exception("主角图片生成失败")

            # 更新创意的主角图片
            creative.character_image = character_image_url
            db.commit()
            db.refresh(creative)

            await progress_callback(3, 3, "主角图片生成完成！")

            return {"message": "成功生成主角图片", "character_image": character_image_url}

        finally:
            db.close()

# 全局任务队列实例
task_queue = TaskQueue()

# 导出进度存储，供路由使用
progress_store = task_queue.progress_store
