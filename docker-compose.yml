services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: video-flow-backend
    ports:
      - "8901:8901"
    env_file:
      - .env
    volumes:
      - ./conf.yaml:/app/conf.yaml:ro
    restart: unless-stopped
    networks:
      - video-flow-network

  frontend:
    build:
      context: ./web
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL
    container_name: video-flow-frontend
    ports:
      - "8902:8902"
    env_file:
      - .env
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - video-flow-network

networks:
    video-flow-network:
        driver: bridge
        ipam:
            driver: default
            config:
            - subnet: **********/24
              gateway: **********
              