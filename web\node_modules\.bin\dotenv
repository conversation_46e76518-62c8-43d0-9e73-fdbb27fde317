#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/dataset/video/video-flow/web/node_modules/.pnpm/dotenv-cli@8.0.0/node_modules/dotenv-cli/node_modules:/mnt/d/dataset/video/video-flow/web/node_modules/.pnpm/dotenv-cli@8.0.0/node_modules:/mnt/d/dataset/video/video-flow/web/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/dataset/video/video-flow/web/node_modules/.pnpm/dotenv-cli@8.0.0/node_modules/dotenv-cli/node_modules:/mnt/d/dataset/video/video-flow/web/node_modules/.pnpm/dotenv-cli@8.0.0/node_modules:/mnt/d/dataset/video/video-flow/web/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../dotenv-cli/cli.js" "$@"
else
  exec node  "$basedir/../dotenv-cli/cli.js" "$@"
fi
