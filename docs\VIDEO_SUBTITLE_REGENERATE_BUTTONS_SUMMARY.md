# 视频和字幕卡片重新生成按钮功能实现总结

## 功能概述

为视频卡片和字幕卡片添加了重新生成按钮，用户可以直接在卡片上快速重新生成视频或音频，提升操作便利性。

## 实现内容

### 1. 视频卡片重新生成按钮 ✅

#### 按钮位置和样式
在视频卡片的左上角添加了重新生成视频按钮：

```tsx
{/* 头部按钮区域 */}
<div className="absolute top-2 left-2">
  <Button
    variant="secondary"
    size="sm"
    onClick={() => handleRegenerateVideo(video.id)}
    disabled={regeneratingVideos.has(video.id)}
    title="重新生成视频"
    className="h-8 w-8 p-0"
  >
    {regeneratingVideos.has(video.id) ? (
      <Loader2 className="h-4 w-4 animate-spin" />
    ) : (
      <RefreshCw className="h-4 w-4" />
    )}
  </Button>
</div>
```

#### 视频生成遮罩
在重新生成过程中显示遮罩效果：

```tsx
{/* 重新生成视频遮罩 */}
{regeneratingVideos.has(video.id) && (
  <div className="absolute inset-0 bg-black/50 rounded-md flex items-center justify-center">
    <div className="text-white text-sm flex items-center gap-2">
      <Loader2 className="h-5 w-5 animate-spin" />
      重新生成中...
    </div>
  </div>
)}
```

#### 按钮特性
- **位置**：视频卡片左上角，不遮挡视频内容
- **图标**：使用 RefreshCw 图标表示重新生成
- **状态**：生成中时显示 Loader2 动画图标
- **禁用**：生成中时按钮禁用，防止重复点击
- **提示**：有 tooltip 提示"重新生成视频"
- **样式**：secondary 变体，8x8 尺寸，圆形按钮

### 2. 字幕卡片重新生成按钮 ✅

#### 按钮位置和样式
在字幕卡片的左上角添加了重新生成音频按钮：

```tsx
{/* 头部按钮区域 */}
<div className="absolute top-2 left-2">
  <Button
    variant="secondary"
    size="sm"
    onClick={() => handleRegenerateAudio(subtitle.id)}
    disabled={regeneratingAudios.has(subtitle.id)}
    title="重新生成音频"
    className="h-8 w-8 p-0"
  >
    {regeneratingAudios.has(subtitle.id) ? (
      <Loader2 className="h-4 w-4 animate-spin" />
    ) : (
      <RefreshCw className="h-4 w-4" />
    )}
  </Button>
</div>
```

#### 音频生成遮罩
在重新生成过程中显示遮罩效果：

```tsx
{/* 重新生成音频遮罩 */}
{regeneratingAudios.has(subtitle.id) && (
  <div className="absolute inset-0 bg-black/50 rounded-md flex items-center justify-center">
    <div className="text-white text-sm flex items-center gap-2">
      <Loader2 className="h-5 w-5 animate-spin" />
      重新生成音频中...
    </div>
  </div>
)}
```

#### 按钮特性
- **位置**：字幕卡片左上角，不遮挡字幕内容
- **图标**：使用 RefreshCw 图标表示重新生成
- **状态**：生成中时显示 Loader2 动画图标
- **禁用**：生成中时按钮禁用，防止重复点击
- **提示**：有 tooltip 提示"重新生成音频"
- **样式**：secondary 变体，8x8 尺寸，圆形按钮

### 3. 状态管理复用 ✅

#### 复用现有状态
重新生成功能复用了已有的状态管理：

```tsx
// 视频重新生成状态（已存在）
const [regeneratingVideos, setRegeneratingVideos] = useState<Set<number>>(new Set());

// 音频重新生成状态（已存在）
const [regeneratingAudios, setRegeneratingAudios] = useState<Set<number>>(new Set());

// 任务ID跟踪（已存在）
const [regenerationTaskIds, setRegenerationTaskIds] = useState<{[key: number]: string}>({});

// 重新生成函数（已存在）
const handleRegenerateVideo = async (videoId: number) => { ... };
const handleRegenerateAudio = async (subtitleId: number) => { ... };
```

#### 状态特性
- **多任务支持**：支持多个视频/音频同时重新生成
- **状态隔离**：每个视频/音频的状态独立管理
- **确认对话框**：操作前显示确认对话框
- **API 集成**：使用现有的重新生成 API

## UI 布局设计

### 视频卡片布局
```
┌─────────────────────────────────────────────────────┐
│ [🔄]                                    [Video #1]  │
│                                                     │
│                                                     │
│                   [视频播放器]                       │
│                                                     │
│                                                     │
└─────────────────────────────────────────────────────┘
│ Scene Title: 城市夜景                                │
│ Prompt: 一个繁华的城市夜景...                        │
│ [编辑提示词] [重新生成]                              │
│ [████████████████████████████████████] 75%         │ │
│ 正在重新生成视频...                                  │
└─────────────────────────────────────────────────────┘
```

### 字幕卡片布局
```
┌─────────────────────────────────────────────────────┐
│ [🔄]                                 [Subtitle #1]  │
│                                                     │
│                                                     │
│                   [字幕文本]                         │
│                                                     │
│                                                     │
└─────────────────────────────────────────────────────┘
│ From: 城市夜景                          [Video #1]  │
│ [音频播放器]                                         │
│ [编辑字幕] [重新生成音频]                             │
│ [████████████████████████████████████] 60%         │ │
│ 正在重新生成音频...                                  │
└─────────────────────────────────────────────────────┘
```

## 用户交互流程

### 1. 视频重新生成流程
1. **查看视频**：在 Videos 标签页查看视频列表
2. **点击重新生成按钮**：视频卡片左上角的 RefreshCw 图标
3. **确认操作**：显示确认对话框
4. **开始生成**：
   - 按钮变为动画图标并禁用
   - 视频上显示生成遮罩
   - 显示进度条
5. **查看进度**：实时显示重新生成进度
6. **完成生成**：
   - 自动刷新数据
   - 显示新生成的视频
   - 清理生成状态

### 2. 音频重新生成流程
1. **查看字幕**：在 Subtitles 标签页查看字幕列表
2. **点击重新生成按钮**：字幕卡片左上角的 RefreshCw 图标
3. **确认操作**：显示确认对话框
4. **开始生成**：
   - 按钮变为动画图标并禁用
   - 字幕区域显示生成遮罩
   - 显示进度条
5. **查看进度**：实时显示重新生成进度
6. **完成生成**：
   - 自动刷新数据
   - 显示新生成的音频
   - 清理生成状态

### 3. 多任务操作
1. **同时操作多个项目**：可以同时为多个视频/音频重新生成
2. **独立状态管理**：每个项目的生成状态独立
3. **不影响其他操作**：生成过程中可以进行其他操作

## 技术实现

### 1. 组件结构更新

#### 视频卡片组件
```tsx
<Card key={video.id} className="overflow-hidden">
  <div className="aspect-video bg-muted/50 relative">
    {/* 视频播放器 */}
    <video src={video.video} controls />
    
    {/* 重新生成遮罩 */}                                    {/* 新增 */}
    {regeneratingVideos.has(video.id) && (
      <div className="absolute inset-0 bg-black/50">
        <div>重新生成中...</div>
      </div>
    )}
    
    {/* 头部按钮 */}                                        {/* 新增 */}
    <div className="absolute top-2 left-2">
      <Button onClick={handleRegenerateVideo}>🔄</Button>
    </div>
    
    {/* 右上角标识 */}
    <div className="absolute top-2 right-2">
      <Badge>Video #{video.id}</Badge>
    </div>
  </div>
  
  <CardContent>
    {/* 视频信息和操作按钮 */}
    <div>Scene Title & Prompt</div>
    <div>[编辑提示词] [重新生成]</div>                      {/* 保留现有 */}
    {regenerating && <ProgressBar />}                      {/* 保留现有 */}
  </CardContent>
</Card>
```

#### 字幕卡片组件
```tsx
<Card key={subtitle.id} className="overflow-hidden">
  <div className="aspect-video bg-muted/50 relative">
    {/* 字幕文本 */}
    <div className="text-center">
      <p>{subtitle.subtitle}</p>
    </div>
    
    {/* 重新生成遮罩 */}                                    {/* 新增 */}
    {regeneratingAudios.has(subtitle.id) && (
      <div className="absolute inset-0 bg-black/50">
        <div>重新生成音频中...</div>
      </div>
    )}
    
    {/* 头部按钮 */}                                        {/* 新增 */}
    <div className="absolute top-2 left-2">
      <Button onClick={handleRegenerateAudio}>🔄</Button>
    </div>
    
    {/* 右上角标识 */}
    <div className="absolute top-2 right-2">
      <Badge>Subtitle #{subtitle.id}</Badge>
    </div>
  </div>
  
  <CardContent>
    {/* 字幕信息和操作按钮 */}
    <div>From: Scene & Video Info</div>
    <audio src={subtitle.audio} controls />
    <div>[编辑字幕] [重新生成音频]</div>                   {/* 保留现有 */}
    {regenerating && <ProgressBar />}                      {/* 保留现有 */}
  </CardContent>
</Card>
```

### 2. 状态管理
```tsx
// 重新生成状态
const [regeneratingVideos, setRegeneratingVideos] = useState<Set<number>>(new Set());
const [regeneratingAudios, setRegeneratingAudios] = useState<Set<number>>(new Set());
const [regenerationTaskIds, setRegenerationTaskIds] = useState<{[key: number]: string}>({});

// 按钮状态
const isRegeneratingVideo = regeneratingVideos.has(video.id);
const isRegeneratingAudio = regeneratingAudios.has(subtitle.id);

// 状态更新
const startVideoRegeneration = (videoId: number, taskId: string) => {
  setRegeneratingVideos(prev => new Set(prev).add(videoId));
  setRegenerationTaskIds(prev => ({ ...prev, [videoId]: taskId }));
};

const completeVideoRegeneration = (videoId: number) => {
  setRegeneratingVideos(prev => {
    const newSet = new Set(prev);
    newSet.delete(videoId);
    return newSet;
  });
  setRegenerationTaskIds(prev => {
    const newIds = { ...prev };
    delete newIds[videoId];
    return newIds;
  });
};
```

### 3. API 集成
```tsx
// API 调用（复用现有）
const { regenerateVideo, regenerateAudio } = useVideoAPI();

const performRegenerateVideo = async (videoId: number) => {
  try {
    setRegeneratingVideos(prev => new Set(prev).add(videoId));
    const result = await regenerateVideo(videoId);
    
    if (result.task_id) {
      setRegenerationTaskIds(prev => ({
        ...prev,
        [videoId]: result.task_id
      }));
    }
  } catch (error) {
    // 错误处理
    setRegeneratingVideos(prev => {
      const newSet = new Set(prev);
      newSet.delete(videoId);
      return newSet;
    });
  }
};
```

## 测试验证

### 测试脚本
提供了 `test_video_subtitle_regenerate_buttons.py` 脚本，测试：
- 创建创意并生成完整流程
- 重新生成视频功能
- 重新生成音频功能
- 前端集成功能
- UI 布局和交互

### 运行测试
```bash
python test_video_subtitle_regenerate_buttons.py
```

### 手动测试步骤
1. **创建创意并生成完整流程**
2. **在 Videos 标签页查看视频卡片**
3. **点击视频卡片左上角的重新生成按钮**
4. **确认操作并观察生成过程**：
   - 按钮状态变化
   - 视频遮罩效果
   - 进度条显示
5. **在 Subtitles 标签页查看字幕卡片**
6. **点击字幕卡片左上角的重新生成按钮**
7. **确认操作并观察生成过程**：
   - 按钮状态变化
   - 字幕遮罩效果
   - 进度条显示
8. **验证结果**：视频/音频更新成功

## 功能特性

### ✅ 用户体验优化
- **便捷操作**：直接在卡片上快速重新生成
- **直观反馈**：清晰的视觉状态反馈
- **进度跟踪**：实时显示生成进度
- **错误处理**：完善的错误处理机制

### ✅ 界面设计
- **一致性**：按钮样式与设计语言保持一致
- **可访问性**：有 tooltip 提示和键盘支持
- **响应式**：适配不同屏幕尺寸
- **性能**：状态管理高效，不影响其他功能

### ✅ 技术实现
- **代码复用**：复用现有的重新生成逻辑
- **状态管理**：清晰的状态管理和生命周期
- **API 集成**：与后端 API 完美集成
- **类型安全**：完整的 TypeScript 类型支持

## 总结

✅ **实现完成**：
- 在视频卡片左上角添加重新生成视频按钮
- 在字幕卡片左上角添加重新生成音频按钮
- 集成遮罩效果和进度跟踪
- 复用现有的状态管理和 API
- 提供良好的用户体验

🎯 **用户价值**：
- 提高操作效率，快速重新生成
- 直观的视觉反馈和进度显示
- 支持多项目同时操作
- 完整的错误处理和状态管理

🔧 **技术优势**：
- 代码复用，减少重复开发
- 一致的设计语言和交互模式
- 高效的状态管理
- 良好的可维护性和扩展性

现在用户可以直接在视频和字幕卡片上快速重新生成内容，大大提升了操作便利性！🚀
