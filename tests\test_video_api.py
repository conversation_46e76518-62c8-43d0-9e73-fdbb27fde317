#!/usr/bin/env python3
"""
测试视频API的脚本
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/video"

def test_creative_crud():
    """测试视频创意的CRUD操作"""
    print("=== 测试视频创意 CRUD ===")
    
    # 1. 创建创意
    creative_data = {
        "creative": "一个关于未来城市的科幻短片",
        "art_style": "赛博朋克"
    }
    
    print("1. 创建视频创意...")
    response = requests.post(f"{BASE_URL}/creatives", json=creative_data)
    if response.status_code == 200:
        creative = response.json()
        creative_id = creative["id"]
        print(f"   创建成功，ID: {creative_id}")
        print(f"   创意: {creative['creative']}")
    else:
        print(f"   创建失败: {response.status_code} - {response.text}")
        return None
    
    # 2. 获取创意列表
    print("2. 获取创意列表...")
    response = requests.get(f"{BASE_URL}/creatives")
    if response.status_code == 200:
        data = response.json()
        print(f"   获取成功，总数: {data['total']}")
    else:
        print(f"   获取失败: {response.status_code} - {response.text}")
    
    # 3. 获取单个创意
    print("3. 获取单个创意...")
    response = requests.get(f"{BASE_URL}/creatives/{creative_id}")
    if response.status_code == 200:
        creative = response.json()
        print(f"   获取成功: {creative['creative']}")
    else:
        print(f"   获取失败: {response.status_code} - {response.text}")
    
    # 4. 更新创意
    print("4. 更新创意...")
    update_data = {"art_style": "写实风格"}
    response = requests.put(f"{BASE_URL}/creatives/{creative_id}", json=update_data)
    if response.status_code == 200:
        creative = response.json()
        print(f"   更新成功，新风格: {creative['art_style']}")
    else:
        print(f"   更新失败: {response.status_code} - {response.text}")
    
    return creative_id


def test_prompt_crud(creative_id):
    """测试视频提示词的CRUD操作"""
    print("\n=== 测试视频提示词 CRUD ===")
    
    # 1. 创建提示词
    prompt_data = {
        "creative_id": creative_id,
        "scene": "开场场景",
        "prompt": "一个繁华的未来城市，霓虹灯闪烁，飞行汽车穿梭在高楼大厦之间"
    }
    
    print("1. 创建视频提示词...")
    response = requests.post(f"{BASE_URL}/prompts", json=prompt_data)
    if response.status_code == 200:
        prompt = response.json()
        prompt_id = prompt["id"]
        print(f"   创建成功，ID: {prompt_id}")
        print(f"   场景: {prompt['scene']}")
    else:
        print(f"   创建失败: {response.status_code} - {response.text}")
        return None
    
    # 2. 获取提示词列表
    print("2. 获取提示词列表...")
    response = requests.get(f"{BASE_URL}/prompts?creative_id={creative_id}")
    if response.status_code == 200:
        data = response.json()
        print(f"   获取成功，总数: {data['total']}")
    else:
        print(f"   获取失败: {response.status_code} - {response.text}")
    
    return prompt_id


def test_scene_crud(prompt_id):
    """测试视频场景的CRUD操作"""
    print("\n=== 测试视频场景 CRUD ===")
    
    # 1. 创建场景
    scene_data = {
        "prompt_id": prompt_id,
        "image": "/path/to/scene_image.jpg",
        "video": "/path/to/scene_video.mp4"
    }
    
    print("1. 创建视频场景...")
    response = requests.post(f"{BASE_URL}/scenes", json=scene_data)
    if response.status_code == 200:
        scene = response.json()
        scene_id = scene["id"]
        print(f"   创建成功，ID: {scene_id}")
        print(f"   图片: {scene['image']}")
    else:
        print(f"   创建失败: {response.status_code} - {response.text}")
        return None
    
    return scene_id


def test_subtitle_crud(scene_id):
    """测试视频字幕的CRUD操作"""
    print("\n=== 测试视频字幕 CRUD ===")
    
    # 1. 创建字幕
    subtitle_data = {
        "scene_id": scene_id,
        "subtitle": "欢迎来到2077年的新东京",
        "audio": "/path/to/subtitle_audio.mp3"
    }
    
    print("1. 创建视频字幕...")
    response = requests.post(f"{BASE_URL}/subtitles", json=subtitle_data)
    if response.status_code == 200:
        subtitle = response.json()
        subtitle_id = subtitle["id"]
        print(f"   创建成功，ID: {subtitle_id}")
        print(f"   字幕: {subtitle['subtitle']}")
    else:
        print(f"   创建失败: {response.status_code} - {response.text}")
        return None
    
    return subtitle_id


def main():
    """主测试函数"""
    print("开始测试视频API...")
    
    try:
        # 测试创意
        creative_id = test_creative_crud()
        if not creative_id:
            return
        
        # 测试提示词
        prompt_id = test_prompt_crud(creative_id)
        if not prompt_id:
            return
        
        # 测试场景
        scene_id = test_scene_crud(prompt_id)
        if not scene_id:
            return
        
        # 测试字幕
        subtitle_id = test_subtitle_crud(scene_id)
        
        print("\n=== 测试完成 ===")
        print("所有API测试通过！")
        
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到服务器，请确保服务器正在运行在 http://localhost:8000")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
