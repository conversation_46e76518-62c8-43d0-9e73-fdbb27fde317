"use client";

import { Suspense } from "react";
import dynamic from "next/dynamic";
import { useTranslations } from "next-intl";

import { Logo } from "~/components/video-flow/logo";
import { ThemeToggle } from "~/components/video-flow/theme-toggle";
import { Button } from "~/components/ui/button";
import { Tooltip } from "~/components/video-flow/tooltip";
import Link from "next/link";

// 动态导入主组件以避免 SSR 问题
const WorkflowMain = dynamic(() => import("./components/workflow-main"), {
  ssr: false,
  loading: () => (
    <div className="flex h-full w-full items-center justify-center">
      <div className="text-lg">Loading VideoFlow Workflow...</div>
    </div>
  ),
});

export default function WorkflowPage() {
  const t = useTranslations("workflow");

  return (
    <div className="h-screen w-screen overflow-hidden">
      {/* 顶部导航栏 - 与 chat 页面保持一致的样式 */}
      <header className="fixed top-0 left-0 flex h-12 w-full items-center justify-between px-4 bg-background/80 backdrop-blur-sm border-b z-50">
        <div className="flex items-center gap-4">
          <Logo />
          <div className="text-lg font-semibold text-foreground">
            Video Workflow
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Tooltip title="Back to Chat">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/chat">
                Chat
              </Link>
            </Button>
          </Tooltip>

          <ThemeToggle />
        </div>
      </header>

      {/* 主内容区域 - 使用 calc 计算准确高度 */}
      <main className="w-full h-full pt-12 overflow-auto">
        <Suspense fallback={
          <div className="flex h-full w-full items-center justify-center">
            <div className="text-lg">Loading...</div>
          </div>
        }>
          <WorkflowMain />
        </Suspense>
      </main>
    </div>
  );
}
