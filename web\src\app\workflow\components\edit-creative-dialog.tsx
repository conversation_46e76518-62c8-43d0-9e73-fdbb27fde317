"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  Di<PERSON>Trigger,
} from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Edit2, Loader2 } from "lucide-react";
import { useVideoAPI } from "../hooks/use-video-api";
import { Alert, AlertDescription } from "~/components/ui/alert";
import {type Creative} from "./workflow-main"


interface EditCreativeDialogProps {
  creative: Creative;
  onCreativeUpdated: () => void;
}

export function EditCreativeDialog({ creative, onCreativeUpdated }: EditCreativeDialogProps) {
  const t = useTranslations("workflow.detail");
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    creative: creative.creative || "",
    art_style: creative.art_style || "",
    scene_cnt: creative.scene_cnt || 5,
    character_prompt: creative.character_prompt || "",
  });

  const { updateCreative } = useVideoAPI();

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.creative.trim()) {
      setError("创意描述不能为空");
      return;
    }

    if (!formData.character_prompt.trim()) {
      setError("主角提示词不能为空");
      return;
    }

    if (formData.scene_cnt < 1 || formData.scene_cnt > 10) {
      setError("场景数量必须在 1-10 之间");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      await updateCreative(creative.id, {
        creative: formData.creative.trim(),
        art_style: formData.art_style.trim() || undefined,
        scene_cnt: formData.scene_cnt,
        character_prompt: formData.character_prompt.trim(),
      });
      
      // 成功后关闭对话框
      setOpen(false);
      onCreativeUpdated();
    } catch (err) {
      console.error("Failed to update creative:", err);
      setError("更新创意失败，请重试。");
    } finally {
      setLoading(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除错误信息
    if (error) setError(null);
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      creative: creative.creative || "",
      art_style: creative.art_style || "",
      scene_cnt: creative.scene_cnt || 5,
      character_prompt: creative.character_prompt || "",
    });
    setError(null);
  };

  return (
    <Dialog open={open} onOpenChange={(newOpen) => {
      setOpen(newOpen);
      if (!newOpen) {
        resetForm();
      }
    }}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Edit2 className="h-4 w-4" />
          编辑创意
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>编辑创意信息</DialogTitle>
          <DialogDescription>
            修改创意的基本信息，包括描述、风格、场景数量和主角提示词。
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 创意描述 */}
          <div className="space-y-2">
            <Label htmlFor="creative">创意描述 *</Label>
            <Textarea
              id="creative"
              placeholder="请输入创意描述..."
              value={formData.creative}
              onChange={(e) => handleInputChange("creative", e.target.value)}
              disabled={loading}
              rows={3}
              className="resize-none"
            />
          </div>

          {/* 艺术风格 */}
          <div className="space-y-2">
            <Label htmlFor="art_style">艺术风格</Label>
            <Input
              id="art_style"
              placeholder="如：写实、卡通、科幻等"
              value={formData.art_style}
              onChange={(e) => handleInputChange("art_style", e.target.value)}
              disabled={loading}
            />
          </div>

          {/* 场景数量 */}
          <div className="space-y-2">
            <Label htmlFor="scene_cnt">场景数量 *</Label>
            <Input
              id="scene_cnt"
              type="number"
              min="1"
              max="10"
              placeholder="1-10"
              value={formData.scene_cnt}
              onChange={(e) => handleInputChange("scene_cnt", parseInt(e.target.value) || 5)}
              disabled={loading}
            />
            <p className="text-xs text-muted-foreground">
              设置将创意切分为多少个场景（1-10个）
            </p>
          </div>

          {/* 主角提示词 */}
          <div className="space-y-2">
            <Label htmlFor="character_prompt">主角提示词 *</Label>
            <Textarea
              id="character_prompt"
              placeholder="请描述主角的外观、特征、服装等..."
              value={formData.character_prompt}
              onChange={(e) => handleInputChange("character_prompt", e.target.value)}
              disabled={loading}
              rows={4}
              className="resize-none"
            />
            <p className="text-xs text-muted-foreground">
              详细描述主角的形象，用于生成主角图片和场景描述
            </p>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  保存中...
                </>
              ) : (
                <>
                  <Edit2 className="h-4 w-4 mr-2" />
                  保存更改
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
