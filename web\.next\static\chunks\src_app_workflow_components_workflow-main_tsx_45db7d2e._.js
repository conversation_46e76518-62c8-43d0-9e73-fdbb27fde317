(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/app/workflow/components/workflow-main.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_f88f61ff._.js",
  "static/chunks/node_modules__pnpm_8ec0b432._.js",
  "static/chunks/src_app_workflow_components_workflow-main_tsx_5139f444._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/app/workflow/components/workflow-main.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);