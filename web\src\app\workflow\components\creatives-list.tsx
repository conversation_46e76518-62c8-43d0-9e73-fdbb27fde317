"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { But<PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Input } from "~/components/ui/input";
import { RefreshCw, Search, Video, Calendar } from "lucide-react";
import { type Creative } from "./workflow-main";
import { CreateCreativeDialog } from "./create-creative-dialog";

interface CreativesListProps {
  creatives: Creative[];
  onCreativeSelect: (creative: Creative) => void;
  onRefresh: () => void;
  onCreativeCreated: () => void;
}

export function CreativesList({ creatives, onCreativeSelect, onRefresh, onCreativeCreated }: CreativesListProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [isRefreshing, setIsRefreshing] = useState(false);

  // 过滤创意
  const filteredCreatives = creatives.filter(creative =>
    creative.creative.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (creative.art_style && creative.art_style.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // 处理刷新
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await onRefresh();
    } finally {
      setIsRefreshing(false);
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="h-full flex flex-col">
      {/* 工具栏 - 简化版本 */}
      <div className="flex items-center justify-between p-6 border-b bg-muted/30 flex-shrink-0">
        <div className="flex items-center gap-4">
          <h1 className="text-2xl font-bold">Video Creatives</h1>
          <Badge variant="secondary" className="text-sm">
            {filteredCreatives.length} items
          </Badge>
        </div>

        <div className="flex items-center gap-3">
          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search creatives..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>

          {/* 新增创意按钮 */}
          <CreateCreativeDialog onCreativeCreated={onCreativeCreated} />

          {/* 刷新按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* 创意列表 */}
      <div className="flex-1 overflow-auto p-6">
        {filteredCreatives.length === 0 ? (
          <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
            <Video className="h-16 w-16 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {searchTerm ? "No matching creatives found" : "No creatives yet"}
            </h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm
                ? "Try adjusting your search terms"
                : "Create your first video creative to get started"
              }
            </p>
            {searchTerm && (
              <Button variant="outline" onClick={() => setSearchTerm("")}>
                Clear search
              </Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredCreatives.map((creative) => (
              <Card
                key={creative.id}
                className="cursor-pointer transition-all hover:shadow-lg hover:scale-[1.02] border-2 hover:border-primary/50"
                onClick={() => onCreativeSelect(creative)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg line-clamp-2 leading-tight">
                      {creative.creative}
                    </CardTitle>
                    <Badge variant="outline" className="ml-2 shrink-0">
                      #{creative.id}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <div className="space-y-3">
                    {/* 艺术风格 */}
                    {creative.art_style && (
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">
                          {creative.art_style}
                        </Badge>
                      </div>
                    )}
                    
                    {/* 更新时间 */}
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(creative.update_time)}</span>
                    </div>
                    
                    {/* 操作按钮 */}
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full mt-3"
                      onClick={(e) => {
                        e.stopPropagation();
                        onCreativeSelect(creative);
                      }}
                    >
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
