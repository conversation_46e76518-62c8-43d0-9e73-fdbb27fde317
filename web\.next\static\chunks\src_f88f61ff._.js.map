{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/ui/card.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;;;;;;AAI/B;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,sSAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/app/workflow/components/creatives-list.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"~/components/ui/card\";\nimport { <PERSON><PERSON> } from \"~/components/ui/button\";\nimport { Badge } from \"~/components/ui/badge\";\nimport { Input } from \"~/components/ui/input\";\nimport { RefreshCw, Search, Video, Calendar } from \"lucide-react\";\nimport { Creative } from \"./workflow-main\";\nimport { CreateCreativeDialog } from \"./create-creative-dialog\";\n\ninterface CreativesListProps {\n  creatives: Creative[];\n  onCreativeSelect: (creative: Creative) => void;\n  onRefresh: () => void;\n  onCreativeCreated: () => void;\n}\n\nexport function CreativesList({ creatives, onCreativeSelect, onRefresh, onCreativeCreated }: CreativesListProps) {\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [isRefreshing, setIsRefreshing] = useState(false);\n\n  // 过滤创意\n  const filteredCreatives = creatives.filter(creative =>\n    creative.creative.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    (creative.art_style && creative.art_style.toLowerCase().includes(searchTerm.toLowerCase()))\n  );\n\n  // 处理刷新\n  const handleRefresh = async () => {\n    setIsRefreshing(true);\n    try {\n      await onRefresh();\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n\n  // 格式化日期\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString(\"zh-CN\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* 工具栏 - 简化版本 */}\n      <div className=\"flex items-center justify-between p-6 border-b bg-muted/30 flex-shrink-0\">\n        <div className=\"flex items-center gap-4\">\n          <h1 className=\"text-2xl font-bold\">Video Creatives</h1>\n          <Badge variant=\"secondary\" className=\"text-sm\">\n            {filteredCreatives.length} items\n          </Badge>\n        </div>\n\n        <div className=\"flex items-center gap-3\">\n          {/* 搜索框 */}\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Search creatives...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10 w-64\"\n            />\n          </div>\n\n          {/* 刷新按钮 */}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={handleRefresh}\n            disabled={isRefreshing}\n          >\n            <RefreshCw className={`h-4 w-4 ${isRefreshing ? \"animate-spin\" : \"\"}`} />\n            Refresh\n          </Button>\n        </div>\n      </div>\n\n      {/* 创意列表 */}\n      <div className=\"flex-1 overflow-auto p-6\">\n        {filteredCreatives.length === 0 ? (\n          <div className=\"flex flex-col items-center justify-center min-h-[400px] text-center\">\n            <Video className=\"h-16 w-16 text-muted-foreground mb-4\" />\n            <h3 className=\"text-lg font-semibold mb-2\">\n              {searchTerm ? \"No matching creatives found\" : \"No creatives yet\"}\n            </h3>\n            <p className=\"text-muted-foreground mb-4\">\n              {searchTerm\n                ? \"Try adjusting your search terms\"\n                : \"Create your first video creative to get started\"\n              }\n            </p>\n            {searchTerm && (\n              <Button variant=\"outline\" onClick={() => setSearchTerm(\"\")}>\n                Clear search\n              </Button>\n            )}\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {filteredCreatives.map((creative) => (\n              <Card\n                key={creative.id}\n                className=\"cursor-pointer transition-all hover:shadow-lg hover:scale-[1.02] border-2 hover:border-primary/50\"\n                onClick={() => onCreativeSelect(creative)}\n              >\n                <CardHeader className=\"pb-3\">\n                  <div className=\"flex items-start justify-between\">\n                    <CardTitle className=\"text-lg line-clamp-2 leading-tight\">\n                      {creative.creative}\n                    </CardTitle>\n                    <Badge variant=\"outline\" className=\"ml-2 shrink-0\">\n                      #{creative.id}\n                    </Badge>\n                  </div>\n                </CardHeader>\n                \n                <CardContent className=\"pt-0\">\n                  <div className=\"space-y-3\">\n                    {/* 艺术风格 */}\n                    {creative.art_style && (\n                      <div className=\"flex items-center gap-2\">\n                        <Badge variant=\"secondary\" className=\"text-xs\">\n                          {creative.art_style}\n                        </Badge>\n                      </div>\n                    )}\n                    \n                    {/* 更新时间 */}\n                    <div className=\"flex items-center gap-2 text-sm text-muted-foreground\">\n                      <Calendar className=\"h-3 w-3\" />\n                      <span>{formatDate(creative.update_time)}</span>\n                    </div>\n                    \n                    {/* 操作按钮 */}\n                    <Button \n                      variant=\"outline\" \n                      size=\"sm\" \n                      className=\"w-full mt-3\"\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        onCreativeSelect(creative);\n                      }}\n                    >\n                      View Details\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAkBO,SAAS,cAAc,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,iBAAiB,EAAsB;;IAC7G,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,OAAO;IACP,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA,WACzC,SAAS,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,SAAS,SAAS,IAAI,SAAS,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAGzF,OAAO;IACP,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,IAAI;YACF,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,qBACE,sSAAC;QAAI,WAAU;;0BAEb,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,sSAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;;oCAClC,kBAAkB,MAAM;oCAAC;;;;;;;;;;;;;kCAI9B,sSAAC;wBAAI,WAAU;;0CAEb,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,6RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,sSAAC,oIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAKd,sSAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;;kDAEV,sSAAC,uSAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,QAAQ,EAAE,eAAe,iBAAiB,IAAI;;;;;;oCAAI;;;;;;;;;;;;;;;;;;;0BAO/E,sSAAC;gBAAI,WAAU;0BACZ,kBAAkB,MAAM,KAAK,kBAC5B,sSAAC;oBAAI,WAAU;;sCACb,sSAAC,2RAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,sSAAC;4BAAG,WAAU;sCACX,aAAa,gCAAgC;;;;;;sCAEhD,sSAAC;4BAAE,WAAU;sCACV,aACG,oCACA;;;;;;wBAGL,4BACC,sSAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS,IAAM,cAAc;sCAAK;;;;;;;;;;;yCAMhE,sSAAC;oBAAI,WAAU;8BACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,sSAAC,mIAAA,CAAA,OAAI;4BAEH,WAAU;4BACV,SAAS,IAAM,iBAAiB;;8CAEhC,sSAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,SAAS,QAAQ;;;;;;0DAEpB,sSAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;oDAAgB;oDAC/C,SAAS,EAAE;;;;;;;;;;;;;;;;;;8CAKnB,sSAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,sSAAC;wCAAI,WAAU;;4CAEZ,SAAS,SAAS,kBACjB,sSAAC;gDAAI,WAAU;0DACb,cAAA,sSAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAClC,SAAS,SAAS;;;;;;;;;;;0DAMzB,sSAAC;gDAAI,WAAU;;kEACb,sSAAC,iSAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,sSAAC;kEAAM,WAAW,SAAS,WAAW;;;;;;;;;;;;0DAIxC,sSAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,iBAAiB;gDACnB;0DACD;;;;;;;;;;;;;;;;;;2BAzCA,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;AAqDhC;GAhJgB;KAAA", "debugId": null}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/ui/tabs.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;;;AAK/B;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,sSAAC,mRAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,sSAAC,mRAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,mRAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,mRAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 618, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/env.js"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { createEnv } from \"@t3-oss/env-nextjs\";\r\nimport { z } from \"zod\";\r\n\r\nexport const env = createEnv({\r\n  /**\r\n   * Specify your server-side environment variables schema here. This way you can ensure the app\r\n   * isn't built with invalid env vars.\r\n   */\r\n  server: {\r\n    NODE_ENV: z.enum([\"development\", \"test\", \"production\"]),\r\n    AMPLITUDE_API_KEY: z.string().optional(),\r\n    GITHUB_OAUTH_TOKEN: z.string().optional(),\r\n  },\r\n\r\n  /**\r\n   * Specify your client-side environment variables schema here. This way you can ensure the app\r\n   * isn't built with invalid env vars. To expose them to the client, prefix them with\r\n   * `NEXT_PUBLIC_`.\r\n   */\r\n  client: {\r\n    NEXT_PUBLIC_API_URL: z.string().optional(),\r\n    NEXT_PUBLIC_STATIC_WEBSITE_ONLY: z.boolean().optional(),\r\n  },\r\n\r\n  /**\r\n   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.\r\n   * middlewares) or client-side so we need to destruct manually.\r\n   */\r\n  runtimeEnv: {\r\n    NODE_ENV: process.env.NODE_ENV,\r\n    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,\r\n    NEXT_PUBLIC_STATIC_WEBSITE_ONLY:\r\n      process.env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY === \"true\",\r\n    AMPLITUDE_API_KEY: process.env.AMPLITUDE_API_KEY,\r\n    GITHUB_OAUTH_TOKEN: process.env.GITHUB_OAUTH_TOKEN,\r\n  },\r\n  /**\r\n   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially\r\n   * useful for Docker builds.\r\n   */\r\n  skipValidation: !!process.env.SKIP_ENV_VALIDATION,\r\n  /**\r\n   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and\r\n   * `SOME_VAR=''` will throw an error.\r\n   */\r\n  emptyStringAsUndefined: true,\r\n});\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AA+BjB;AA7Bd;AACA;;;AAEO,MAAM,MAAM,CAAA,GAAA,wRAAA,CAAA,YAAS,AAAD,EAAE;IAC3B;;;GAGC,GACD,QAAQ;QACN,UAAU,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAe;YAAQ;SAAa;QACtD,mBAAmB,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,oBAAoB,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACzC;IAEA;;;;GAIC,GACD,QAAQ;QACN,qBAAqB,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACxC,iCAAiC,wLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IACvD;IAEA;;;GAGC,GACD,YAAY;QACV,QAAQ;QACR,mBAAmB;QACnB,iCACE,yQAAA,CAAA,UAAO,CAAC,GAAG,CAAC,+BAA+B,KAAK;QAClD,mBAAmB,yQAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB;QAChD,oBAAoB,yQAAA,CAAA,UAAO,CAAC,GAAG,CAAC,kBAAkB;IACpD;IACA;;;GAGC,GACD,gBAAgB,CAAC,CAAC,yQAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB;IACjD;;;GAGC,GACD,wBAAwB;AAC1B", "debugId": null}}, {"offset": {"line": 677, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/app/workflow/hooks/use-video-api.ts"], "sourcesContent": ["import { useCallback } from \"react\";\nimport { env } from \"~/env\";\n\n// API 基础 URL\nconst getApiUrl = () => {\n  return env.NEXT_PUBLIC_API_URL || \"http://localhost:8000/api\";\n};\n\n// API 响应类型\ninterface PaginatedResponse<T> {\n  items: T[];\n  total: number;\n  page: number;\n  size: number;\n  pages: number;\n}\n\ninterface Creative {\n  id: number;\n  creative: string;\n  art_style?: string;\n  update_time: string;\n}\n\ninterface CreativeCreate {\n  creative: string;\n  art_style?: string;\n}\n\ninterface Prompt {\n  id: number;\n  creative_id: number;\n  scene: string;\n  prompt: string;\n  update_time: string;\n}\n\ninterface Scene {\n  id: number;\n  prompt_id: number;\n  image?: string;\n  video?: string;\n  update_time: string;\n}\n\ninterface Subtitle {\n  id: number;\n  scene_id: number;\n  subtitle?: string;\n  audio?: string;\n  update_time: string;\n}\n\nexport function useVideoAPI() {\n  // 通用的 fetch 函数 - GET 请求\n  const apiRequest = useCallback(async <T>(endpoint: string): Promise<T> => {\n    const url = `${getApiUrl()}${endpoint}`;\n\n    try {\n      const response = await fetch(url, {\n        method: \"GET\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API request failed for ${endpoint}:`, error);\n      throw error;\n    }\n  }, []);\n\n  // 通用的 POST 请求函数\n  const apiPostRequest = useCallback(async <T>(endpoint: string, data: any): Promise<T> => {\n    const url = `${getApiUrl()}${endpoint}`;\n\n    try {\n      const response = await fetch(url, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API POST request failed for ${endpoint}:`, error);\n      throw error;\n    }\n  }, []);\n\n  // 获取创意列表\n  const fetchCreatives = useCallback(\n    async (page = 1, size = 20): Promise<PaginatedResponse<Creative>> => {\n      return apiRequest(`/video/creatives?page=${page}&size=${size}`);\n    },\n    [apiRequest]\n  );\n\n  // 获取单个创意\n  const fetchCreative = useCallback(\n    async (id: number): Promise<Creative> => {\n      return apiRequest(`/video/creatives/${id}`);\n    },\n    [apiRequest]\n  );\n\n  // 创建新创意\n  const createCreative = useCallback(\n    async (data: CreativeCreate): Promise<Creative> => {\n      return apiPostRequest(`/video/creatives`, data);\n    },\n    [apiPostRequest]\n  );\n\n  // 获取创意的提示词列表\n  const fetchPrompts = useCallback(\n    async (creativeId: number, page = 1, size = 50): Promise<PaginatedResponse<Prompt>> => {\n      return apiRequest(`/video/prompts?creative_id=${creativeId}&page=${page}&size=${size}`);\n    },\n    [apiRequest]\n  );\n\n  // 获取提示词的场景列表\n  const fetchScenes = useCallback(\n    async (promptId: number, page = 1, size = 50): Promise<PaginatedResponse<Scene>> => {\n      return apiRequest(`/video/scenes?prompt_id=${promptId}&page=${page}&size=${size}`);\n    },\n    [apiRequest]\n  );\n\n  // 获取场景的字幕列表\n  const fetchSubtitles = useCallback(\n    async (sceneId: number, page = 1, size = 50): Promise<PaginatedResponse<Subtitle>> => {\n      return apiRequest(`/video/subtitles?scene_id=${sceneId}&page=${page}&size=${size}`);\n    },\n    [apiRequest]\n  );\n\n  return {\n    fetchCreatives,\n    fetchCreative,\n    createCreative,\n    fetchPrompts,\n    fetchScenes,\n    fetchSubtitles,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;AAEA,aAAa;AACb,MAAM,YAAY;IAChB,OAAO,6GAAA,CAAA,MAAG,CAAC,mBAAmB,IAAI;AACpC;AA+CO,SAAS;;IACd,wBAAwB;IACxB,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;+CAAE,OAAU;YACvC,MAAM,MAAM,GAAG,cAAc,UAAU;YAEvC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,KAAK;oBAChC,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;gBAC1D;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC,EAAE;gBACrD,MAAM;YACR;QACF;8CAAG,EAAE;IAEL,gBAAgB;IAChB,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAAE,OAAU,UAAkB;YAC7D,MAAM,MAAM,GAAG,cAAc,UAAU;YAEvC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,KAAK;oBAChC,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;gBAC1D;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,SAAS,CAAC,CAAC,EAAE;gBAC1D,MAAM;YACR;QACF;kDAAG,EAAE;IAEL,SAAS;IACT,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAC/B,OAAO,OAAO,CAAC,EAAE,OAAO,EAAE;YACxB,OAAO,WAAW,CAAC,sBAAsB,EAAE,KAAK,MAAM,EAAE,MAAM;QAChE;kDACA;QAAC;KAAW;IAGd,SAAS;IACT,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;kDAC9B,OAAO;YACL,OAAO,WAAW,CAAC,iBAAiB,EAAE,IAAI;QAC5C;iDACA;QAAC;KAAW;IAGd,QAAQ;IACR,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAC/B,OAAO;YACL,OAAO,eAAe,CAAC,gBAAgB,CAAC,EAAE;QAC5C;kDACA;QAAC;KAAe;IAGlB,aAAa;IACb,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;iDAC7B,OAAO,YAAoB,OAAO,CAAC,EAAE,OAAO,EAAE;YAC5C,OAAO,WAAW,CAAC,2BAA2B,EAAE,WAAW,MAAM,EAAE,KAAK,MAAM,EAAE,MAAM;QACxF;gDACA;QAAC;KAAW;IAGd,aAAa;IACb,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;gDAC5B,OAAO,UAAkB,OAAO,CAAC,EAAE,OAAO,EAAE;YAC1C,OAAO,WAAW,CAAC,wBAAwB,EAAE,SAAS,MAAM,EAAE,KAAK,MAAM,EAAE,MAAM;QACnF;+CACA;QAAC;KAAW;IAGd,YAAY;IACZ,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAC/B,OAAO,SAAiB,OAAO,CAAC,EAAE,OAAO,EAAE;YACzC,OAAO,WAAW,CAAC,0BAA0B,EAAE,QAAQ,MAAM,EAAE,KAAK,MAAM,EAAE,MAAM;QACpF;kDACA;QAAC;KAAW;IAGd,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAxGgB", "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"~/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,2KACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,sSAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/app/workflow/components/creative-detail.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"~/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"~/components/ui/card\";\nimport { Badge } from \"~/components/ui/badge\";\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"~/components/ui/tabs\";\nimport { ArrowLeft, Calendar, Palette, FileText, Image, Video, Volume2, Subtitles, Loader2 } from \"lucide-react\";\nimport { Creative, Prompt, Scene, Subtitle } from \"./workflow-main\";\nimport { useVideoAPI } from \"../hooks/use-video-api\";\nimport { Alert, AlertDescription } from \"~/components/ui/alert\";\n\ninterface CreativeDetailProps {\n  creative: Creative;\n  onBack: () => void;\n}\n\nexport function CreativeDetail({ creative, onBack }: CreativeDetailProps) {\n  const [prompts, setPrompts] = useState<Prompt[]>([]);\n  const [scenes, setScenes] = useState<Scene[]>([]);\n  const [subtitles, setSubtitles] = useState<Subtitle[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState(\"overview\");\n\n  const { fetchPrompts, fetchScenes, fetchSubtitles } = useVideoAPI();\n\n  // 格式化日期\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString(\"zh-CN\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  // 加载相关数据\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        // 获取提示词\n        const promptsData = await fetchPrompts(creative.id);\n        setPrompts(promptsData.items || []);\n\n        // 获取所有场景\n        const allScenes: Scene[] = [];\n        const allSubtitles: Subtitle[] = [];\n\n        for (const prompt of promptsData.items || []) {\n          const scenesData = await fetchScenes(prompt.id);\n          allScenes.push(...(scenesData.items || []));\n\n          // 获取每个场景的字幕\n          for (const scene of scenesData.items || []) {\n            const subtitlesData = await fetchSubtitles(scene.id);\n            allSubtitles.push(...(subtitlesData.items || []));\n          }\n        }\n\n        setScenes(allScenes);\n        setSubtitles(allSubtitles);\n      } catch (err) {\n        console.error(\"Failed to load creative details:\", err);\n        setError(\"Failed to load creative details. Please try again.\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadData();\n  }, [creative.id, fetchPrompts, fetchScenes, fetchSubtitles]);\n\n  if (loading) {\n    return (\n      <div className=\"flex h-full w-full items-center justify-center\">\n        <div className=\"flex items-center gap-2\">\n          <Loader2 className=\"h-6 w-6 animate-spin\" />\n          <span className=\"text-lg\">Loading creative details...</span>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"flex h-full w-full items-center justify-center p-6\">\n        <Alert className=\"max-w-md\">\n          <AlertDescription>{error}</AlertDescription>\n        </Alert>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* 头部 */}\n      <div className=\"flex items-center justify-between p-6 border-b bg-muted/30 flex-shrink-0\">\n        <div className=\"flex items-center gap-4\">\n          <Button variant=\"ghost\" size=\"sm\" onClick={onBack}>\n            <ArrowLeft className=\"h-4 w-4\" />\n            Back to List\n          </Button>\n          <div>\n            <h1 className=\"text-2xl font-bold line-clamp-2\">{creative.creative}</h1>\n            <div className=\"flex items-center gap-4 mt-2\">\n              <Badge variant=\"outline\">ID: {creative.id}</Badge>\n              {creative.art_style && (\n                <Badge variant=\"secondary\">\n                  <Palette className=\"h-3 w-3 mr-1\" />\n                  {creative.art_style}\n                </Badge>\n              )}\n              <div className=\"flex items-center gap-1 text-sm text-muted-foreground\">\n                <Calendar className=\"h-3 w-3\" />\n                {formatDate(creative.update_time)}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 内容区域 */}\n      <div className=\"flex-1 overflow-auto p-6\">\n        <Tabs value={activeTab} onValueChange={setActiveTab}>\n          <TabsList className=\"grid w-full grid-cols-4\">\n            <TabsTrigger value=\"overview\">Overview</TabsTrigger>\n            <TabsTrigger value=\"prompts\">\n              Prompts ({prompts.length})\n            </TabsTrigger>\n            <TabsTrigger value=\"scenes\">\n              Scenes ({scenes.length})\n            </TabsTrigger>\n            <TabsTrigger value=\"subtitles\">\n              Subtitles ({subtitles.length})\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"overview\" className=\"mt-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n              <Card>\n                <CardHeader className=\"pb-2\">\n                  <CardTitle className=\"text-sm font-medium flex items-center gap-2\">\n                    <FileText className=\"h-4 w-4\" />\n                    Prompts\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">{prompts.length}</div>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader className=\"pb-2\">\n                  <CardTitle className=\"text-sm font-medium flex items-center gap-2\">\n                    <Image className=\"h-4 w-4\" />\n                    Scenes\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">{scenes.length}</div>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader className=\"pb-2\">\n                  <CardTitle className=\"text-sm font-medium flex items-center gap-2\">\n                    <Video className=\"h-4 w-4\" />\n                    Videos\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">\n                    {scenes.filter(s => s.video).length}\n                  </div>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader className=\"pb-2\">\n                  <CardTitle className=\"text-sm font-medium flex items-center gap-2\">\n                    <Subtitles className=\"h-4 w-4\" />\n                    Subtitles\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">{subtitles.length}</div>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* 创意详情 */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Creative Details</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div>\n                  <label className=\"text-sm font-medium text-muted-foreground\">Description</label>\n                  <p className=\"mt-1 text-sm\">{creative.creative}</p>\n                </div>\n                {creative.art_style && (\n                  <div>\n                    <label className=\"text-sm font-medium text-muted-foreground\">Art Style</label>\n                    <p className=\"mt-1 text-sm\">{creative.art_style}</p>\n                  </div>\n                )}\n                <div>\n                  <label className=\"text-sm font-medium text-muted-foreground\">Last Updated</label>\n                  <p className=\"mt-1 text-sm\">{formatDate(creative.update_time)}</p>\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          <TabsContent value=\"prompts\" className=\"mt-6\">\n            <div className=\"space-y-4\">\n              {prompts.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <FileText className=\"h-16 w-16 text-muted-foreground mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-semibold mb-2\">No prompts yet</h3>\n                  <p className=\"text-muted-foreground\">This creative doesn't have any prompts yet.</p>\n                </div>\n              ) : (\n                prompts.map((prompt) => (\n                  <Card key={prompt.id}>\n                    <CardHeader>\n                      <div className=\"flex items-start justify-between\">\n                        <div>\n                          <CardTitle className=\"text-lg\">{prompt.scene}</CardTitle>\n                          <CardDescription className=\"flex items-center gap-2 mt-1\">\n                            <Badge variant=\"outline\">ID: {prompt.id}</Badge>\n                            <span className=\"flex items-center gap-1\">\n                              <Calendar className=\"h-3 w-3\" />\n                              {formatDate(prompt.update_time)}\n                            </span>\n                          </CardDescription>\n                        </div>\n                      </div>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"space-y-3\">\n                        <div>\n                          <label className=\"text-sm font-medium text-muted-foreground\">Prompt</label>\n                          <p className=\"mt-1 text-sm bg-muted/50 p-3 rounded-md\">{prompt.prompt}</p>\n                        </div>\n                        <div className=\"flex items-center gap-2 text-sm text-muted-foreground\">\n                          <span>Scenes: {scenes.filter(s => s.prompt_id === prompt.id).length}</span>\n                        </div>\n                      </div>\n                    </CardContent>\n                  </Card>\n                ))\n              )}\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"scenes\" className=\"mt-6\">\n            <div className=\"space-y-4\">\n              {scenes.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <Image className=\"h-16 w-16 text-muted-foreground mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-semibold mb-2\">No scenes yet</h3>\n                  <p className=\"text-muted-foreground\">This creative doesn't have any scenes yet.</p>\n                </div>\n              ) : (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                  {scenes.map((scene) => {\n                    const relatedPrompt = prompts.find(p => p.id === scene.prompt_id);\n                    const sceneSubtitles = subtitles.filter(s => s.scene_id === scene.id);\n\n                    return (\n                      <Card key={scene.id}>\n                        <CardHeader className=\"pb-3\">\n                          <div className=\"flex items-start justify-between\">\n                            <CardTitle className=\"text-base\">\n                              Scene #{scene.id}\n                            </CardTitle>\n                            <Badge variant=\"outline\" className=\"text-xs\">\n                              {formatDate(scene.update_time)}\n                            </Badge>\n                          </div>\n                          {relatedPrompt && (\n                            <CardDescription className=\"text-sm\">\n                              From: {relatedPrompt.scene}\n                            </CardDescription>\n                          )}\n                        </CardHeader>\n                        <CardContent className=\"space-y-3\">\n                          {/* 图片 */}\n                          {scene.image && (\n                            <div>\n                              <label className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\n                                <Image className=\"h-3 w-3\" />\n                                Image\n                              </label>\n                              <div className=\"mt-1 p-2 bg-muted/50 rounded text-xs font-mono break-all\">\n                                {scene.image}\n                              </div>\n                            </div>\n                          )}\n\n                          {/* 视频 */}\n                          {scene.video && (\n                            <div>\n                              <label className=\"text-xs font-medium text-muted-foreground flex items-center gap-1\">\n                                <Video className=\"h-3 w-3\" />\n                                Video\n                              </label>\n                              <div className=\"mt-1 p-2 bg-muted/50 rounded text-xs font-mono break-all\">\n                                {scene.video}\n                              </div>\n                            </div>\n                          )}\n\n                          {/* 字幕数量 */}\n                          <div className=\"flex items-center gap-2 text-xs text-muted-foreground\">\n                            <Subtitles className=\"h-3 w-3\" />\n                            <span>{sceneSubtitles.length} subtitles</span>\n                          </div>\n                        </CardContent>\n                      </Card>\n                    );\n                  })}\n                </div>\n              )}\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"subtitles\" className=\"mt-6\">\n            <div className=\"space-y-4\">\n              {subtitles.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <Subtitles className=\"h-16 w-16 text-muted-foreground mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-semibold mb-2\">No subtitles yet</h3>\n                  <p className=\"text-muted-foreground\">This creative doesn't have any subtitles yet.</p>\n                </div>\n              ) : (\n                subtitles.map((subtitle) => {\n                  const relatedScene = scenes.find(s => s.id === subtitle.scene_id);\n                  const relatedPrompt = relatedScene ? prompts.find(p => p.id === relatedScene.prompt_id) : null;\n\n                  return (\n                    <Card key={subtitle.id}>\n                      <CardHeader>\n                        <div className=\"flex items-start justify-between\">\n                          <div>\n                            <CardTitle className=\"text-lg\">Subtitle #{subtitle.id}</CardTitle>\n                            <CardDescription className=\"flex items-center gap-2 mt-1\">\n                              {relatedPrompt && (\n                                <span className=\"text-xs\">From: {relatedPrompt.scene}</span>\n                              )}\n                              <Badge variant=\"outline\" className=\"text-xs\">\n                                Scene #{subtitle.scene_id}\n                              </Badge>\n                              <span className=\"flex items-center gap-1 text-xs\">\n                                <Calendar className=\"h-3 w-3\" />\n                                {formatDate(subtitle.update_time)}\n                              </span>\n                            </CardDescription>\n                          </div>\n                        </div>\n                      </CardHeader>\n                      <CardContent>\n                        <div className=\"space-y-3\">\n                          {/* 字幕文本 */}\n                          {subtitle.subtitle && (\n                            <div>\n                              <label className=\"text-sm font-medium text-muted-foreground flex items-center gap-1\">\n                                <Subtitles className=\"h-3 w-3\" />\n                                Subtitle Text\n                              </label>\n                              <p className=\"mt-1 text-sm bg-muted/50 p-3 rounded-md\">{subtitle.subtitle}</p>\n                            </div>\n                          )}\n\n                          {/* 音频文件 */}\n                          {subtitle.audio && (\n                            <div>\n                              <label className=\"text-sm font-medium text-muted-foreground flex items-center gap-1\">\n                                <Volume2 className=\"h-3 w-3\" />\n                                Audio File\n                              </label>\n                              <div className=\"mt-1 p-2 bg-muted/50 rounded text-sm font-mono break-all\">\n                                {subtitle.audio}\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                      </CardContent>\n                    </Card>\n                  );\n                })\n              )}\n            </div>\n          </TabsContent>\n        </Tabs>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;;;AAVA;;;;;;;;;AAiBO,SAAS,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAuB;;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAEhE,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,SAAS;IACT,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;qDAAW;oBACf,IAAI;wBACF,WAAW;wBACX,SAAS;wBAET,QAAQ;wBACR,MAAM,cAAc,MAAM,aAAa,SAAS,EAAE;wBAClD,WAAW,YAAY,KAAK,IAAI,EAAE;wBAElC,SAAS;wBACT,MAAM,YAAqB,EAAE;wBAC7B,MAAM,eAA2B,EAAE;wBAEnC,KAAK,MAAM,UAAU,YAAY,KAAK,IAAI,EAAE,CAAE;4BAC5C,MAAM,aAAa,MAAM,YAAY,OAAO,EAAE;4BAC9C,UAAU,IAAI,IAAK,WAAW,KAAK,IAAI,EAAE;4BAEzC,YAAY;4BACZ,KAAK,MAAM,SAAS,WAAW,KAAK,IAAI,EAAE,CAAE;gCAC1C,MAAM,gBAAgB,MAAM,eAAe,MAAM,EAAE;gCACnD,aAAa,IAAI,IAAK,cAAc,KAAK,IAAI,EAAE;4BACjD;wBACF;wBAEA,UAAU;wBACV,aAAa;oBACf,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,oCAAoC;wBAClD,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;mCAAG;QAAC,SAAS,EAAE;QAAE;QAAc;QAAa;KAAe;IAE3D,IAAI,SAAS;QACX,qBACE,sSAAC;YAAI,WAAU;sBACb,cAAA,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,wSAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,sSAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;;;;;;IAIlC;IAEA,IAAI,OAAO;QACT,qBACE,sSAAC;YAAI,WAAU;sBACb,cAAA,sSAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BACf,cAAA,sSAAC,oIAAA,CAAA,mBAAgB;8BAAE;;;;;;;;;;;;;;;;IAI3B;IAEA,qBACE,sSAAC;QAAI,WAAU;;0BAEb,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC;oBAAI,WAAU;;sCACb,sSAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,SAAS;;8CACzC,sSAAC,uSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGnC,sSAAC;;8CACC,sSAAC;oCAAG,WAAU;8CAAmC,SAAS,QAAQ;;;;;;8CAClE,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;;gDAAU;gDAAK,SAAS,EAAE;;;;;;;wCACxC,SAAS,SAAS,kBACjB,sSAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;;8DACb,sSAAC,+RAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAClB,SAAS,SAAS;;;;;;;sDAGvB,sSAAC;4CAAI,WAAU;;8DACb,sSAAC,iSAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDACnB,WAAW,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1C,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,mIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;;sCACrC,sSAAC,mIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,sSAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,sSAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;;wCAAU;wCACjB,QAAQ,MAAM;wCAAC;;;;;;;8CAE3B,sSAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;;wCAAS;wCACjB,OAAO,MAAM;wCAAC;;;;;;;8CAEzB,sSAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;;wCAAY;wCACjB,UAAU,MAAM;wCAAC;;;;;;;;;;;;;sCAIjC,sSAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;;8CACtC,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,mIAAA,CAAA,OAAI;;8DACH,sSAAC,mIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,sSAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,sSAAC,qSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;8DAIpC,sSAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,sSAAC;wDAAI,WAAU;kEAAsB,QAAQ,MAAM;;;;;;;;;;;;;;;;;sDAIvD,sSAAC,mIAAA,CAAA,OAAI;;8DACH,sSAAC,mIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,sSAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,sSAAC,2RAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;8DAIjC,sSAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,sSAAC;wDAAI,WAAU;kEAAsB,OAAO,MAAM;;;;;;;;;;;;;;;;;sDAItD,sSAAC,mIAAA,CAAA,OAAI;;8DACH,sSAAC,mIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,sSAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,sSAAC,2RAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;8DAIjC,sSAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,sSAAC;wDAAI,WAAU;kEACZ,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,EAAE,MAAM;;;;;;;;;;;;;;;;;sDAKzC,sSAAC,mIAAA,CAAA,OAAI;;8DACH,sSAAC,mIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,sSAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,sSAAC,kSAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;8DAIrC,sSAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,sSAAC;wDAAI,WAAU;kEAAsB,UAAU,MAAM;;;;;;;;;;;;;;;;;;;;;;;8CAM3D,sSAAC,mIAAA,CAAA,OAAI;;sDACH,sSAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,sSAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,sSAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,sSAAC;;sEACC,sSAAC;4DAAM,WAAU;sEAA4C;;;;;;sEAC7D,sSAAC;4DAAE,WAAU;sEAAgB,SAAS,QAAQ;;;;;;;;;;;;gDAE/C,SAAS,SAAS,kBACjB,sSAAC;;sEACC,sSAAC;4DAAM,WAAU;sEAA4C;;;;;;sEAC7D,sSAAC;4DAAE,WAAU;sEAAgB,SAAS,SAAS;;;;;;;;;;;;8DAGnD,sSAAC;;sEACC,sSAAC;4DAAM,WAAU;sEAA4C;;;;;;sEAC7D,sSAAC;4DAAE,WAAU;sEAAgB,WAAW,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMpE,sSAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAU,WAAU;sCACrC,cAAA,sSAAC;gCAAI,WAAU;0CACZ,QAAQ,MAAM,KAAK,kBAClB,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,qSAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,sSAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,sSAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;2CAGvC,QAAQ,GAAG,CAAC,CAAC,uBACX,sSAAC,mIAAA,CAAA,OAAI;;0DACH,sSAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,sSAAC;oDAAI,WAAU;8DACb,cAAA,sSAAC;;0EACC,sSAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAW,OAAO,KAAK;;;;;;0EAC5C,sSAAC,mIAAA,CAAA,kBAAe;gEAAC,WAAU;;kFACzB,sSAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;;4EAAU;4EAAK,OAAO,EAAE;;;;;;;kFACvC,sSAAC;wEAAK,WAAU;;0FACd,sSAAC,iSAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EACnB,WAAW,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAMxC,sSAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,sSAAC;oDAAI,WAAU;;sEACb,sSAAC;;8EACC,sSAAC;oEAAM,WAAU;8EAA4C;;;;;;8EAC7D,sSAAC;oEAAE,WAAU;8EAA2C,OAAO,MAAM;;;;;;;;;;;;sEAEvE,sSAAC;4DAAI,WAAU;sEACb,cAAA,sSAAC;;oEAAK;oEAAS,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,OAAO,EAAE,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;uCAtBhE,OAAO,EAAE;;;;;;;;;;;;;;;sCAgC5B,sSAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACpC,cAAA,sSAAC;gCAAI,WAAU;0CACZ,OAAO,MAAM,KAAK,kBACjB,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,2RAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,sSAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,sSAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;yDAGvC,sSAAC;oCAAI,WAAU;8CACZ,OAAO,GAAG,CAAC,CAAC;wCACX,MAAM,gBAAgB,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,SAAS;wCAChE,MAAM,iBAAiB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,MAAM,EAAE;wCAEpE,qBACE,sSAAC,mIAAA,CAAA,OAAI;;8DACH,sSAAC,mIAAA,CAAA,aAAU;oDAAC,WAAU;;sEACpB,sSAAC;4DAAI,WAAU;;8EACb,sSAAC,mIAAA,CAAA,YAAS;oEAAC,WAAU;;wEAAY;wEACvB,MAAM,EAAE;;;;;;;8EAElB,sSAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;8EAChC,WAAW,MAAM,WAAW;;;;;;;;;;;;wDAGhC,+BACC,sSAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;;gEAAU;gEAC5B,cAAc,KAAK;;;;;;;;;;;;;8DAIhC,sSAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;;wDAEpB,MAAM,KAAK,kBACV,sSAAC;;8EACC,sSAAC;oEAAM,WAAU;;sFACf,sSAAC,2RAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAAY;;;;;;;8EAG/B,sSAAC;oEAAI,WAAU;8EACZ,MAAM,KAAK;;;;;;;;;;;;wDAMjB,MAAM,KAAK,kBACV,sSAAC;;8EACC,sSAAC;oEAAM,WAAU;;sFACf,sSAAC,2RAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAAY;;;;;;;8EAG/B,sSAAC;oEAAI,WAAU;8EACZ,MAAM,KAAK;;;;;;;;;;;;sEAMlB,sSAAC;4DAAI,WAAU;;8EACb,sSAAC,kSAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,sSAAC;;wEAAM,eAAe,MAAM;wEAAC;;;;;;;;;;;;;;;;;;;;2CA9CxB,MAAM,EAAE;;;;;oCAmDvB;;;;;;;;;;;;;;;;sCAMR,sSAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAY,WAAU;sCACvC,cAAA,sSAAC;gCAAI,WAAU;0CACZ,UAAU,MAAM,KAAK,kBACpB,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,kSAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,sSAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,sSAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;2CAGvC,UAAU,GAAG,CAAC,CAAC;oCACb,MAAM,eAAe,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,QAAQ;oCAChE,MAAM,gBAAgB,eAAe,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,SAAS,IAAI;oCAE1F,qBACE,sSAAC,mIAAA,CAAA,OAAI;;0DACH,sSAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,sSAAC;oDAAI,WAAU;8DACb,cAAA,sSAAC;;0EACC,sSAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;;oEAAU;oEAAW,SAAS,EAAE;;;;;;;0EACrD,sSAAC,mIAAA,CAAA,kBAAe;gEAAC,WAAU;;oEACxB,+BACC,sSAAC;wEAAK,WAAU;;4EAAU;4EAAO,cAAc,KAAK;;;;;;;kFAEtD,sSAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,WAAU;;4EAAU;4EACnC,SAAS,QAAQ;;;;;;;kFAE3B,sSAAC;wEAAK,WAAU;;0FACd,sSAAC,iSAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EACnB,WAAW,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM1C,sSAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,sSAAC;oDAAI,WAAU;;wDAEZ,SAAS,QAAQ,kBAChB,sSAAC;;8EACC,sSAAC;oEAAM,WAAU;;sFACf,sSAAC,kSAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;wEAAY;;;;;;;8EAGnC,sSAAC;oEAAE,WAAU;8EAA2C,SAAS,QAAQ;;;;;;;;;;;;wDAK5E,SAAS,KAAK,kBACb,sSAAC;;8EACC,sSAAC;oEAAM,WAAU;;sFACf,sSAAC,mSAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;wEAAY;;;;;;;8EAGjC,sSAAC;oEAAI,WAAU;8EACZ,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;;;uCAzChB,SAAS,EAAE;;;;;gCAiD1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;GAnYgB;;QAQwC,yJAAA,CAAA,cAAW;;;KARnD", "debugId": null}}, {"offset": {"line": 2143, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/app/workflow/components/workflow-main.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { CreativesList } from \"./creatives-list\";\nimport { CreativeDetail } from \"./creative-detail\";\nimport { useVideoAPI } from \"../hooks/use-video-api\";\nimport { Alert, AlertDescription } from \"~/components/ui/alert\";\nimport { Loader2 } from \"lucide-react\";\n\nexport interface Creative {\n  id: number;\n  creative: string;\n  art_style?: string;\n  update_time: string;\n}\n\nexport interface Prompt {\n  id: number;\n  creative_id: number;\n  scene: string;\n  prompt: string;\n  update_time: string;\n}\n\nexport interface Scene {\n  id: number;\n  prompt_id: number;\n  image?: string;\n  video?: string;\n  update_time: string;\n}\n\nexport interface Subtitle {\n  id: number;\n  scene_id: number;\n  subtitle?: string;\n  audio?: string;\n  update_time: string;\n}\n\nexport default function WorkflowMain() {\n  const [selectedCreative, setSelectedCreative] = useState<Creative | null>(null);\n  const [creatives, setCreatives] = useState<Creative[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const { fetchCreatives } = useVideoAPI();\n\n  // 加载创意列表\n  useEffect(() => {\n    const loadCreatives = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n        const data = await fetchCreatives();\n        setCreatives(data.items || []);\n      } catch (err) {\n        console.error(\"Failed to load creatives:\", err);\n        setError(\"Failed to load creatives. Please check your API connection.\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadCreatives();\n  }, [fetchCreatives]);\n\n  // 处理创意选择\n  const handleCreativeSelect = (creative: Creative) => {\n    setSelectedCreative(creative);\n  };\n\n  // 处理返回列表\n  const handleBackToList = () => {\n    setSelectedCreative(null);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex h-full w-full items-center justify-center\">\n        <div className=\"flex items-center gap-2\">\n          <Loader2 className=\"h-6 w-6 animate-spin\" />\n          <span className=\"text-lg\">Loading creatives...</span>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"flex h-full w-full items-center justify-center p-6\">\n        <Alert className=\"max-w-md\">\n          <AlertDescription>{error}</AlertDescription>\n        </Alert>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-full w-full\">\n      {selectedCreative ? (\n        <CreativeDetail\n          creative={selectedCreative}\n          onBack={handleBackToList}\n        />\n      ) : (\n        <CreativesList\n          creatives={creatives}\n          onCreativeSelect={handleCreativeSelect}\n          onRefresh={() => window.location.reload()}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAwCe,SAAS;;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAErC,SAAS;IACT,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;wDAAgB;oBACpB,IAAI;wBACF,WAAW;wBACX,SAAS;wBACT,MAAM,OAAO,MAAM;wBACnB,aAAa,KAAK,KAAK,IAAI,EAAE;oBAC/B,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,6BAA6B;wBAC3C,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;iCAAG;QAAC;KAAe;IAEnB,SAAS;IACT,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;IACtB;IAEA,SAAS;IACT,MAAM,mBAAmB;QACvB,oBAAoB;IACtB;IAEA,IAAI,SAAS;QACX,qBACE,sSAAC;YAAI,WAAU;sBACb,cAAA,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,wSAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,sSAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;;;;;;IAIlC;IAEA,IAAI,OAAO;QACT,qBACE,sSAAC;YAAI,WAAU;sBACb,cAAA,sSAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BACf,cAAA,sSAAC,oIAAA,CAAA,mBAAgB;8BAAE;;;;;;;;;;;;;;;;IAI3B;IAEA,qBACE,sSAAC;QAAI,WAAU;kBACZ,iCACC,sSAAC,8JAAA,CAAA,iBAAc;YACb,UAAU;YACV,QAAQ;;;;;iCAGV,sSAAC,6JAAA,CAAA,gBAAa;YACZ,WAAW;YACX,kBAAkB;YAClB,WAAW,IAAM,OAAO,QAAQ,CAAC,MAAM;;;;;;;;;;;AAKjD;GA1EwB;;QAMK,yJAAA,CAAA,cAAW;;;KANhB", "debugId": null}}]}