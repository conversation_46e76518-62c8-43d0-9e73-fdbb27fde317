{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/ui/card.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;;;;;;AAI/B;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,sSAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className=\"sr-only\">Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,sSAAC,kRAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,sSAAC,kRAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,sSAAC,kRAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,sSAAC,kRAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,sSAAC,kRAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,sSAAC;QAAa,aAAU;;0BACtB,sSAAC;;;;;0BACD,sSAAC,kRAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,sSAAC,kRAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,sSAAC,uRAAA,CAAA,QAAK;;;;;0CACN,sSAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,kRAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,sSAAC,kRAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,sSAAC,iRAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/env.js"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { createEnv } from \"@t3-oss/env-nextjs\";\r\nimport { z } from \"zod\";\r\n\r\nexport const env = createEnv({\r\n  /**\r\n   * Specify your server-side environment variables schema here. This way you can ensure the app\r\n   * isn't built with invalid env vars.\r\n   */\r\n  server: {\r\n    NODE_ENV: z.enum([\"development\", \"test\", \"production\"]),\r\n    AMPLITUDE_API_KEY: z.string().optional(),\r\n    GITHUB_OAUTH_TOKEN: z.string().optional(),\r\n  },\r\n\r\n  /**\r\n   * Specify your client-side environment variables schema here. This way you can ensure the app\r\n   * isn't built with invalid env vars. To expose them to the client, prefix them with\r\n   * `NEXT_PUBLIC_`.\r\n   */\r\n  client: {\r\n    NEXT_PUBLIC_API_URL: z.string().optional(),\r\n    NEXT_PUBLIC_STATIC_WEBSITE_ONLY: z.boolean().optional(),\r\n  },\r\n\r\n  /**\r\n   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.\r\n   * middlewares) or client-side so we need to destruct manually.\r\n   */\r\n  runtimeEnv: {\r\n    NODE_ENV: process.env.NODE_ENV,\r\n    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,\r\n    NEXT_PUBLIC_STATIC_WEBSITE_ONLY:\r\n      process.env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY === \"true\",\r\n    AMPLITUDE_API_KEY: process.env.AMPLITUDE_API_KEY,\r\n    GITHUB_OAUTH_TOKEN: process.env.GITHUB_OAUTH_TOKEN,\r\n  },\r\n  /**\r\n   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially\r\n   * useful for Docker builds.\r\n   */\r\n  skipValidation: !!process.env.SKIP_ENV_VALIDATION,\r\n  /**\r\n   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and\r\n   * `SOME_VAR=''` will throw an error.\r\n   */\r\n  emptyStringAsUndefined: true,\r\n});\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AA+BjB;AA7Bd;AACA;;;AAEO,MAAM,MAAM,CAAA,GAAA,wRAAA,CAAA,YAAS,AAAD,EAAE;IAC3B;;;GAGC,GACD,QAAQ;QACN,UAAU,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAe;YAAQ;SAAa;QACtD,mBAAmB,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,oBAAoB,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACzC;IAEA;;;;GAIC,GACD,QAAQ;QACN,qBAAqB,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACxC,iCAAiC,wLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IACvD;IAEA;;;GAGC,GACD,YAAY;QACV,QAAQ;QACR,mBAAmB;QACnB,iCACE,yQAAA,CAAA,UAAO,CAAC,GAAG,CAAC,+BAA+B,KAAK;QAClD,mBAAmB,yQAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB;QAChD,oBAAoB,yQAAA,CAAA,UAAO,CAAC,GAAG,CAAC,kBAAkB;IACpD;IACA;;;GAGC,GACD,gBAAgB,CAAC,CAAC,yQAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB;IACjD;;;GAGC,GACD,wBAAwB;AAC1B", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/app/workflow/hooks/use-video-api.ts"], "sourcesContent": ["import { useCallback } from \"react\";\nimport { env } from \"~/env\";\n\n// API 基础 URL\nconst getApiUrl = () => {\n  return env.NEXT_PUBLIC_API_URL || \"http://localhost:8000/api\";\n};\n\n// API 响应类型\ninterface PaginatedResponse<T> {\n  items: T[];\n  total: number;\n  page: number;\n  size: number;\n  pages: number;\n}\n\ninterface Creative {\n  id: number;\n  creative: string;\n  art_style?: string;\n  update_time: string;\n}\n\ninterface CreativeCreate {\n  creative: string;\n  art_style?: string;\n}\n\ninterface CreativeUpdate {\n  creative?: string;\n  art_style?: string;\n}\n\ninterface GenerationTask {\n  task_id: string;\n  status: \"pending\" | \"running\" | \"completed\" | \"failed\";\n  progress: number;\n  message?: string;\n  result?: any;\n}\n\ninterface Scene {\n  id: number;\n  creative_id: number;\n  scene: string;\n  prompt: string;\n  image?: string;\n  update_time: string;\n}\ninterface SceneCreate {\n  creative_id: number;\n  scene: string;\n  prompt: string;\n}\n\ninterface VideoItem {\n  id: number;\n  scene_id: number;\n  prompt?: string;\n  video?: string;\n  update_time: string;\n}\n\ninterface Subtitle {\n  id: number;\n  video_id: number;\n  subtitle?: string;\n  audio?: string;\n  update_time: string;\n}\n\nexport function useVideoAPI() {\n  // 通用的 fetch 函数 - GET 请求\n  const apiRequest = useCallback(async <T>(endpoint: string): Promise<T> => {\n    const url = `${getApiUrl()}${endpoint}`;\n\n    try {\n      const response = await fetch(url, {\n        method: \"GET\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API request failed for ${endpoint}:`, error);\n      throw error;\n    }\n  }, []);\n\n  // 通用的 POST 请求函数\n  const apiPostRequest = useCallback(async <T>(endpoint: string, data: any): Promise<T> => {\n    const url = `${getApiUrl()}${endpoint}`;\n\n    try {\n      const response = await fetch(url, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API POST request failed for ${endpoint}:`, error);\n      throw error;\n    }\n  }, []);\n\n  // 通用的 PUT 请求函数\n  const apiPutRequest = useCallback(async <T>(endpoint: string, data: any): Promise<T> => {\n    const url = `${getApiUrl()}${endpoint}`;\n\n    try {\n      const response = await fetch(url, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API PUT request failed for ${endpoint}:`, error);\n      throw error;\n    }\n  }, []);\n\n    // 通用的 DELETE 请求函数\n  const apiDeleteRequest = useCallback(async <T>(endpoint: string, data: any): Promise<T> => {\n    const url = `${getApiUrl()}${endpoint}`;\n\n    try {\n      const response = await fetch(url, {\n        method: \"DELETE\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API DELETE request failed for ${endpoint}:`, error);\n      throw error;\n    }\n  }, []);\n\n  // 更新视频\n  const updateVideo = useCallback(\n    async (id: number, data: { prompt?: string; video?: string }): Promise<VideoItem> => {\n      return apiPutRequest(`/video/videos/${id}`, data);\n    },\n    [apiPutRequest]\n  );\n\n  // 重新生成单个视频\n  const regenerateVideo = useCallback(\n    async (videoId: number): Promise<GenerationTask> => {\n      return apiPostRequest(`/video/generate/videos/video/${videoId}`, {});\n    },\n    [apiPostRequest]\n  );\n\n  // 更新字幕\n  const updateSubtitle = useCallback(\n    async (id: number, data: { subtitle?: string; audio?: string }): Promise<Subtitle> => {\n      return apiPutRequest(`/video/subtitles/${id}`, data);\n    },\n    [apiPutRequest]\n  );\n\n  // 生成主角图片\n  const generateCharacterImage = useCallback(\n    async (creativeId: number): Promise<GenerationTask> => {\n      return apiPostRequest(`/video/generate/character-image/${creativeId}`, {});\n    },\n    [apiPostRequest]\n  );\n\n  // 重新生成音频\n  const regenerateAudio = useCallback(\n    async (subtitleId: number): Promise<GenerationTask> => {\n      return apiPostRequest(`/video/generate/audios/subtitle/${subtitleId}`, {});\n    },\n    [apiPostRequest]\n  );\n\n  // 获取创意列表\n  const fetchCreatives = useCallback(\n    async (page = 1, size = 20): Promise<PaginatedResponse<Creative>> => {\n      return apiRequest(`/video/creatives?page=${page}&size=${size}`);\n    },\n    [apiRequest]\n  );\n\n  // 获取单个创意\n  const fetchCreative = useCallback(\n    async (id: number): Promise<Creative> => {\n      return apiRequest(`/video/creatives/${id}`);\n    },\n    [apiRequest]\n  );\n\n  // 获取创意完整信息（包含场景、视频、字幕）\n  const fetchCreativeComplete = useCallback(\n    async (id: number): Promise<{\n      creative: Creative;\n      scenes: Scene[];\n      videos: VideoItem[];\n      subtitles: Subtitle[];\n    }> => {\n      return apiRequest(`/video/creatives/${id}/complete`);\n    },\n    [apiRequest]\n  );\n\n  // 创建新创意\n  const createCreative = useCallback(\n    async (data: CreativeCreate): Promise<Creative> => {\n      return apiPostRequest(`/video/creatives`, data);\n    },\n    [apiPostRequest]\n  );\n\n  // 更新创意\n  const updateCreative = useCallback(\n    async (id: number, data: CreativeUpdate): Promise<Creative> => {\n      return apiPutRequest(`/video/creatives/${id}`, data);\n    },\n    [apiPutRequest]\n  );\n\n  // 获取创意的场景列表\n  const fetchScenes = useCallback(\n    async (creativeId: number, page = 1, size = 50): Promise<PaginatedResponse<Scene>> => {\n      return apiRequest(`/video/scenes?creative_id=${creativeId}&page=${page}&size=${size}`);\n    },\n    [apiRequest]\n  );\n\n  // 更新场景\n  const updateScene = useCallback(\n    async (id: number, data: { scene?: string; prompt?: string }): Promise<Scene> => {\n      return apiPutRequest(`/video/scenes/${id}`, data);\n    },\n    [apiPutRequest]\n  );\n\n  // 添加场景\n  const addScene = useCallback(\n    async (data: SceneCreate): Promise<Scene> => {\n      return apiPostRequest(`/video/scenes`, data);\n    },\n    [apiPostRequest]\n  );\n\n  // 删除场景\n  const deleteScene = useCallback(\n    async (id: number): Promise<void> => {\n      return apiDeleteRequest(`/video/scenes/${id}`, {});\n    },\n    [apiDeleteRequest]\n  );\n\n  // 获取视频列表\n  const fetchVideos = useCallback(\n    async (creativeId?: number, sceneId?: number, page = 1, size = 50): Promise<PaginatedResponse<VideoItem>> => {\n      const params = new URLSearchParams();\n      if (creativeId) params.append('creative_id', creativeId.toString());\n      if (sceneId) params.append('scene_id', sceneId.toString());\n      params.append('page', page.toString());\n      params.append('size', size.toString());\n      return apiRequest(`/video/videos?${params.toString()}`);\n    },\n    [apiRequest]\n  );\n\n  // 获取字幕列表\n  const fetchSubtitles = useCallback(\n    async (creativeId?: number, videoId?: number, page = 1, size = 50): Promise<PaginatedResponse<Subtitle>> => {\n      const params = new URLSearchParams();\n      if (creativeId) params.append('creative_id', creativeId.toString());\n      if (videoId) params.append('video_id', videoId.toString());\n      params.append('page', page.toString());\n      params.append('size', size.toString());\n      return apiRequest(`/video/subtitles?${params.toString()}`);\n    },\n    [apiRequest]\n  );\n\n  // 生成场景\n  const generateScenes = useCallback(\n    async (creativeId: number): Promise<GenerationTask> => {\n      return apiPostRequest(`/video/generate/scenes`, { creative_id: creativeId });\n    },\n    [apiPostRequest]\n  );\n\n  // 生成图片\n  const generateImages = useCallback(\n    async (creativeId: number): Promise<GenerationTask> => {\n      return apiPostRequest(`/video/generate/images`, { creative_id: creativeId });\n    },\n    [apiPostRequest]\n  );\n\n  // 重新生成单个场景的图片\n  const regenerateSceneImage = useCallback(\n    async (sceneId: number): Promise<GenerationTask> => {\n      return apiPostRequest(`/video/generate/images/scene/${sceneId}`, {});\n    },\n    [apiPostRequest]\n  );\n\n  // 生成视频\n  const generateVideos = useCallback(\n    async (creativeId: number): Promise<GenerationTask> => {\n      return apiPostRequest(`/video/generate/videos`, { creative_id: creativeId });\n    },\n    [apiPostRequest]\n  );\n\n  // 生成字幕\n  const generateSubtitles = useCallback(\n    async (creativeId: number): Promise<GenerationTask> => {\n      return apiPostRequest(`/video/generate/subtitles`, { creative_id: creativeId });\n    },\n    [apiPostRequest]\n  );\n\n  // 获取任务状态\n  const getTaskStatus = useCallback(\n    async (taskId: string): Promise<GenerationTask> => {\n      return apiRequest(`/video/tasks/${taskId}`);\n    },\n    [apiRequest]\n  );\n\n  return {\n    fetchCreatives,\n    fetchCreative,\n    fetchCreativeComplete,\n    createCreative,\n    updateCreative,\n    fetchScenes,\n    updateScene,\n    addScene,\n    deleteScene,\n    fetchVideos,\n    fetchSubtitles,\n    generateScenes,\n    generateImages,\n    regenerateSceneImage,\n    generateVideos,\n    generateSubtitles,\n    getTaskStatus,\n    updateVideo,\n    regenerateVideo,\n    updateSubtitle,\n    generateCharacterImage,\n    regenerateAudio,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;AAEA,aAAa;AACb,MAAM,YAAY;IAChB,OAAO,6GAAA,CAAA,MAAG,CAAC,mBAAmB,IAAI;AACpC;AAkEO,SAAS;;IACd,wBAAwB;IACxB,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;+CAAE,OAAU;YACvC,MAAM,MAAM,GAAG,cAAc,UAAU;YAEvC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,KAAK;oBAChC,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;gBAC1D;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC,EAAE;gBACrD,MAAM;YACR;QACF;8CAAG,EAAE;IAEL,gBAAgB;IAChB,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAAE,OAAU,UAAkB;YAC7D,MAAM,MAAM,GAAG,cAAc,UAAU;YAEvC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,KAAK;oBAChC,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;gBAC1D;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,SAAS,CAAC,CAAC,EAAE;gBAC1D,MAAM;YACR;QACF;kDAAG,EAAE;IAEL,eAAe;IACf,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;kDAAE,OAAU,UAAkB;YAC5D,MAAM,MAAM,GAAG,cAAc,UAAU;YAEvC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,KAAK;oBAChC,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;gBAC1D;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,SAAS,CAAC,CAAC,EAAE;gBACzD,MAAM;YACR;QACF;iDAAG,EAAE;IAEH,kBAAkB;IACpB,MAAM,mBAAmB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;qDAAE,OAAU,UAAkB;YAC/D,MAAM,MAAM,GAAG,cAAc,UAAU;YAEvC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,KAAK;oBAChC,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;gBAC1D;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,SAAS,CAAC,CAAC,EAAE;gBAC5D,MAAM;YACR;QACF;oDAAG,EAAE;IAEL,OAAO;IACP,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;gDAC5B,OAAO,IAAY;YACjB,OAAO,cAAc,CAAC,cAAc,EAAE,IAAI,EAAE;QAC9C;+CACA;QAAC;KAAc;IAGjB,WAAW;IACX,MAAM,kBAAkB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;oDAChC,OAAO;YACL,OAAO,eAAe,CAAC,6BAA6B,EAAE,SAAS,EAAE,CAAC;QACpE;mDACA;QAAC;KAAe;IAGlB,OAAO;IACP,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAC/B,OAAO,IAAY;YACjB,OAAO,cAAc,CAAC,iBAAiB,EAAE,IAAI,EAAE;QACjD;kDACA;QAAC;KAAc;IAGjB,SAAS;IACT,MAAM,yBAAyB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;2DACvC,OAAO;YACL,OAAO,eAAe,CAAC,gCAAgC,EAAE,YAAY,EAAE,CAAC;QAC1E;0DACA;QAAC;KAAe;IAGlB,SAAS;IACT,MAAM,kBAAkB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;oDAChC,OAAO;YACL,OAAO,eAAe,CAAC,gCAAgC,EAAE,YAAY,EAAE,CAAC;QAC1E;mDACA;QAAC;KAAe;IAGlB,SAAS;IACT,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAC/B,OAAO,OAAO,CAAC,EAAE,OAAO,EAAE;YACxB,OAAO,WAAW,CAAC,sBAAsB,EAAE,KAAK,MAAM,EAAE,MAAM;QAChE;kDACA;QAAC;KAAW;IAGd,SAAS;IACT,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;kDAC9B,OAAO;YACL,OAAO,WAAW,CAAC,iBAAiB,EAAE,IAAI;QAC5C;iDACA;QAAC;KAAW;IAGd,uBAAuB;IACvB,MAAM,wBAAwB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;0DACtC,OAAO;YAML,OAAO,WAAW,CAAC,iBAAiB,EAAE,GAAG,SAAS,CAAC;QACrD;yDACA;QAAC;KAAW;IAGd,QAAQ;IACR,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAC/B,OAAO;YACL,OAAO,eAAe,CAAC,gBAAgB,CAAC,EAAE;QAC5C;kDACA;QAAC;KAAe;IAGlB,OAAO;IACP,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAC/B,OAAO,IAAY;YACjB,OAAO,cAAc,CAAC,iBAAiB,EAAE,IAAI,EAAE;QACjD;kDACA;QAAC;KAAc;IAGjB,YAAY;IACZ,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;gDAC5B,OAAO,YAAoB,OAAO,CAAC,EAAE,OAAO,EAAE;YAC5C,OAAO,WAAW,CAAC,0BAA0B,EAAE,WAAW,MAAM,EAAE,KAAK,MAAM,EAAE,MAAM;QACvF;+CACA;QAAC;KAAW;IAGd,OAAO;IACP,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;gDAC5B,OAAO,IAAY;YACjB,OAAO,cAAc,CAAC,cAAc,EAAE,IAAI,EAAE;QAC9C;+CACA;QAAC;KAAc;IAGjB,OAAO;IACP,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;6CACzB,OAAO;YACL,OAAO,eAAe,CAAC,aAAa,CAAC,EAAE;QACzC;4CACA;QAAC;KAAe;IAGlB,OAAO;IACP,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;gDAC5B,OAAO;YACL,OAAO,iBAAiB,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC;QAClD;+CACA;QAAC;KAAiB;IAGpB,SAAS;IACT,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;gDAC5B,OAAO,YAAqB,SAAkB,OAAO,CAAC,EAAE,OAAO,EAAE;YAC/D,MAAM,SAAS,IAAI;YACnB,IAAI,YAAY,OAAO,MAAM,CAAC,eAAe,WAAW,QAAQ;YAChE,IAAI,SAAS,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;YACvD,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ;YACnC,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ;YACnC,OAAO,WAAW,CAAC,cAAc,EAAE,OAAO,QAAQ,IAAI;QACxD;+CACA;QAAC;KAAW;IAGd,SAAS;IACT,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAC/B,OAAO,YAAqB,SAAkB,OAAO,CAAC,EAAE,OAAO,EAAE;YAC/D,MAAM,SAAS,IAAI;YACnB,IAAI,YAAY,OAAO,MAAM,CAAC,eAAe,WAAW,QAAQ;YAChE,IAAI,SAAS,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;YACvD,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ;YACnC,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ;YACnC,OAAO,WAAW,CAAC,iBAAiB,EAAE,OAAO,QAAQ,IAAI;QAC3D;kDACA;QAAC;KAAW;IAGd,OAAO;IACP,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAC/B,OAAO;YACL,OAAO,eAAe,CAAC,sBAAsB,CAAC,EAAE;gBAAE,aAAa;YAAW;QAC5E;kDACA;QAAC;KAAe;IAGlB,OAAO;IACP,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAC/B,OAAO;YACL,OAAO,eAAe,CAAC,sBAAsB,CAAC,EAAE;gBAAE,aAAa;YAAW;QAC5E;kDACA;QAAC;KAAe;IAGlB,cAAc;IACd,MAAM,uBAAuB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;yDACrC,OAAO;YACL,OAAO,eAAe,CAAC,6BAA6B,EAAE,SAAS,EAAE,CAAC;QACpE;wDACA;QAAC;KAAe;IAGlB,OAAO;IACP,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAC/B,OAAO;YACL,OAAO,eAAe,CAAC,sBAAsB,CAAC,EAAE;gBAAE,aAAa;YAAW;QAC5E;kDACA;QAAC;KAAe;IAGlB,OAAO;IACP,MAAM,oBAAoB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;sDAClC,OAAO;YACL,OAAO,eAAe,CAAC,yBAAyB,CAAC,EAAE;gBAAE,aAAa;YAAW;QAC/E;qDACA;QAAC;KAAe;IAGlB,SAAS;IACT,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;kDAC9B,OAAO;YACL,OAAO,WAAW,CAAC,aAAa,EAAE,QAAQ;QAC5C;iDACA;QAAC;KAAW;IAGd,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAvTgB", "debugId": null}}, {"offset": {"line": 859, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"~/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,2KACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,sSAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 936, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/app/workflow/components/create-creative-dialog.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { useTranslations } from \"next-intl\";\nimport { Button } from \"~/components/ui/button\";\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  Di<PERSON>Footer,\n  <PERSON><PERSON>Header,\n  <PERSON><PERSON>T<PERSON><PERSON>,\n  DialogTrigger,\n} from \"~/components/ui/dialog\";\nimport { Input } from \"~/components/ui/input\";\nimport { Label } from \"~/components/ui/label\";\nimport { Textarea } from \"~/components/ui/textarea\";\nimport { Plus, Loader2 } from \"lucide-react\";\nimport { useVideoAPI } from \"../hooks/use-video-api\";\nimport { Alert, AlertDescription } from \"~/components/ui/alert\";\n\ninterface CreateCreativeDialogProps {\n  onCreativeCreated: () => void;\n}\n\nexport function CreateCreativeDialog({ onCreativeCreated }: CreateCreativeDialogProps) {\n  const t = useTranslations(\"workflow.creatives.create\");\n  const tCreatives = useTranslations(\"workflow.creatives\");\n  const [open, setOpen] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [formData, setFormData] = useState({\n    creative: \"\",\n    art_style: \"\",\n  });\n\n  const { createCreative } = useVideoAPI();\n\n  // 处理表单提交\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!formData.creative.trim()) {\n      setError(t(\"errorRequired\"));\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      const data = {\n        creative: formData.creative.trim(),\n        art_style: formData.art_style.trim() || undefined,\n      };\n\n      await createCreative(data);\n      \n      // 成功后重置表单并关闭对话框\n      setFormData({ creative: \"\", art_style: \"\" });\n      setOpen(false);\n      onCreativeCreated();\n    } catch (err) {\n      console.error(\"Failed to create creative:\", err);\n      setError(t(\"errorFailed\"));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 处理输入变化\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    // 清除错误信息\n    if (error) setError(null);\n  };\n\n  // 重置表单\n  const resetForm = () => {\n    setFormData({ creative: \"\", art_style: \"\" });\n    setError(null);\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={(newOpen) => {\n      setOpen(newOpen);\n      if (!newOpen) {\n        resetForm();\n      }\n    }}>\n      <DialogTrigger asChild>\n        <Button className=\"gap-2\">\n          <Plus className=\"h-4 w-4\" />\n          {tCreatives(\"newCreative\")}\n        </Button>\n      </DialogTrigger>\n\n      <DialogContent className=\"sm:max-w-[500px]\">\n        <DialogHeader>\n          <DialogTitle>{t(\"title\")}</DialogTitle>\n          <DialogDescription>\n            {t(\"description\")}\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* 错误提示 */}\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* 创意描述 */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"creative\">{t(\"creativeLabel\")} {t(\"required\")}</Label>\n            <Textarea\n              id=\"creative\"\n              placeholder={t(\"creativePlaceholder\")}\n              value={formData.creative}\n              onChange={(e) => handleInputChange(\"creative\", e.target.value)}\n              rows={4}\n              disabled={loading}\n              className=\"resize-none\"\n            />\n          </div>\n\n          {/* 艺术风格 */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"art_style\">{t(\"artStyleLabel\")}</Label>\n            <Input\n              id=\"art_style\"\n              placeholder={t(\"artStylePlaceholder\")}\n              value={formData.art_style}\n              onChange={(e) => handleInputChange(\"art_style\", e.target.value)}\n              disabled={loading}\n            />\n          </div>\n\n          <DialogFooter>\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={() => setOpen(false)}\n              disabled={loading}\n            >\n              {t(\"cancel\")}\n            </Button>\n            <Button type=\"submit\" disabled={loading || !formData.creative.trim()}>\n              {loading ? (\n                <>\n                  <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n                  {t(\"creating\")}\n                </>\n              ) : (\n                <>\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  {t(\"create\")}\n                </>\n              )}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AASA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AAnBA;;;;;;;;;;;AAyBO,SAAS,qBAAqB,EAAE,iBAAiB,EAA6B;;IACnF,MAAM,IAAI,CAAA,GAAA,wTAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,wTAAA,CAAA,kBAAe,AAAD,EAAE;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,WAAW;IACb;IAEA,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAErC,SAAS;IACT,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC7B,SAAS,EAAE;YACX;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,OAAO;gBACX,UAAU,SAAS,QAAQ,CAAC,IAAI;gBAChC,WAAW,SAAS,SAAS,CAAC,IAAI,MAAM;YAC1C;YAEA,MAAM,eAAe;YAErB,gBAAgB;YAChB,YAAY;gBAAE,UAAU;gBAAI,WAAW;YAAG;YAC1C,QAAQ;YACR;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS,EAAE;QACb,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,SAAS;QACT,IAAI,OAAO,SAAS;IACtB;IAEA,OAAO;IACP,MAAM,YAAY;QAChB,YAAY;YAAE,UAAU;YAAI,WAAW;QAAG;QAC1C,SAAS;IACX;IAEA,qBACE,sSAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc,CAAC;YACjC,QAAQ;YACR,IAAI,CAAC,SAAS;gBACZ;YACF;QACF;;0BACE,sSAAC,qIAAA,CAAA,gBAAa;gBAAC,OAAO;0BACpB,cAAA,sSAAC,qIAAA,CAAA,SAAM;oBAAC,WAAU;;sCAChB,sSAAC,yRAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,WAAW;;;;;;;;;;;;0BAIhB,sSAAC,qIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,sSAAC,qIAAA,CAAA,eAAY;;0CACX,sSAAC,qIAAA,CAAA,cAAW;0CAAE,EAAE;;;;;;0CAChB,sSAAC,qIAAA,CAAA,oBAAiB;0CACf,EAAE;;;;;;;;;;;;kCAIP,sSAAC;wBAAK,UAAU;wBAAc,WAAU;;4BAErC,uBACC,sSAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;0CACb,cAAA,sSAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;0CAKvB,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAY,EAAE;4CAAiB;4CAAE,EAAE;;;;;;;kDAClD,sSAAC,uIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,aAAa,EAAE;wCACf,OAAO,SAAS,QAAQ;wCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC7D,MAAM;wCACN,UAAU;wCACV,WAAU;;;;;;;;;;;;0CAKd,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAa,EAAE;;;;;;kDAC9B,sSAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAa,EAAE;wCACf,OAAO,SAAS,SAAS;wCACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC9D,UAAU;;;;;;;;;;;;0CAId,sSAAC,qIAAA,CAAA,eAAY;;kDACX,sSAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS,IAAM,QAAQ;wCACvB,UAAU;kDAET,EAAE;;;;;;kDAEL,sSAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,UAAU,WAAW,CAAC,SAAS,QAAQ,CAAC,IAAI;kDAC/D,wBACC;;8DACE,sSAAC,wSAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAClB,EAAE;;yEAGL;;8DACE,sSAAC,yRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB;GA7IgB;;QACJ,wTAAA,CAAA,kBAAe;QACN,wTAAA,CAAA,kBAAe;QASP,yJAAA,CAAA,cAAW;;;KAXxB", "debugId": null}}, {"offset": {"line": 1248, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/app/workflow/components/creatives-list.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"~/components/ui/card\";\nimport { <PERSON><PERSON> } from \"~/components/ui/button\";\nimport { Badge } from \"~/components/ui/badge\";\nimport { Input } from \"~/components/ui/input\";\nimport { RefreshCw, Search, Video, Calendar } from \"lucide-react\";\nimport { Creative } from \"./workflow-main\";\nimport { CreateCreativeDialog } from \"./create-creative-dialog\";\n\ninterface CreativesListProps {\n  creatives: Creative[];\n  onCreativeSelect: (creative: Creative) => void;\n  onRefresh: () => void;\n  onCreativeCreated: () => void;\n}\n\nexport function CreativesList({ creatives, onCreativeSelect, onRefresh, onCreativeCreated }: CreativesListProps) {\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [isRefreshing, setIsRefreshing] = useState(false);\n\n  // 过滤创意\n  const filteredCreatives = creatives.filter(creative =>\n    creative.creative.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    (creative.art_style && creative.art_style.toLowerCase().includes(searchTerm.toLowerCase()))\n  );\n\n  // 处理刷新\n  const handleRefresh = async () => {\n    setIsRefreshing(true);\n    try {\n      await onRefresh();\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n\n  // 格式化日期\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString(\"zh-CN\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* 工具栏 - 简化版本 */}\n      <div className=\"flex items-center justify-between p-6 border-b bg-muted/30 flex-shrink-0\">\n        <div className=\"flex items-center gap-4\">\n          <h1 className=\"text-2xl font-bold\">Video Creatives</h1>\n          <Badge variant=\"secondary\" className=\"text-sm\">\n            {filteredCreatives.length} items\n          </Badge>\n        </div>\n\n        <div className=\"flex items-center gap-3\">\n          {/* 搜索框 */}\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Search creatives...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10 w-64\"\n            />\n          </div>\n\n          {/* 新增创意按钮 */}\n          <CreateCreativeDialog onCreativeCreated={onCreativeCreated} />\n\n          {/* 刷新按钮 */}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={handleRefresh}\n            disabled={isRefreshing}\n          >\n            <RefreshCw className={`h-4 w-4 ${isRefreshing ? \"animate-spin\" : \"\"}`} />\n            Refresh\n          </Button>\n        </div>\n      </div>\n\n      {/* 创意列表 */}\n      <div className=\"flex-1 overflow-auto p-6\">\n        {filteredCreatives.length === 0 ? (\n          <div className=\"flex flex-col items-center justify-center min-h-[400px] text-center\">\n            <Video className=\"h-16 w-16 text-muted-foreground mb-4\" />\n            <h3 className=\"text-lg font-semibold mb-2\">\n              {searchTerm ? \"No matching creatives found\" : \"No creatives yet\"}\n            </h3>\n            <p className=\"text-muted-foreground mb-4\">\n              {searchTerm\n                ? \"Try adjusting your search terms\"\n                : \"Create your first video creative to get started\"\n              }\n            </p>\n            {searchTerm && (\n              <Button variant=\"outline\" onClick={() => setSearchTerm(\"\")}>\n                Clear search\n              </Button>\n            )}\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {filteredCreatives.map((creative) => (\n              <Card\n                key={creative.id}\n                className=\"cursor-pointer transition-all hover:shadow-lg hover:scale-[1.02] border-2 hover:border-primary/50\"\n                onClick={() => onCreativeSelect(creative)}\n              >\n                <CardHeader className=\"pb-3\">\n                  <div className=\"flex items-start justify-between\">\n                    <CardTitle className=\"text-lg line-clamp-2 leading-tight\">\n                      {creative.creative}\n                    </CardTitle>\n                    <Badge variant=\"outline\" className=\"ml-2 shrink-0\">\n                      #{creative.id}\n                    </Badge>\n                  </div>\n                </CardHeader>\n                \n                <CardContent className=\"pt-0\">\n                  <div className=\"space-y-3\">\n                    {/* 艺术风格 */}\n                    {creative.art_style && (\n                      <div className=\"flex items-center gap-2\">\n                        <Badge variant=\"secondary\" className=\"text-xs\">\n                          {creative.art_style}\n                        </Badge>\n                      </div>\n                    )}\n                    \n                    {/* 更新时间 */}\n                    <div className=\"flex items-center gap-2 text-sm text-muted-foreground\">\n                      <Calendar className=\"h-3 w-3\" />\n                      <span>{formatDate(creative.update_time)}</span>\n                    </div>\n                    \n                    {/* 操作按钮 */}\n                    <Button \n                      variant=\"outline\" \n                      size=\"sm\" \n                      className=\"w-full mt-3\"\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        onCreativeSelect(creative);\n                      }}\n                    >\n                      View Details\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;;;AATA;;;;;;;;AAkBO,SAAS,cAAc,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,iBAAiB,EAAsB;;IAC7G,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,OAAO;IACP,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA,WACzC,SAAS,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,SAAS,SAAS,IAAI,SAAS,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAGzF,OAAO;IACP,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,IAAI;YACF,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,qBACE,sSAAC;QAAI,WAAU;;0BAEb,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,sSAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;;oCAClC,kBAAkB,MAAM;oCAAC;;;;;;;;;;;;;kCAI9B,sSAAC;wBAAI,WAAU;;0CAEb,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,6RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,sSAAC,oIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAKd,sSAAC,wKAAA,CAAA,uBAAoB;gCAAC,mBAAmB;;;;;;0CAGzC,sSAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;;kDAEV,sSAAC,uSAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,QAAQ,EAAE,eAAe,iBAAiB,IAAI;;;;;;oCAAI;;;;;;;;;;;;;;;;;;;0BAO/E,sSAAC;gBAAI,WAAU;0BACZ,kBAAkB,MAAM,KAAK,kBAC5B,sSAAC;oBAAI,WAAU;;sCACb,sSAAC,2RAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,sSAAC;4BAAG,WAAU;sCACX,aAAa,gCAAgC;;;;;;sCAEhD,sSAAC;4BAAE,WAAU;sCACV,aACG,oCACA;;;;;;wBAGL,4BACC,sSAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS,IAAM,cAAc;sCAAK;;;;;;;;;;;yCAMhE,sSAAC;oBAAI,WAAU;8BACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,sSAAC,mIAAA,CAAA,OAAI;4BAEH,WAAU;4BACV,SAAS,IAAM,iBAAiB;;8CAEhC,sSAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,SAAS,QAAQ;;;;;;0DAEpB,sSAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;oDAAgB;oDAC/C,SAAS,EAAE;;;;;;;;;;;;;;;;;;8CAKnB,sSAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,sSAAC;wCAAI,WAAU;;4CAEZ,SAAS,SAAS,kBACjB,sSAAC;gDAAI,WAAU;0DACb,cAAA,sSAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAClC,SAAS,SAAS;;;;;;;;;;;0DAMzB,sSAAC;gDAAI,WAAU;;kEACb,sSAAC,iSAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,sSAAC;kEAAM,WAAW,SAAS,WAAW;;;;;;;;;;;;0DAIxC,sSAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,iBAAiB;gDACnB;0DACD;;;;;;;;;;;;;;;;;;2BAzCA,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;AAqDhC;GAnJgB;KAAA", "debugId": null}}, {"offset": {"line": 1589, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/ui/tabs.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;;;AAK/B;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,sSAAC,mRAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,sSAAC,mRAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,mRAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,mRAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1667, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/ui/progress.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"~/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,sSAAC,iRAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,sSAAC,iRAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,iRAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1712, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/app/workflow/components/progress-bar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\nimport { Progress } from \"~/components/ui/progress\";\nimport { Alert, AlertDescription } from \"~/components/ui/alert\";\nimport { CheckCircle, XCircle, Loader2 } from \"lucide-react\";\nimport { env } from \"~/env\";\n\n// API 基础 URL\nconst getApiUrl = () => {\n  return env.NEXT_PUBLIC_API_URL || \"http://localhost:8000/api\";\n};\n\ninterface ProgressData {\n  step: number;\n  total: number;\n  message: string;\n  timestamp: string;\n  type?: \"complete\";\n  error?: string;\n}\n\ninterface ProgressBarProps {\n  taskId: string | null;\n  onComplete?: () => void;\n  onError?: (error: string) => void;\n}\n\nexport function ProgressBar({ taskId, onComplete, onError }: ProgressBarProps) {\n  const [progress, setProgress] = useState<ProgressData | null>(null);\n  const [isComplete, setIsComplete] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (!taskId) {\n      setProgress(null);\n      setIsComplete(false);\n      setError(null);\n      return;\n    }\n\n    let eventSource: EventSource | null = null;\n    let timeoutId: NodeJS.Timeout | null = null;\n\n    const connectToProgress = () => {\n      try {\n        eventSource = new EventSource(`${getApiUrl()}/video/progress/${taskId}`);\n\n        eventSource.onmessage = (event) => {\n          try {\n            const data: ProgressData = JSON.parse(event.data);\n            setProgress(data);\n\n            // 检查是否完成\n            if (data.type === \"complete\") {\n              setIsComplete(true);\n              eventSource?.close();\n\n              // 延迟调用完成回调，让用户看到完成状态\n              setTimeout(() => {\n                onComplete?.();\n              }, 1000);\n            }\n\n            // 检查是否有错误\n            if (data.error) {\n              setError(data.error);\n              eventSource?.close();\n              onError?.(data.error);\n            }\n          } catch (err) {\n            console.error(\"Failed to parse progress data:\", err);\n          }\n        };\n\n        eventSource.onerror = (event) => {\n          console.error(\"EventSource error:\", event);\n          eventSource?.close();\n\n          // 如果还没有完成，设置超时重连\n          if (!isComplete && !error) {\n            timeoutId = setTimeout(() => {\n              connectToProgress();\n            }, 2000);\n          }\n        };\n\n        // 设置总体超时（10分钟）\n        timeoutId = setTimeout(() => {\n          eventSource?.close();\n          setError(\"任务执行超时\");\n          onError?.(\"任务执行超时\");\n        }, 10 * 60 * 1000);\n\n      } catch (err) {\n        console.error(\"Failed to create EventSource:\", err);\n        setError(\"无法连接到进度服务\");\n        onError?.(\"无法连接到进度服务\");\n      }\n    };\n\n    connectToProgress();\n\n    // 清理函数\n    return () => {\n      eventSource?.close();\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n    };\n  }, [taskId, onComplete, onError, isComplete, error]);\n\n  // 如果没有任务ID，不显示进度条\n  if (!taskId) {\n    return null;\n  }\n\n  // 计算进度百分比\n  const progressPercentage = progress ? Math.round((progress.step / progress.total) * 100) : 0;\n\n  return (\n    <div className=\"space-y-3 p-4 border rounded-lg bg-muted/50\">\n      {/* 进度条 */}\n      <div className=\"space-y-2\">\n        <div className=\"flex items-center justify-between text-sm\">\n          <span className=\"font-medium\">\n            {isComplete ? \"任务完成\" : error ? \"任务失败\" : \"正在处理...\"}\n          </span>\n          <span className=\"text-muted-foreground\">\n            {progress ? `${progress.step}/${progress.total}` : \"0/1\"}\n          </span>\n        </div>\n\n        <Progress\n          value={progressPercentage}\n          className=\"h-2\"\n        />\n\n        <div className=\"flex items-center justify-between text-xs text-muted-foreground\">\n          <span>{progressPercentage}%</span>\n          <span>任务ID: {taskId.slice(0, 8)}...</span>\n        </div>\n      </div>\n\n      {/* 状态消息 */}\n      <div className=\"flex items-center gap-2 text-sm\">\n        {isComplete ? (\n          <CheckCircle className=\"h-4 w-4 text-green-500\" />\n        ) : error ? (\n          <XCircle className=\"h-4 w-4 text-red-500\" />\n        ) : (\n          <Loader2 className=\"h-4 w-4 animate-spin text-blue-500\" />\n        )}\n\n        <span className={`${\n          isComplete ? \"text-green-700\" :\n          error ? \"text-red-700\" :\n          \"text-blue-700\"\n        }`}>\n          {progress?.message || \"等待任务开始...\"}\n        </span>\n      </div>\n\n      {/* 错误提示 */}\n      {error && (\n        <Alert variant=\"destructive\">\n          <XCircle className=\"h-4 w-4\" />\n          <AlertDescription>\n            {error}\n          </AlertDescription>\n        </Alert>\n      )}\n\n      {/* 完成提示 */}\n      {isComplete && !error && (\n        <Alert>\n          <CheckCircle className=\"h-4 w-4\" />\n          <AlertDescription>\n            任务已成功完成！页面将自动刷新...\n          </AlertDescription>\n        </Alert>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AANA;;;;;;AAQA,aAAa;AACb,MAAM,YAAY;IAChB,OAAO,6GAAA,CAAA,MAAG,CAAC,mBAAmB,IAAI;AACpC;AAiBO,SAAS,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAoB;;IAC3E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,CAAC,QAAQ;gBACX,YAAY;gBACZ,cAAc;gBACd,SAAS;gBACT;YACF;YAEA,IAAI,cAAkC;YACtC,IAAI,YAAmC;YAEvC,MAAM;2DAAoB;oBACxB,IAAI;wBACF,cAAc,IAAI,YAAY,GAAG,YAAY,gBAAgB,EAAE,QAAQ;wBAEvE,YAAY,SAAS;uEAAG,CAAC;gCACvB,IAAI;oCACF,MAAM,OAAqB,KAAK,KAAK,CAAC,MAAM,IAAI;oCAChD,YAAY;oCAEZ,SAAS;oCACT,IAAI,KAAK,IAAI,KAAK,YAAY;wCAC5B,cAAc;wCACd,aAAa;wCAEb,qBAAqB;wCACrB;uFAAW;gDACT;4CACF;sFAAG;oCACL;oCAEA,UAAU;oCACV,IAAI,KAAK,KAAK,EAAE;wCACd,SAAS,KAAK,KAAK;wCACnB,aAAa;wCACb,UAAU,KAAK,KAAK;oCACtB;gCACF,EAAE,OAAO,KAAK;oCACZ,QAAQ,KAAK,CAAC,kCAAkC;gCAClD;4BACF;;wBAEA,YAAY,OAAO;uEAAG,CAAC;gCACrB,QAAQ,KAAK,CAAC,sBAAsB;gCACpC,aAAa;gCAEb,iBAAiB;gCACjB,IAAI,CAAC,cAAc,CAAC,OAAO;oCACzB,YAAY;mFAAW;4CACrB;wCACF;kFAAG;gCACL;4BACF;;wBAEA,eAAe;wBACf,YAAY;uEAAW;gCACrB,aAAa;gCACb,SAAS;gCACT,UAAU;4BACZ;sEAAG,KAAK,KAAK;oBAEf,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,iCAAiC;wBAC/C,SAAS;wBACT,UAAU;oBACZ;gBACF;;YAEA;YAEA,OAAO;YACP;yCAAO;oBACL,aAAa;oBACb,IAAI,WAAW;wBACb,aAAa;oBACf;gBACF;;QACF;gCAAG;QAAC;QAAQ;QAAY;QAAS;QAAY;KAAM;IAEnD,kBAAkB;IAClB,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,UAAU;IACV,MAAM,qBAAqB,WAAW,KAAK,KAAK,CAAC,AAAC,SAAS,IAAI,GAAG,SAAS,KAAK,GAAI,OAAO;IAE3F,qBACE,sSAAC;QAAI,WAAU;;0BAEb,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAK,WAAU;0CACb,aAAa,SAAS,QAAQ,SAAS;;;;;;0CAE1C,sSAAC;gCAAK,WAAU;0CACb,WAAW,GAAG,SAAS,IAAI,CAAC,CAAC,EAAE,SAAS,KAAK,EAAE,GAAG;;;;;;;;;;;;kCAIvD,sSAAC,uIAAA,CAAA,WAAQ;wBACP,OAAO;wBACP,WAAU;;;;;;kCAGZ,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;;oCAAM;oCAAmB;;;;;;;0CAC1B,sSAAC;;oCAAK;oCAAO,OAAO,KAAK,CAAC,GAAG;oCAAG;;;;;;;;;;;;;;;;;;;0BAKpC,sSAAC;gBAAI,WAAU;;oBACZ,2BACC,sSAAC,kTAAA,CAAA,cAAW;wBAAC,WAAU;;;;;+BACrB,sBACF,sSAAC,mSAAA,CAAA,UAAO;wBAAC,WAAU;;;;;6CAEnB,sSAAC,wSAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCAGrB,sSAAC;wBAAK,WAAW,GACf,aAAa,mBACb,QAAQ,iBACR,iBACA;kCACC,UAAU,WAAW;;;;;;;;;;;;YAKzB,uBACC,sSAAC,oIAAA,CAAA,QAAK;gBAAC,SAAQ;;kCACb,sSAAC,mSAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,sSAAC,oIAAA,CAAA,mBAAgB;kCACd;;;;;;;;;;;;YAMN,cAAc,CAAC,uBACd,sSAAC,oIAAA,CAAA,QAAK;;kCACJ,sSAAC,kTAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,sSAAC,oIAAA,CAAA,mBAAgB;kCAAC;;;;;;;;;;;;;;;;;;AAO5B;GA5JgB;KAAA", "debugId": null}}, {"offset": {"line": 2010, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/app/workflow/components/generation-buttons.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { useTranslations } from \"next-intl\";\nimport { Button } from \"~/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"~/components/ui/card\";\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from \"~/components/ui/dialog\";\nimport { FileText, Image, Video, Subtitles, Loader2, Play } from \"lucide-react\";\nimport { useVideoAPI } from \"../hooks/use-video-api\";\nimport { ProgressBar } from \"./progress-bar\";\n\ninterface GenerationButtonsProps {\n  creativeId: number;\n  onRefresh: () => void;\n}\n\nexport function GenerationButtons({ creativeId, onRefresh }: GenerationButtonsProps) {\n  const t = useTranslations(\"workflow.detail.generation\");\n  const [loading, setLoading] = useState<string | null>(null);\n  const [taskId, setTaskId] = useState<string | null>(null);\n\n  // 确认对话框状态\n  const [confirmDialog, setConfirmDialog] = useState<{\n    open: boolean;\n    title: string;\n    description: string;\n    onConfirm: () => void;\n  }>({\n    open: false,\n    title: \"\",\n    description: \"\",\n    onConfirm: () => {}\n  });\n\n  const { generateScenes, generateImages, generateVideos, generateSubtitles } = useVideoAPI();\n\n  // 确认对话框辅助函数\n  const showConfirmDialog = (title: string, description: string, onConfirm: () => void) => {\n    setConfirmDialog({\n      open: true,\n      title,\n      description,\n      onConfirm\n    });\n  };\n\n  const closeConfirmDialog = () => {\n    setConfirmDialog(prev => ({ ...prev, open: false }));\n  };\n\n  // 处理生成操作\n  const handleGenerate = async (type: string, generateFn: () => Promise<any>) => {\n    try {\n      setLoading(type);\n      setTaskId(null); // 重置任务ID\n\n      const result = await generateFn();\n      console.log(`${type} generation completed:`, result);\n\n      // 如果返回了任务ID，设置它以显示进度条\n      if (result.task_id) {\n        setTaskId(result.task_id);\n      } else {\n        // 如果没有任务ID，直接刷新数据\n        onRefresh();\n        setLoading(null);\n      }\n    } catch (error) {\n      console.error(`Failed to generate ${type}:`, error);\n      setLoading(null);\n      setTaskId(null);\n      // 可以在这里添加错误提示\n    }\n  };\n\n  // 进度完成回调\n  const handleProgressComplete = () => {\n    setLoading(null);\n    setTaskId(null);\n    onRefresh();\n  };\n\n  // 进度错误回调\n  const handleProgressError = (error: string) => {\n    setLoading(null);\n    setTaskId(null);\n    console.error('Progress error:', error);\n  };\n\n\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Play className=\"h-5 w-5\" />\n          {t(\"title\")}\n        </CardTitle>\n        <CardDescription>\n          {t(\"description\")}\n        </CardDescription>\n      </CardHeader>\n      \n      <CardContent>\n        {/* 进度条 */}\n        {taskId && (\n          <div className=\"mb-6\">\n            <ProgressBar\n              taskId={taskId}\n              onComplete={handleProgressComplete}\n              onError={handleProgressError}\n            />\n          </div>\n        )}\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          {/* 生成场景按钮 */}\n          <Button\n            variant=\"outline\"\n            className=\"h-auto flex-col gap-2 p-4\"\n            onClick={() => showConfirmDialog(\n              \"生成场景\",\n              \"确定要生成场景吗？这将为创意生成新的场景描述。\",\n              () => {\n                closeConfirmDialog();\n                handleGenerate(\"scenes\", () => generateScenes(creativeId));\n              }\n            )}\n            disabled={loading !== null}\n          >\n            {loading === \"scenes\" ? (\n              <Loader2 className=\"h-6 w-6 animate-spin\" />\n            ) : (\n              <FileText className=\"h-6 w-6\" />\n            )}\n            <div className=\"text-center\">\n              <div className=\"font-medium\">{t(\"generateScenes\")}</div>\n              <div className=\"text-xs text-muted-foreground\">{t(\"generateScenesDesc\")}</div>\n            </div>\n          </Button>\n\n          {/* 生成图片按钮 */}\n          <Button\n            variant=\"outline\"\n            className=\"h-auto flex-col gap-2 p-4\"\n            onClick={() => showConfirmDialog(\n              \"生成图片\",\n              \"确定要生成图片吗？这将为所有场景生成对应的图片。\",\n              () => {\n                closeConfirmDialog();\n                handleGenerate(\"images\", () => generateImages(creativeId));\n              }\n            )}\n            disabled={loading !== null}\n          >\n            {loading === \"images\" ? (\n              <Loader2 className=\"h-6 w-6 animate-spin\" />\n            ) : (\n              <Image className=\"h-6 w-6\" />\n            )}\n            <div className=\"text-center\">\n              <div className=\"font-medium\">{t(\"generateImages\")}</div>\n              <div className=\"text-xs text-muted-foreground\">{t(\"generateImagesDesc\")}</div>\n            </div>\n          </Button>\n\n          {/* 生成视频按钮 */}\n          <Button\n            variant=\"outline\"\n            className=\"h-auto flex-col gap-2 p-4\"\n            onClick={() => showConfirmDialog(\n              \"生成视频\",\n              \"确定要生成视频吗？这将基于场景图片生成对应的视频。\",\n              () => {\n                closeConfirmDialog();\n                handleGenerate(\"videos\", () => generateVideos(creativeId));\n              }\n            )}\n            disabled={loading !== null}\n          >\n            {loading === \"videos\" ? (\n              <Loader2 className=\"h-6 w-6 animate-spin\" />\n            ) : (\n              <Video className=\"h-6 w-6\" />\n            )}\n            <div className=\"text-center\">\n              <div className=\"font-medium\">{t(\"generateVideos\")}</div>\n              <div className=\"text-xs text-muted-foreground\">{t(\"generateVideosDesc\")}</div>\n            </div>\n          </Button>\n\n          {/* 生成字幕按钮 */}\n          <Button\n            variant=\"outline\"\n            className=\"h-auto flex-col gap-2 p-4\"\n            onClick={() => showConfirmDialog(\n              \"生成字幕\",\n              \"确定要生成字幕吗？这将为所有视频生成对应的字幕和音频。\",\n              () => {\n                closeConfirmDialog();\n                handleGenerate(\"subtitles\", () => generateSubtitles(creativeId));\n              }\n            )}\n            disabled={loading !== null}\n          >\n            {loading === \"subtitles\" ? (\n              <Loader2 className=\"h-6 w-6 animate-spin\" />\n            ) : (\n              <Subtitles className=\"h-6 w-6\" />\n            )}\n            <div className=\"text-center\">\n              <div className=\"font-medium\">{t(\"generateSubtitles\")}</div>\n              <div className=\"text-xs text-muted-foreground\">{t(\"generateSubtitlesDesc\")}</div>\n            </div>\n          </Button>\n        </div>\n      </CardContent>\n\n      {/* 确认对话框 */}\n      <Dialog open={confirmDialog.open} onOpenChange={closeConfirmDialog}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>{confirmDialog.title}</DialogTitle>\n            <DialogDescription>{confirmDialog.description}</DialogDescription>\n          </DialogHeader>\n          <DialogFooter>\n            <Button variant=\"outline\" onClick={closeConfirmDialog}>\n              取消\n            </Button>\n            <Button variant=\"destructive\" onClick={confirmDialog.onConfirm}>\n              确认\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AATA;;;;;;;;;AAgBO,SAAS,kBAAkB,EAAE,UAAU,EAAE,SAAS,EAA0B;;IACjF,MAAM,IAAI,CAAA,GAAA,wTAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpD,UAAU;IACV,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAK9C;QACD,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;0CAAE,KAAO;;IACpB;IAEA,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAExF,YAAY;IACZ,MAAM,oBAAoB,CAAC,OAAe,aAAqB;QAC7D,iBAAiB;YACf,MAAM;YACN;YACA;YACA;QACF;IACF;IAEA,MAAM,qBAAqB;QACzB,iBAAiB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAM,CAAC;IACpD;IAEA,SAAS;IACT,MAAM,iBAAiB,OAAO,MAAc;QAC1C,IAAI;YACF,WAAW;YACX,UAAU,OAAO,SAAS;YAE1B,MAAM,SAAS,MAAM;YACrB,QAAQ,GAAG,CAAC,GAAG,KAAK,sBAAsB,CAAC,EAAE;YAE7C,sBAAsB;YACtB,IAAI,OAAO,OAAO,EAAE;gBAClB,UAAU,OAAO,OAAO;YAC1B,OAAO;gBACL,kBAAkB;gBAClB;gBACA,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC,EAAE;YAC7C,WAAW;YACX,UAAU;QACV,cAAc;QAChB;IACF;IAEA,SAAS;IACT,MAAM,yBAAyB;QAC7B,WAAW;QACX,UAAU;QACV;IACF;IAEA,SAAS;IACT,MAAM,sBAAsB,CAAC;QAC3B,WAAW;QACX,UAAU;QACV,QAAQ,KAAK,CAAC,mBAAmB;IACnC;IAIA,qBACE,sSAAC,mIAAA,CAAA,OAAI;;0BACH,sSAAC,mIAAA,CAAA,aAAU;;kCACT,sSAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,sSAAC,yRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,EAAE;;;;;;;kCAEL,sSAAC,mIAAA,CAAA,kBAAe;kCACb,EAAE;;;;;;;;;;;;0BAIP,sSAAC,mIAAA,CAAA,cAAW;;oBAET,wBACC,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC,2JAAA,CAAA,cAAW;4BACV,QAAQ;4BACR,YAAY;4BACZ,SAAS;;;;;;;;;;;kCAKf,sSAAC;wBAAI,WAAU;;0CAEb,sSAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,kBACb,QACA,2BACA;wCACE;wCACA,eAAe,UAAU,IAAM,eAAe;oCAChD;gCAEF,UAAU,YAAY;;oCAErB,YAAY,yBACX,sSAAC,wSAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,sSAAC,qSAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDAEtB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;0DAAe,EAAE;;;;;;0DAChC,sSAAC;gDAAI,WAAU;0DAAiC,EAAE;;;;;;;;;;;;;;;;;;0CAKtD,sSAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,kBACb,QACA,4BACA;wCACE;wCACA,eAAe,UAAU,IAAM,eAAe;oCAChD;gCAEF,UAAU,YAAY;;oCAErB,YAAY,yBACX,sSAAC,wSAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,sSAAC,2RAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDAEnB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;0DAAe,EAAE;;;;;;0DAChC,sSAAC;gDAAI,WAAU;0DAAiC,EAAE;;;;;;;;;;;;;;;;;;0CAKtD,sSAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,kBACb,QACA,6BACA;wCACE;wCACA,eAAe,UAAU,IAAM,eAAe;oCAChD;gCAEF,UAAU,YAAY;;oCAErB,YAAY,yBACX,sSAAC,wSAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,sSAAC,2RAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDAEnB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;0DAAe,EAAE;;;;;;0DAChC,sSAAC;gDAAI,WAAU;0DAAiC,EAAE;;;;;;;;;;;;;;;;;;0CAKtD,sSAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,kBACb,QACA,+BACA;wCACE;wCACA,eAAe,aAAa,IAAM,kBAAkB;oCACtD;gCAEF,UAAU,YAAY;;oCAErB,YAAY,4BACX,sSAAC,wSAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,sSAAC,kSAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDAEvB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;0DAAe,EAAE;;;;;;0DAChC,sSAAC;gDAAI,WAAU;0DAAiC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1D,sSAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM,cAAc,IAAI;gBAAE,cAAc;0BAC9C,cAAA,sSAAC,qIAAA,CAAA,gBAAa;;sCACZ,sSAAC,qIAAA,CAAA,eAAY;;8CACX,sSAAC,qIAAA,CAAA,cAAW;8CAAE,cAAc,KAAK;;;;;;8CACjC,sSAAC,qIAAA,CAAA,oBAAiB;8CAAE,cAAc,WAAW;;;;;;;;;;;;sCAE/C,sSAAC,qIAAA,CAAA,eAAY;;8CACX,sSAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAoB;;;;;;8CAGvD,sSAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAc,SAAS,cAAc,SAAS;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5E;GA7NgB;;QACJ,wTAAA,CAAA,kBAAe;QAiBqD,yJAAA,CAAA,cAAW;;;KAlB3E", "debugId": null}}, {"offset": {"line": 2470, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/app/workflow/components/create-scene-dialog.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { useTranslations } from \"next-intl\";\nimport { Button } from \"~/components/ui/button\";\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  Di<PERSON>Footer,\n  <PERSON><PERSON>Header,\n  <PERSON>alogTitle,\n  DialogTrigger,\n} from \"~/components/ui/dialog\";\nimport { Input } from \"~/components/ui/input\";\nimport { Label } from \"~/components/ui/label\";\nimport { Textarea } from \"~/components/ui/textarea\";\nimport { Plus, Loader2 } from \"lucide-react\";\nimport { useVideoAPI } from \"../hooks/use-video-api\";\nimport { Alert, AlertDescription } from \"~/components/ui/alert\";\n\ninterface CreateSceneDialogProps {\n  creativeId: number;\n  onSceneCreated: () => void;\n}\n\nexport function CreateSceneDialog({ creativeId, onSceneCreated }: CreateSceneDialogProps) {\n  const t = useTranslations(\"workflow.detail.create\");\n  const tScenes = useTranslations(\"workflow.detail\");\n  const [open, setOpen] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [formData, setFormData] = useState({\n    scene: \"\",\n    prompt: \"\",\n  });\n\n  const { addScene } = useVideoAPI();\n\n  // 处理表单提交\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!formData.scene.trim()) {\n      setError(t(\"errorSceneRequired\"));\n      return;\n    }\n\n    if (!formData.prompt.trim()) {\n      setError(t(\"errorPromptRequired\"));\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      const data = {\n        creative_id: creativeId,\n        scene: formData.scene.trim(),\n        prompt: formData.prompt.trim(),\n      };\n\n      await addScene(data);\n      \n      // 成功后重置表单并关闭对话框\n      setFormData({ scene: \"\", prompt: \"\" });\n      setOpen(false);\n      onSceneCreated();\n    } catch (err) {\n      console.error(\"Failed to create scene:\", err);\n      setError(t(\"errorFailed\"));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 处理输入变化\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    // 清除错误信息\n    if (error) setError(null);\n  };\n\n  // 重置表单\n  const resetForm = () => {\n    setFormData({ scene: \"\", prompt: \"\" });\n    setError(null);\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={(newOpen) => {\n      setOpen(newOpen);\n      if (!newOpen) {\n        resetForm();\n      }\n    }}>\n      <DialogTrigger asChild>\n        <Button className=\"gap-2\">\n          <Plus className=\"h-4 w-4\" />\n          {tScenes(\"addScene\")}\n        </Button>\n      </DialogTrigger>\n\n      <DialogContent className=\"sm:max-w-[500px]\">\n        <DialogHeader>\n          <DialogTitle>{t(\"title\")}</DialogTitle>\n          <DialogDescription>\n            {t(\"description\")}\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* 错误提示 */}\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* 场景标题 */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"scene\">{t(\"sceneLabel\")} {t(\"required\")}</Label>\n            <Input\n              id=\"scene\"\n              placeholder={t(\"scenePlaceholder\")}\n              value={formData.scene}\n              onChange={(e) => handleInputChange(\"scene\", e.target.value)}\n              disabled={loading}\n            />\n          </div>\n\n          {/* 场景提示词 */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"prompt\">{t(\"promptLabel\")} {t(\"required\")}</Label>\n            <Textarea\n              id=\"prompt\"\n              placeholder={t(\"promptPlaceholder\")}\n              value={formData.prompt}\n              onChange={(e) => handleInputChange(\"prompt\", e.target.value)}\n              rows={4}\n              disabled={loading}\n              className=\"resize-none\"\n            />\n          </div>\n\n          <DialogFooter>\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={() => setOpen(false)}\n              disabled={loading}\n            >\n              {t(\"cancel\")}\n            </Button>\n            <Button type=\"submit\" disabled={loading || !formData.scene.trim() || !formData.prompt.trim()}>\n              {loading ? (\n                <>\n                  <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n                  {t(\"creating\")}\n                </>\n              ) : (\n                <>\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  {t(\"create\")}\n                </>\n              )}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AASA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AAnBA;;;;;;;;;;;AA0BO,SAAS,kBAAkB,EAAE,UAAU,EAAE,cAAc,EAA0B;;IACtF,MAAM,IAAI,CAAA,GAAA,wTAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,UAAU,CAAA,GAAA,wTAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAE/B,SAAS;IACT,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,SAAS,EAAE;YACX;QACF;QAEA,IAAI,CAAC,SAAS,MAAM,CAAC,IAAI,IAAI;YAC3B,SAAS,EAAE;YACX;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,OAAO;gBACX,aAAa;gBACb,OAAO,SAAS,KAAK,CAAC,IAAI;gBAC1B,QAAQ,SAAS,MAAM,CAAC,IAAI;YAC9B;YAEA,MAAM,SAAS;YAEf,gBAAgB;YAChB,YAAY;gBAAE,OAAO;gBAAI,QAAQ;YAAG;YACpC,QAAQ;YACR;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS,EAAE;QACb,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,SAAS;QACT,IAAI,OAAO,SAAS;IACtB;IAEA,OAAO;IACP,MAAM,YAAY;QAChB,YAAY;YAAE,OAAO;YAAI,QAAQ;QAAG;QACpC,SAAS;IACX;IAEA,qBACE,sSAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc,CAAC;YACjC,QAAQ;YACR,IAAI,CAAC,SAAS;gBACZ;YACF;QACF;;0BACE,sSAAC,qIAAA,CAAA,gBAAa;gBAAC,OAAO;0BACpB,cAAA,sSAAC,qIAAA,CAAA,SAAM;oBAAC,WAAU;;sCAChB,sSAAC,yRAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,QAAQ;;;;;;;;;;;;0BAIb,sSAAC,qIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,sSAAC,qIAAA,CAAA,eAAY;;0CACX,sSAAC,qIAAA,CAAA,cAAW;0CAAE,EAAE;;;;;;0CAChB,sSAAC,qIAAA,CAAA,oBAAiB;0CACf,EAAE;;;;;;;;;;;;kCAIP,sSAAC;wBAAK,UAAU;wBAAc,WAAU;;4BAErC,uBACC,sSAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;0CACb,cAAA,sSAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;0CAKvB,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAS,EAAE;4CAAc;4CAAE,EAAE;;;;;;;kDAC5C,sSAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAa,EAAE;wCACf,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,UAAU;;;;;;;;;;;;0CAKd,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAU,EAAE;4CAAe;4CAAE,EAAE;;;;;;;kDAC9C,sSAAC,uIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,aAAa,EAAE;wCACf,OAAO,SAAS,MAAM;wCACtB,UAAU,CAAC,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,KAAK;wCAC3D,MAAM;wCACN,UAAU;wCACV,WAAU;;;;;;;;;;;;0CAId,sSAAC,qIAAA,CAAA,eAAY;;kDACX,sSAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS,IAAM,QAAQ;wCACvB,UAAU;kDAET,EAAE;;;;;;kDAEL,sSAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,UAAU,WAAW,CAAC,SAAS,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,MAAM,CAAC,IAAI;kDACvF,wBACC;;8DACE,sSAAC,wSAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAClB,EAAE;;yEAGL;;8DACE,sSAAC,yRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB;GAnJgB;;QACJ,wTAAA,CAAA,kBAAe;QACT,wTAAA,CAAA,kBAAe;QASV,yJAAA,CAAA,cAAW;;;KAXlB", "debugId": null}}, {"offset": {"line": 2791, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/app/workflow/components/creative-detail.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useTranslations } from \"next-intl\";\nimport { But<PERSON> } from \"~/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"~/components/ui/card\";\nimport { Badge } from \"~/components/ui/badge\";\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from \"~/components/ui/tabs\";\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from \"~/components/ui/dialog\";\nimport { ArrowLeft, Calendar, Palette, Image, Video,  Subtitles, Loader2, Camera, Edit2, Save, X, Trash2, RefreshCw } from \"lucide-react\";\nimport type { Creative, Scene } from \"./workflow-main\";\n\n// 定义 VideoItem 类型\ninterface VideoItem {\n  id: number;\n  scene_id: number;\n  prompt?: string;\n  video?: string;\n  update_time: string;\n}\n\n// 定义 Subtitle 类型（更新为使用 video_id）\ninterface Subtitle {\n  id: number;\n  video_id: number;\n  subtitle?: string;\n  audio?: string;\n  update_time: string;\n}\nimport { useVideoAPI } from \"../hooks/use-video-api\";\nimport { <PERSON><PERSON>, AlertDescription } from \"~/components/ui/alert\";\nimport { Input } from \"~/components/ui/input\";\nimport { Textarea } from \"~/components/ui/textarea\";\nimport { GenerationButtons } from \"./generation-buttons\";\nimport { ProgressBar } from \"./progress-bar\";\nimport { CreateSceneDialog } from \"./create-scene-dialog\";\nimport { EditCreativeDialog } from \"./edit-creative-dialog\";\n\ninterface CreativeDetailProps {\n  creative: Creative;\n  onBack: () => void;\n  onCreativeUpdated?: (updatedCreative: Creative) => void;\n}\n\nexport function CreativeDetail({ creative, onBack, onCreativeUpdated }: CreativeDetailProps) {\n  const t = useTranslations(\"workflow.detail\");\n  const [scenes, setScenes] = useState<Scene[]>([]);\n  const [videos, setVideos] = useState<VideoItem[]>([]);\n  const [subtitles, setSubtitles] = useState<Subtitle[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState(\"scenes\"); // 默认显示 scenes\n\n  // 编辑状态\n  const [editingCreative, setEditingCreative] = useState(false);\n  const [editingScenes, setEditingScenes] = useState<Set<number>>(new Set());\n  const [editingSubtitles, setEditingSubtitles] = useState<Set<number>>(new Set());\n  const [editingScenePrompts, setEditingScenePrompts] = useState<Set<number>>(new Set());\n  const [editingVideoPrompts, setEditingVideoPrompts] = useState<Set<number>>(new Set());\n  const [editingSubtitleTexts, setEditingSubtitleTexts] = useState<Set<number>>(new Set());\n\n\n\n  // 重新生成状态\n  const [regeneratingScenes, setRegeneratingScenes] = useState<Set<number>>(new Set());\n  const [regeneratingVideos, setRegeneratingVideos] = useState<Set<number>>(new Set());\n  const [regeneratingAudios, setRegeneratingAudios] = useState<Set<number>>(new Set());\n  const [regenerationTaskIds, setRegenerationTaskIds] = useState<{[key: number]: string}>({});\n\n  // 主角图片生成状态\n  const [generatingCharacterImage, setGeneratingCharacterImage] = useState(false);\n  const [characterImageTaskId, setCharacterImageTaskId] = useState<string | null>(null);\n\n  // 确认对话框状态\n  const [confirmDialog, setConfirmDialog] = useState<{\n    open: boolean;\n    title: string;\n    description: string;\n    onConfirm: () => void;\n  }>({\n    open: false,\n    title: \"\",\n    description: \"\",\n    onConfirm: () => {}\n  });\n\n  // 编辑数据\n  const [editCreativeData, setEditCreativeData] = useState({\n    creative: creative.creative,\n    art_style: creative.art_style || \"\",\n    scene_cnt: creative.scene_cnt || 5,\n    character_prompt: creative.character_prompt || \"\",\n    character_image: creative.character_image || \"\"\n  });\n  const [editScenesData, setEditScenesData] = useState<{[key: number]: {scene: string, prompt: string}}>({});\n  const [editSubtitlesData, setEditSubtitlesData] = useState<{[key: number]: string}>({});\n  const [editScenePromptsData, setEditScenePromptsData] = useState<{[key: number]: string}>({});\n  const [editVideoPromptsData, setEditVideoPromptsData] = useState<{[key: number]: string}>({});\n  const [editSubtitleTextsData, setEditSubtitleTextsData] = useState<{[key: number]: string}>({});\n\n  const { fetchCreativeComplete, updateScene, deleteScene, updateCreative, regenerateSceneImage, updateVideo, regenerateVideo, updateSubtitle, generateCharacterImage, regenerateAudio } = useVideoAPI();\n\n  // 确认对话框辅助函数\n  const showConfirmDialog = (title: string, description: string, onConfirm: () => void) => {\n    setConfirmDialog({\n      open: true,\n      title,\n      description,\n      onConfirm\n    });\n  };\n\n  const closeConfirmDialog = () => {\n    setConfirmDialog(prev => ({ ...prev, open: false }));\n  };\n\n  // 格式化日期\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString(\"zh-CN\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  // 加载相关数据\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // 使用统一接口获取创意的所有相关数据\n      const completeData = await fetchCreativeComplete(creative.id);\n\n      // 设置场景数据\n      setScenes(completeData.scenes);\n\n      // 设置视频数据\n      setVideos(completeData.videos);\n\n      // 设置字幕数据\n      setSubtitles(completeData.subtitles);\n\n    } catch (err) {\n      console.error(\"Failed to load creative details:\", err);\n      setError(\"Failed to load creative details. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadData();\n  }, [creative.id, fetchCreativeComplete]);\n\n  // 删除场景\n  const handleDeleteScene = async (sceneId: number) => {\n    const confirmAction = () => {\n      closeConfirmDialog();\n      performDeleteScene(sceneId);\n    };\n\n    showConfirmDialog(\n      \"删除场景\",\n      \"确定要删除这个场景吗？删除后将无法恢复，同时会删除相关的视频和字幕。\",\n      confirmAction\n    );\n  };\n\n  const performDeleteScene = async (sceneId: number) => {\n    try {\n      setLoading(true);\n      await deleteScene(sceneId);\n\n      // 重新加载数据\n      await loadData();\n\n      // 如果正在编辑这个场景，取消编辑状态\n      setEditingScenes(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(sceneId);\n        return newSet;\n      });\n\n      // 清除编辑数据\n      setEditScenesData(prev => {\n        const newData = { ...prev };\n        delete newData[sceneId];\n        return newData;\n      });\n\n    } catch (err) {\n      console.error(\"Failed to delete scene:\", err);\n      setError(\"删除场景失败，请重试。\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n\n  // 重新生成场景图片\n  const handleRegenerateSceneImage = async (sceneId: number) => {\n    const confirmAction = () => {\n      closeConfirmDialog();\n      performRegenerateSceneImage(sceneId);\n    };\n\n    showConfirmDialog(\n      \"重新生成图片\",\n      \"确定要重新生成这个场景的图片吗？这将替换现有的图片。\",\n      confirmAction\n    );\n  };\n\n  const performRegenerateSceneImage = async (sceneId: number) => {\n    try {\n      setRegeneratingScenes(prev => new Set(prev).add(sceneId));\n\n      const result = await regenerateSceneImage(sceneId);\n\n      if (result.task_id) {\n        setRegenerationTaskIds(prev => ({\n          ...prev,\n          [sceneId]: result.task_id\n        }));\n      } else {\n        // 如果没有任务ID，直接刷新数据\n        await loadData();\n        setRegeneratingScenes(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(sceneId);\n          return newSet;\n        });\n      }\n\n    } catch (err) {\n      console.error(\"Failed to regenerate scene image:\", err);\n      setRegeneratingScenes(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(sceneId);\n        return newSet;\n      });\n    }\n  };\n\n  // 编辑视频 prompt\n  const handleEditVideoPrompt = (videoId: number, currentPrompt: string) => {\n    setEditingVideoPrompts(prev => new Set(prev).add(videoId));\n    setEditVideoPromptsData(prev => ({\n      ...prev,\n      [videoId]: currentPrompt || \"\"\n    }));\n  };\n\n  // 取消编辑视频 prompt\n  const handleCancelEditVideoPrompt = (videoId: number) => {\n    setEditingVideoPrompts(prev => {\n      const newSet = new Set(prev);\n      newSet.delete(videoId);\n      return newSet;\n    });\n    setEditVideoPromptsData(prev => {\n      const newData = { ...prev };\n      delete newData[videoId];\n      return newData;\n    });\n  };\n\n  // 保存视频 prompt\n  const handleSaveVideoPrompt = async (videoId: number) => {\n    const newPrompt = editVideoPromptsData[videoId];\n    if (newPrompt === undefined) return;\n\n    try {\n      setLoading(true);\n      await updateVideo(videoId, { prompt: newPrompt });\n      await loadData();\n      handleCancelEditVideoPrompt(videoId);\n    } catch (err) {\n      console.error(\"Failed to update video prompt:\", err);\n      setError(\"保存视频提示词失败，请重试。\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 重新生成视频\n  const handleRegenerateVideo = async (videoId: number) => {\n    const confirmAction = () => {\n      closeConfirmDialog();\n      performRegenerateVideo(videoId);\n    };\n\n    showConfirmDialog(\n      \"重新生成视频\",\n      \"确定要重新生成这个视频吗？这将替换现有的视频文件。\",\n      confirmAction\n    );\n  };\n\n  const performRegenerateVideo = async (videoId: number) => {\n    try {\n      setRegeneratingVideos(prev => new Set(prev).add(videoId));\n      const result = await regenerateVideo(videoId);\n\n      if (result.task_id) {\n        setRegenerationTaskIds(prev => ({\n          ...prev,\n          [videoId]: result.task_id\n        }));\n      } else {\n        await loadData();\n        setRegeneratingVideos(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(videoId);\n          return newSet;\n        });\n      }\n    } catch (err) {\n      console.error(\"Failed to regenerate video:\", err);\n      setRegeneratingVideos(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(videoId);\n        return newSet;\n      });\n    }\n  };\n\n  // 编辑字幕文本\n  const handleEditSubtitleText = (subtitleId: number, currentText: string) => {\n    setEditingSubtitleTexts(prev => new Set(prev).add(subtitleId));\n    setEditSubtitleTextsData(prev => ({\n      ...prev,\n      [subtitleId]: currentText || \"\"\n    }));\n  };\n\n  // 取消编辑字幕文本\n  const handleCancelEditSubtitleText = (subtitleId: number) => {\n    setEditingSubtitleTexts(prev => {\n      const newSet = new Set(prev);\n      newSet.delete(subtitleId);\n      return newSet;\n    });\n    setEditSubtitleTextsData(prev => {\n      const newData = { ...prev };\n      delete newData[subtitleId];\n      return newData;\n    });\n  };\n\n  // 保存字幕文本\n  const handleSaveSubtitleText = async (subtitleId: number) => {\n    const newText = editSubtitleTextsData[subtitleId];\n    if (newText === undefined) return;\n\n    try {\n      setLoading(true);\n      await updateSubtitle(subtitleId, { subtitle: newText });\n      await loadData();\n      handleCancelEditSubtitleText(subtitleId);\n    } catch (err) {\n      console.error(\"Failed to update subtitle:\", err);\n      setError(\"保存字幕失败，请重试。\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 重新生成音频\n  const handleRegenerateAudio = async (subtitleId: number) => {\n    const confirmAction = () => {\n      closeConfirmDialog();\n      performRegenerateAudio(subtitleId);\n    };\n\n    showConfirmDialog(\n      \"重新生成音频\",\n      \"确定要重新生成这个字幕的音频吗？这将替换现有的音频文件。\",\n      confirmAction\n    );\n  };\n\n  const performRegenerateAudio = async (subtitleId: number) => {\n    try {\n      setRegeneratingAudios(prev => new Set(prev).add(subtitleId));\n      const result = await regenerateAudio(subtitleId);\n\n      if (result.task_id) {\n        setRegenerationTaskIds(prev => ({\n          ...prev,\n          [subtitleId]: result.task_id\n        }));\n      } else {\n        await loadData();\n        setRegeneratingAudios(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(subtitleId);\n          return newSet;\n        });\n      }\n    } catch (err) {\n      console.error(\"Failed to regenerate audio:\", err);\n      setRegeneratingAudios(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(subtitleId);\n        return newSet;\n      });\n    }\n  };\n\n  // 生成主角图片\n  const handleGenerateCharacterImage = async () => {\n    const confirmAction = () => {\n      closeConfirmDialog();\n      performGenerateCharacterImage();\n    };\n\n    showConfirmDialog(\n      \"生成主角图片\",\n      \"确定要生成主角图片吗？这将根据主角提示词生成新的图片。\",\n      confirmAction\n    );\n  };\n\n  const performGenerateCharacterImage = async () => {\n    try {\n      setGeneratingCharacterImage(true);\n      const result = await generateCharacterImage(creative.id);\n\n      if (result.task_id) {\n        setCharacterImageTaskId(result.task_id);\n      } else {\n        await loadData();\n        setGeneratingCharacterImage(false);\n      }\n    } catch (err) {\n      console.error(\"Failed to generate character image:\", err);\n      setGeneratingCharacterImage(false);\n    }\n  };\n\n  // 保存场景更改\n  const handleSaveScene = async (sceneId: number) => {\n    const sceneData = editScenesData[sceneId];\n    if (!sceneData) return;\n\n    try {\n      setLoading(true);\n      await updateScene(sceneId, {\n        scene: sceneData.scene,\n        prompt: sceneData.prompt\n      });\n\n      // 重新加载数据\n      await loadData();\n\n      // 退出编辑模式\n      setEditingScenes(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(sceneId);\n        return newSet;\n      });\n\n    } catch (err) {\n      console.error(\"Failed to update scene:\", err);\n      setError(\"保存场景失败，请重试。\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 开始编辑场景 prompt\n  const handleEditScenePrompt = (sceneId: number, currentPrompt: string) => {\n    setEditingScenePrompts(prev => new Set(prev).add(sceneId));\n    setEditScenePromptsData(prev => ({\n      ...prev,\n      [sceneId]: currentPrompt\n    }));\n  };\n\n  // 取消编辑场景 prompt\n  const handleCancelEditScenePrompt = (sceneId: number) => {\n    setEditingScenePrompts(prev => {\n      const newSet = new Set(prev);\n      newSet.delete(sceneId);\n      return newSet;\n    });\n    setEditScenePromptsData(prev => {\n      const newData = { ...prev };\n      delete newData[sceneId];\n      return newData;\n    });\n  };\n\n  // 保存场景 prompt 更改\n  const handleSaveScenePrompt = async (sceneId: number) => {\n    const newPrompt = editScenePromptsData[sceneId];\n    if (!newPrompt) return;\n\n    const scene = scenes.find(s => s.id === sceneId);\n    if (!scene) return;\n\n    try {\n      setLoading(true);\n      await updateScene(sceneId, {\n        scene: scene.scene,\n        prompt: newPrompt\n      });\n\n      // 重新加载数据\n      await loadData();\n\n      // 退出编辑模式\n      handleCancelEditScenePrompt(sceneId);\n\n    } catch (err) {\n      console.error(\"Failed to update scene prompt:\", err);\n      setError(\"保存场景提示词失败，请重试。\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 保存创意更改\n  const handleSaveCreative = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const updateData = {\n        creative: editCreativeData.creative.trim(),\n        art_style: editCreativeData.art_style.trim() || undefined,\n      };\n\n      const updatedCreative = await updateCreative(creative.id, updateData);\n\n      // 更新成功后退出编辑模式\n      setEditingCreative(false);\n\n      // 通知父组件创意已更新\n      if (onCreativeUpdated) {\n        onCreativeUpdated(updatedCreative);\n      }\n\n      // 刷新数据以确保数据一致性\n      await loadData();\n\n    } catch (err) {\n      console.error(\"Failed to update creative:\", err);\n      setError(\"Failed to update creative. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex h-full w-full items-center justify-center\">\n        <div className=\"flex items-center gap-2\">\n          <Loader2 className=\"h-6 w-6 animate-spin\" />\n          <span className=\"text-lg\">Loading creative details...</span>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"flex h-full w-full items-center justify-center p-6\">\n        <Alert className=\"max-w-md\">\n          <AlertDescription>{error}</AlertDescription>\n        </Alert>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* 头部 */}\n      <div className=\"flex items-center justify-between p-6 border-b bg-muted/30 flex-shrink-0\">\n        <div className=\"flex items-center gap-4\">\n          <Button variant=\"ghost\" size=\"sm\" onClick={onBack}>\n            <ArrowLeft className=\"h-4 w-4\" />\n            Back to List\n          </Button>\n          <CreateSceneDialog\n            creativeId={creative.id}\n            onSceneCreated={loadData}\n          />\n          <div>\n            <h1 className=\"text-2xl font-bold line-clamp-2\">{creative.creative}</h1>\n            <div className=\"flex items-center gap-4 mt-2\">\n              <Badge variant=\"outline\">ID: {creative.id}</Badge>\n              {creative.art_style && (\n                <Badge variant=\"secondary\">\n                  <Palette className=\"h-3 w-3 mr-1\" />\n                  {creative.art_style}\n                </Badge>\n              )}\n              <div className=\"flex items-center gap-1 text-sm text-muted-foreground\">\n                <Calendar className=\"h-3 w-3\" />\n                {formatDate(creative.update_time)}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div className=\"flex-1 overflow-auto p-6\">\n        {/* 创意详细信息卡片 */}\n        <Card className=\"mb-6\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Palette className=\"h-5 w-5\" />\n              创意详细信息\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {/* 场景数量 */}\n              <div>\n                <label className=\"text-sm font-medium text-muted-foreground\">场景数量</label>\n                <div className=\"mt-1\">\n                  <Badge variant=\"outline\" className=\"text-sm\">\n                    {creative.scene_cnt || 5} 个场景\n                  </Badge>\n                </div>\n              </div>\n\n              {/* 主角提示词 */}\n              <div className=\"md:col-span-2\">\n                <label className=\"text-sm font-medium text-muted-foreground\">主角提示词</label>\n                <div className=\"mt-1\">\n                  {creative.character_prompt ? (\n                    <p className=\"text-sm bg-muted/50 p-2 rounded border\">\n                      {creative.character_prompt}\n                    </p>\n                  ) : (\n                    <p className=\"text-sm text-muted-foreground italic\">\n                      暂无主角提示词\n                    </p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            {/* 主角图片 */}\n            <div>\n              <label className=\"text-sm font-medium text-muted-foreground\">主角图片</label>\n              <div className=\"mt-2 flex items-center gap-4\">\n                {creative.character_image ? (\n                  <div className=\"relative\">\n                    <img\n                      src={creative.character_image}\n                      alt=\"主角图片\"\n                      className=\"w-24 h-24 object-cover rounded-lg border\"\n                    />\n                  </div>\n                ) : (\n                  <div className=\"w-24 h-24 bg-muted/50 rounded-lg border border-dashed flex items-center justify-center\">\n                    <Camera className=\"h-8 w-8 text-muted-foreground\" />\n                  </div>\n                )}\n\n                <div className=\"flex flex-col gap-2\">\n                  {creative.character_prompt && (\n                    <Button\n                      size=\"sm\"\n                      onClick={handleGenerateCharacterImage}\n                      disabled={generatingCharacterImage}\n                    >\n                      {generatingCharacterImage ? (\n                        <>\n                          <Loader2 className=\"h-3 w-3 mr-1 animate-spin\" />\n                          生成中...\n                        </>\n                      ) : (\n                        <>\n                          <Camera className=\"h-3 w-3 mr-1\" />\n                          {creative.character_image ? '重新生成' : '生成主角图片'}\n                        </>\n                      )}\n                    </Button>\n                  )}\n\n                  {!creative.character_prompt && (\n                    <p className=\"text-xs text-muted-foreground\">\n                      请先设置主角提示词\n                    </p>\n                  )}\n                </div>\n              </div>\n\n              {/* 主角图片生成进度条 */}\n              {characterImageTaskId && (\n                <div className=\"mt-3\">\n                  <ProgressBar\n                    taskId={characterImageTaskId}\n                    onComplete={() => {\n                      setGeneratingCharacterImage(false);\n                      setCharacterImageTaskId(null);\n                      loadData(); // 刷新数据\n                    }}\n                    onError={() => {\n                      setGeneratingCharacterImage(false);\n                      setCharacterImageTaskId(null);\n                    }}\n                  />\n                </div>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n\n        <Tabs value={activeTab} onValueChange={setActiveTab}>\n          <TabsList className=\"grid w-full grid-cols-4\">\n            <TabsTrigger value=\"scenes\">\n              {t(\"scenes\")} ({scenes.length})\n            </TabsTrigger>\n            <TabsTrigger value=\"images\">\n              {t(\"images\")} ({scenes.filter(s => s.image).length})\n            </TabsTrigger>\n            <TabsTrigger value=\"videos\">\n              {t(\"videos\")} ({videos.length})\n            </TabsTrigger>\n            <TabsTrigger value=\"subtitles\">\n              {t(\"subtitles\")} ({subtitles.length})\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"scenes\" className=\"mt-6\">\n            <div className=\"space-y-4\">\n              {scenes.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <Camera className=\"h-16 w-16 text-muted-foreground mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-semibold mb-2\">No scenes yet</h3>\n                  <p className=\"text-muted-foreground\">This creative doesn't have any scenes yet.</p>\n                </div>\n              ) : (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                  {scenes.map((scene) => {\n                    // 获取该场景相关的视频\n                    const relatedVideos = videos.filter(video => video.scene_id === scene.id);\n                    // 获取该场景相关的字幕（通过视频关联）\n                    const relatedSubtitles = subtitles.filter(s =>\n                      relatedVideos.some(video => video.id === s.video_id)\n                    );\n                    const isEditing = editingScenes.has(scene.id);\n\n                    return (\n                      <Card key={scene.id}>\n                        <CardHeader className=\"pb-3\">\n                          <div className=\"flex items-start justify-between\">\n                            <CardTitle className=\"text-base\">\n                              Scene #{scene.id}\n                            </CardTitle>\n                            <div className=\"flex items-center gap-2\">\n                              <Badge variant=\"outline\" className=\"text-xs\">\n                                {formatDate(scene.update_time)}\n                              </Badge>\n                              <Button\n                                variant=\"ghost\"\n                                size=\"sm\"\n                                onClick={() => {\n                                  if (isEditing) {\n                                    setEditingScenes(prev => {\n                                      const newSet = new Set(prev);\n                                      newSet.delete(scene.id);\n                                      return newSet;\n                                    });\n                                  } else {\n                                    setEditingScenes(prev => new Set(prev).add(scene.id));\n                                    setEditScenesData(prev => ({\n                                      ...prev,\n                                      [scene.id]: {\n                                        scene: scene.scene,\n                                        prompt: scene.prompt\n                                      }\n                                    }));\n                                  }\n                                }}\n                              >\n                                {isEditing ? <X className=\"h-3 w-3\" /> : <Edit2 className=\"h-3 w-3\" />}\n                              </Button>\n\n                              <Button\n                                variant=\"ghost\"\n                                size=\"sm\"\n                                onClick={() => handleDeleteScene(scene.id)}\n                                className=\"text-destructive hover:text-destructive\"\n                              >\n                                <Trash2 className=\"h-3 w-3\" />\n                              </Button>\n                            </div>\n                          </div>\n                        </CardHeader>\n                        <CardContent className=\"space-y-3\">\n                          {/* 场景描述 */}\n                          <div>\n                            <label className=\"text-xs font-medium text-muted-foreground\">Scene Title</label>\n                            {isEditing ? (\n                              <Input\n                                value={editScenesData[scene.id]?.scene || scene.scene}\n                                onChange={(e) => setEditScenesData(prev => ({\n                                  ...prev,\n                                  [scene.id]: {\n                                    scene: e.target.value,\n                                    prompt: prev[scene.id]?.prompt || scene.prompt\n                                  }\n                                }))}\n                                className=\"mt-1 text-sm\"\n                              />\n                            ) : (\n                              <p className=\"mt-1 text-sm font-medium\">{scene.scene}</p>\n                            )}\n                          </div>\n\n                          {/* 提示词 */}\n                          <div>\n                            <label className=\"text-xs font-medium text-muted-foreground\">Prompt</label>\n                            {isEditing ? (\n                              <Textarea\n                                value={editScenesData[scene.id]?.prompt || scene.prompt}\n                                onChange={(e) => setEditScenesData(prev => ({\n                                  ...prev,\n                                  [scene.id]: {\n                                    scene: prev[scene.id]?.scene || scene.scene,\n                                    prompt: e.target.value\n                                  }\n                                }))}\n                                className=\"mt-1 text-sm\"\n                                rows={3}\n                              />\n                            ) : (\n                              <p className=\"mt-1 text-sm text-muted-foreground\">{scene.prompt}</p>\n                            )}\n                          </div>\n\n                          {/* 关联的图片、视频、字幕数量 */}\n                          <div className=\"flex items-center gap-4 text-xs text-muted-foreground\">\n                            <div className=\"flex items-center gap-1\">\n                              <Image className=\"h-3 w-3\" />\n                              <span>{scene.image ? 1 : 0} image</span>\n                            </div>\n                            <div className=\"flex items-center gap-1\">\n                              <Video className=\"h-3 w-3\" />\n                              <span>{relatedVideos.length} videos</span>\n                            </div>\n                            <div className=\"flex items-center gap-1\">\n                              <Subtitles className=\"h-3 w-3\" />\n                              <span>{relatedSubtitles.length} subtitles</span>\n                            </div>\n                          </div>\n\n                          {/* 编辑模式下的保存和取消按钮 */}\n                          {isEditing && (\n                            <div className=\"flex gap-2 pt-2\">\n                              <Button\n                                size=\"sm\"\n                                onClick={() => handleSaveScene(scene.id)}\n                                disabled={loading}\n                              >\n                                <Save className=\"h-4 w-4 mr-2\" />\n                                Save\n                              </Button>\n                              <Button\n                                variant=\"outline\"\n                                size=\"sm\"\n                                onClick={() => {\n                                  setEditingScenes(prev => {\n                                    const newSet = new Set(prev);\n                                    newSet.delete(scene.id);\n                                    return newSet;\n                                  });\n                                }}\n                                disabled={loading}\n                              >\n                                Cancel\n                              </Button>\n                            </div>\n                          )}\n                        </CardContent>\n                      </Card>\n                    );\n                  })}\n                </div>\n              )}\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"images\" className=\"mt-6\">\n            <div className=\"space-y-4\">\n              {scenes.filter(scene => scene.image).length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <Image className=\"h-16 w-16 text-muted-foreground mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-semibold mb-2\">No images yet</h3>\n                  <p className=\"text-muted-foreground\">This creative doesn't have any images yet.</p>\n                </div>\n              ) : (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n                  {scenes.filter(scene => scene.image).map((scene) => {\n\n                    return (\n                      <Card key={scene.id} className=\"overflow-hidden\">\n                        <div className=\"aspect-video bg-muted/50 relative\">\n                          <img\n                            src={scene.image}\n                            alt={`Scene ${scene.id} Image`}\n                            className=\"w-full h-full object-cover\"\n                            onError={(e) => {\n                              const target = e.target as HTMLImageElement;\n                              target.style.display = 'none';\n                              const parent = target.parentElement;\n                              if (parent) {\n                                parent.innerHTML = `\n                                  <div class=\"flex items-center justify-center h-full\">\n                                    <div class=\"text-center\">\n                                      <div class=\"w-12 h-12 mx-auto mb-2 bg-muted rounded-lg flex items-center justify-center\">\n                                        <svg class=\"w-6 h-6 text-muted-foreground\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\n                                        </svg>\n                                      </div>\n                                      <p class=\"text-xs text-muted-foreground\">Image not available</p>\n                                    </div>\n                                  </div>\n                                `;\n                              }\n                            }}\n                          />\n                          <div className=\"absolute top-2 right-2\">\n                            <Badge variant=\"secondary\" className=\"text-xs\">\n                              Scene #{scene.id}\n                            </Badge>\n                          </div>\n                        </div>\n\n                        <CardContent className=\"p-4\">\n                          <div className=\"space-y-2\">\n                            <div>\n                              <p className=\"text-sm font-medium line-clamp-2\">\n                                {scene.scene}\n                              </p>\n\n                              {/* Prompt 编辑区域 */}\n                              {editingScenePrompts.has(scene.id) ? (\n                                <div className=\"mt-2 space-y-2\">\n                                  <Textarea\n                                    value={editScenePromptsData[scene.id] || scene.prompt}\n                                    onChange={(e) => setEditScenePromptsData(prev => ({\n                                      ...prev,\n                                      [scene.id]: e.target.value\n                                    }))}\n                                    placeholder=\"输入图片生成提示词...\"\n                                    className=\"text-xs min-h-[60px]\"\n                                  />\n                                  <div className=\"flex gap-1\">\n                                    <Button\n                                      size=\"sm\"\n                                      onClick={() => handleSaveScenePrompt(scene.id)}\n                                      className=\"h-6 px-2 text-xs\"\n                                    >\n                                      <Save className=\"h-3 w-3 mr-1\" />\n                                      保存\n                                    </Button>\n                                    <Button\n                                      variant=\"ghost\"\n                                      size=\"sm\"\n                                      onClick={() => handleCancelEditScenePrompt(scene.id)}\n                                      className=\"h-6 px-2 text-xs\"\n                                    >\n                                      <X className=\"h-3 w-3 mr-1\" />\n                                      取消\n                                    </Button>\n                                  </div>\n                                </div>\n                              ) : (\n                                <div className=\"mt-1\">\n                                  <p className=\"text-xs text-muted-foreground line-clamp-2\">\n                                    {scene.prompt}\n                                  </p>\n                                  <div className=\"flex gap-1 mt-2\">\n                                    <Button\n                                      variant=\"outline\"\n                                      size=\"sm\"\n                                      onClick={() => handleEditScenePrompt(scene.id, scene.prompt)}\n                                      className=\"h-6 px-2 text-xs\"\n                                    >\n                                      <Edit2 className=\"h-3 w-3 mr-1\" />\n                                      编辑提示词\n                                    </Button>\n\n                                    <Button\n                                      variant=\"outline\"\n                                      size=\"sm\"\n                                      onClick={() => handleRegenerateSceneImage(scene.id)}\n                                      disabled={regeneratingScenes.has(scene.id)}\n                                      className=\"h-6 px-2 text-xs\"\n                                    >\n                                      {regeneratingScenes.has(scene.id) ? (\n                                        <Loader2 className=\"h-3 w-3 mr-1 animate-spin\" />\n                                      ) : (\n                                        <RefreshCw className=\"h-3 w-3 mr-1\" />\n                                      )}\n                                      重新生成\n                                    </Button>\n                                  </div>\n                                </div>\n                              )}\n                            </div>\n\n                            {/* 重新生成图片进度条 */}\n                            {regeneratingScenes.has(scene.id) && regenerationTaskIds[scene.id] && (\n                              <div className=\"mt-3\">\n                                <ProgressBar\n                                  taskId={regenerationTaskIds[scene.id] || null}\n                                  onComplete={() => {\n                                    setRegeneratingScenes(prev => {\n                                      const newSet = new Set(prev);\n                                      newSet.delete(scene.id);\n                                      return newSet;\n                                    });\n                                    setRegenerationTaskIds(prev => {\n                                      const newIds = { ...prev };\n                                      delete newIds[scene.id];\n                                      return newIds;\n                                    });\n                                    loadData(); // 刷新数据\n                                  }}\n                                  onError={() => {\n                                    setRegeneratingScenes(prev => {\n                                      const newSet = new Set(prev);\n                                      newSet.delete(scene.id);\n                                      return newSet;\n                                    });\n                                    setRegenerationTaskIds(prev => {\n                                      const newIds = { ...prev };\n                                      delete newIds[scene.id];\n                                      return newIds;\n                                    });\n                                  }}\n                                />\n                              </div>\n                            )}\n\n                            <div className=\"flex items-center justify-between text-xs text-muted-foreground\">\n                              <span className=\"flex items-center gap-1\">\n                                <Calendar className=\"h-3 w-3\" />\n                                {formatDate(scene.update_time)}\n                              </span>\n                              {videos.some(v => v.scene_id === scene.id) && (\n                                <Badge variant=\"outline\" className=\"text-xs\">\n                                  Has Video\n                                </Badge>\n                              )}\n                            </div>\n\n                            {/* 图片路径显示 */}\n                            <div className=\"pt-2 border-t\">\n                              <p className=\"text-xs text-muted-foreground font-mono break-all\">\n                                {scene.image}\n                              </p>\n                            </div>\n                          </div>\n                        </CardContent>\n                      </Card>\n                    );\n                  })}\n                </div>\n              )}\n            </div>\n          </TabsContent>\n          \n          <TabsContent value=\"videos\" className=\"mt-6\">\n            <div className=\"space-y-4\">\n              {videos.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <Video className=\"h-16 w-16 text-muted-foreground mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-semibold mb-2\">No videos yet</h3>\n                  <p className=\"text-muted-foreground\">This creative doesn't have any videos yet.</p>\n                </div>\n              ) : (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                  {videos.map((video) => {\n                    const relatedScene = scenes.find(s => s.id === video.scene_id);\n\n                    return (\n                      <Card key={video.id} className=\"overflow-hidden\">\n                        <div className=\"aspect-video bg-muted/50 relative\">\n                          <video\n                            src={video.video}\n                            className=\"w-full h-full object-cover\"\n                            controls\n                            onError={(e) => {\n                              const target = e.target as HTMLVideoElement;\n                              target.style.display = 'none';\n                              const parent = target.parentElement;\n                              if (parent) {\n                                parent.innerHTML = `\n                                  <div class=\"flex items-center justify-center h-full\">\n                                    <div class=\"text-center\">\n                                      <div class=\"w-12 h-12 mx-auto mb-2 bg-muted rounded-lg flex items-center justify-center\">\n                                        <svg class=\"w-6 h-6 text-muted-foreground\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"></path>\n                                        </svg>\n                                      </div>\n                                      <p class=\"text-xs text-muted-foreground\">Video not available</p>\n                                    </div>\n                                  </div>\n                                `;\n                              }\n                            }}\n                          />\n                          <div className=\"absolute top-2 right-2\">\n                            <Badge variant=\"secondary\" className=\"text-xs\">\n                              Video #{video.id}\n                            </Badge>\n                          </div>\n                        </div>\n\n                        <CardContent className=\"p-4\">\n                          <div className=\"space-y-2\">\n                            <div>\n                              <p className=\"text-sm font-medium line-clamp-2\">\n                                {relatedScene ? relatedScene.scene : `Video #${video.id}`}\n                              </p>\n                              \n                              {/* 视频提示词编辑区域 */}\n                              {editingVideoPrompts.has(video.id) ? (\n                                <div className=\"mt-2 space-y-2\">\n                                  <Textarea\n                                    value={editVideoPromptsData[video.id] || video.prompt || \"\"}\n                                    onChange={(e) => setEditVideoPromptsData(prev => ({\n                                      ...prev,\n                                      [video.id]: e.target.value\n                                    }))}\n                                    placeholder=\"输入视频生成提示词...\"\n                                    className=\"text-xs min-h-[60px]\"\n                                  />\n                                  <div className=\"flex gap-1\">\n                                    <Button\n                                      size=\"sm\"\n                                      onClick={() => handleSaveVideoPrompt(video.id)}\n                                      className=\"h-6 px-2 text-xs\"\n                                    >\n                                      <Save className=\"h-3 w-3 mr-1\" />\n                                      保存\n                                    </Button>\n                                    <Button\n                                      variant=\"ghost\"\n                                      size=\"sm\"\n                                      onClick={() => handleCancelEditVideoPrompt(video.id)}\n                                      className=\"h-6 px-2 text-xs\"\n                                    >\n                                      <X className=\"h-3 w-3 mr-1\" />\n                                      取消\n                                    </Button>\n                                  </div>\n                                </div>\n                              ) : (\n                                <div className=\"mt-1\">\n                                  {video.prompt && (\n                                    <div>\n                                      <p className=\"text-xs text-muted-foreground font-medium\">图生视频提示词:</p>\n                                      <p className=\"text-xs text-muted-foreground line-clamp-3 mt-1\">\n                                        {video.prompt}\n                                      </p>\n                                    </div>\n                                  )}\n                                  <div className=\"flex gap-1 mt-2\">\n                                    <Button\n                                      variant=\"outline\"\n                                      size=\"sm\"\n                                      onClick={() => handleEditVideoPrompt(video.id, video.prompt || \"\")}\n                                      className=\"h-6 px-2 text-xs\"\n                                    >\n                                      <Edit2 className=\"h-3 w-3 mr-1\" />\n                                      编辑提示词\n                                    </Button>\n                                    <Button\n                                      variant=\"outline\"\n                                      size=\"sm\"\n                                      onClick={() => handleRegenerateVideo(video.id)}\n                                      disabled={regeneratingVideos.has(video.id)}\n                                      className=\"h-6 px-2 text-xs\"\n                                    >\n                                      {regeneratingVideos.has(video.id) ? (\n                                        <Loader2 className=\"h-3 w-3 mr-1 animate-spin\" />\n                                      ) : (\n                                        <RefreshCw className=\"h-3 w-3 mr-1\" />\n                                      )}\n                                      重新生成\n                                    </Button>\n                                  </div>\n                                </div>\n                              )}\n                            </div>\n\n                            {/* 重新生成进度条 */}\n                            {regeneratingVideos.has(video.id) && regenerationTaskIds[video.id] && (\n                              <div className=\"mb-2\">\n                                <ProgressBar\n                                  taskId={regenerationTaskIds[video.id] || null}\n                                  onComplete={() => {\n                                    setRegeneratingVideos(prev => {\n                                      const newSet = new Set(prev);\n                                      newSet.delete(video.id);\n                                      return newSet;\n                                    });\n                                    setRegenerationTaskIds(prev => {\n                                      const newIds = { ...prev };\n                                      delete newIds[video.id];\n                                      return newIds;\n                                    });\n                                    loadData(); // 刷新数据\n                                  }}\n                                  onError={() => {\n                                    setRegeneratingVideos(prev => {\n                                      const newSet = new Set(prev);\n                                      newSet.delete(video.id);\n                                      return newSet;\n                                    });\n                                    setRegenerationTaskIds(prev => {\n                                      const newIds = { ...prev };\n                                      delete newIds[video.id];\n                                      return newIds;\n                                    });\n                                  }}\n                                />\n                              </div>\n                            )}\n\n                            <div className=\"flex items-center justify-between text-xs text-muted-foreground\">\n                              <span className=\"flex items-center gap-1\">\n                                <Calendar className=\"h-3 w-3\" />\n                                {formatDate(video.update_time)}\n                              </span>\n                              {relatedScene?.image && (\n                                <Badge variant=\"outline\" className=\"text-xs\">\n                                  Has Image\n                                </Badge>\n                              )}\n                            </div>\n\n                            {/* 视频路径显示 */}\n                            <div className=\"pt-2 border-t\">\n                              <p className=\"text-xs text-muted-foreground font-mono break-all\">\n                                {video.video}\n                              </p>\n                            </div>\n                          </div>\n                        </CardContent>\n                      </Card>\n                    );\n                  })}\n                </div>\n              )}\n            </div>\n          </TabsContent>      \n\n          <TabsContent value=\"subtitles\" className=\"mt-6\">\n            <div className=\"space-y-4\">\n              {subtitles.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <Subtitles className=\"h-16 w-16 text-muted-foreground mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-semibold mb-2\">No subtitles yet</h3>\n                  <p className=\"text-muted-foreground\">This creative doesn't have any subtitles yet.</p>\n                </div>\n              ) : (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                  {subtitles.map((subtitle) => {\n                    const relatedVideo = videos.find(video => video.id === subtitle.video_id);\n                    const relatedScene = relatedVideo ? scenes.find(s => s.id === relatedVideo.scene_id) : null;\n                    const isEditing = editingSubtitleTexts.has(subtitle.id);\n\n                    return (\n                      <Card key={subtitle.id} className=\"overflow-hidden\">\n                        {/* 字幕预览区域 */}\n                        <div className=\"aspect-video bg-muted/50 relative flex items-center justify-center p-4\">\n                          <div className=\"text-center max-w-full\">\n                            {isEditing ? (\n                              <Textarea\n                                value={editSubtitleTextsData[subtitle.id] || subtitle.subtitle || \"\"}\n                                onChange={(e) => setEditSubtitleTextsData(prev => ({\n                                  ...prev,\n                                  [subtitle.id]: e.target.value\n                                }))}\n                                className=\"w-full bg-white/90 text-center resize-none\"\n                                rows={4}\n                                placeholder=\"输入字幕文本...\"\n                              />\n                            ) : (\n                              <p className=\"text-sm font-medium text-center leading-relaxed\">\n                                {subtitle.subtitle || \"暂无字幕文本\"}\n                              </p>\n                            )}\n                          </div>\n\n                          {/* 右上角标识 */}\n                          <div className=\"absolute top-2 right-2\">\n                            <Badge variant=\"secondary\" className=\"text-xs\">\n                              Subtitle #{subtitle.id}\n                            </Badge>\n                          </div>\n                        </div>\n\n                        <CardContent className=\"p-4\">\n                          <div className=\"space-y-3\">\n                            {/* 场景和视频信息 */}\n                            <div className=\"flex items-center justify-between text-xs text-muted-foreground\">\n                              <div className=\"flex items-center gap-2\">\n                                {relatedScene && (\n                                  <span>From: {relatedScene.scene}</span>\n                                )}\n                                <Badge variant=\"outline\" className=\"text-xs\">\n                                  Video #{subtitle.video_id}\n                                </Badge>\n                              </div>\n                              <span className=\"flex items-center gap-1\">\n                                <Calendar className=\"h-3 w-3\" />\n                                {formatDate(subtitle.update_time)}\n                              </span>\n                            </div>\n\n                            {/* 音频播放器 */}\n                            {subtitle.audio && (\n                              <div>\n                                <audio\n                                  src={subtitle.audio}\n                                  controls\n                                  className=\"w-full h-8\"\n                                  onError={(e) => {\n                                    const target = e.target as HTMLAudioElement;\n                                    target.style.display = 'none';\n                                    const parent = target.parentElement;\n                                    if (parent) {\n                                      parent.innerHTML = `\n                                        <div class=\"flex items-center justify-center p-2 bg-muted/50 rounded text-xs text-muted-foreground\">\n                                          Audio not available\n                                        </div>\n                                      `;\n                                    }\n                                  }}\n                                />\n                              </div>\n                            )}\n\n                            {/* 操作按钮 */}\n                            <div className=\"flex gap-2 pt-2\">\n                            {isEditing ? (\n                              <>\n                                <Button\n                                  size=\"sm\"\n                                  onClick={() => handleSaveSubtitleText(subtitle.id)}\n                                >\n                                  <Save className=\"h-4 w-4 mr-2\" />\n                                  保存\n                                </Button>\n                                <Button\n                                  variant=\"outline\"\n                                  size=\"sm\"\n                                  onClick={() => handleCancelEditSubtitleText(subtitle.id)}\n                                >\n                                  <X className=\"h-4 w-4 mr-2\" />\n                                  取消\n                                </Button>\n                              </>\n                            ) : (\n                              <>\n                                <Button\n                                  variant=\"outline\"\n                                  size=\"sm\"\n                                  onClick={() => handleEditSubtitleText(subtitle.id, subtitle.subtitle || \"\")}\n                                >\n                                  <Edit2 className=\"h-4 w-4 mr-2\" />\n                                  编辑字幕\n                                </Button>\n                                <Button\n                                  variant=\"outline\"\n                                  size=\"sm\"\n                                  onClick={() => handleRegenerateAudio(subtitle.id)}\n                                  disabled={regeneratingAudios.has(subtitle.id)}\n                                >\n                                  {regeneratingAudios.has(subtitle.id) ? (\n                                    <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                                  ) : (\n                                    <RefreshCw className=\"h-4 w-4 mr-2\" />\n                                  )}\n                                  重新生成音频\n                                </Button>\n                              </>\n                            )}\n                          </div>\n\n                          {/* 重新生成音频进度条 */}\n                          {regeneratingAudios.has(subtitle.id) && regenerationTaskIds[subtitle.id] && (\n                            <div className=\"mt-4\">\n                              <ProgressBar\n                                taskId={regenerationTaskIds[subtitle.id] || null}\n                                onComplete={() => {\n                                  setRegeneratingAudios(prev => {\n                                    const newSet = new Set(prev);\n                                    newSet.delete(subtitle.id);\n                                    return newSet;\n                                  });\n                                  setRegenerationTaskIds(prev => {\n                                    const newData = { ...prev };\n                                    delete newData[subtitle.id];\n                                    return newData;\n                                  });\n                                  loadData();\n                                }}\n                                onError={(error) => {\n                                  setRegeneratingAudios(prev => {\n                                    const newSet = new Set(prev);\n                                    newSet.delete(subtitle.id);\n                                    return newSet;\n                                  });\n                                  setError(`音频生成失败: ${error}`);\n                                }}\n                              />\n                            </div>\n                          )}\n                          </div>\n                        </CardContent>\n                      </Card>\n                    );\n                  })}\n                </div>\n              )}\n            </div>\n          </TabsContent>\n        </Tabs>\n      </div>  \n      {/* 内容区域 */}\n      {/* 生成工具 - 在所有标签页下方 */}\n      <div className=\"mt-6 mb-8\">\n        <GenerationButtons\n          creativeId={creative.id}\n          onRefresh={loadData}\n        />\n      </div>\n\n      {/* 确认对话框 */}\n      <Dialog open={confirmDialog.open} onOpenChange={closeConfirmDialog}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>{confirmDialog.title}</DialogTitle>\n            <DialogDescription>{confirmDialog.description}</DialogDescription>\n          </DialogHeader>\n          <DialogFooter>\n            <Button variant=\"outline\" onClick={closeConfirmDialog}>\n              取消\n            </Button>\n            <Button variant=\"destructive\" onClick={confirmDialog.onConfirm}>\n              确认\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAnCA;;;;;;;;;;;;;;;;AA4CO,SAAS,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,iBAAiB,EAAuB;;IACzF,MAAM,IAAI,CAAA,GAAA,wTAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,cAAc;IAEpE,OAAO;IACP,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACpE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAC1E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAChF,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAChF,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAIlF,SAAS;IACT,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAC9E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAC9E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAEzF,WAAW;IACX,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACzE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhF,UAAU;IACV,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAK9C;QACD,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;uCAAE,KAAO;;IACpB;IAEA,OAAO;IACP,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,UAAU,SAAS,QAAQ;QAC3B,WAAW,SAAS,SAAS,IAAI;QACjC,WAAW,SAAS,SAAS,IAAI;QACjC,kBAAkB,SAAS,gBAAgB,IAAI;QAC/C,iBAAiB,SAAS,eAAe,IAAI;IAC/C;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAoD,CAAC;IACxG,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACrF,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAC3F,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAC3F,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAE7F,MAAM,EAAE,qBAAqB,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,oBAAoB,EAAE,WAAW,EAAE,eAAe,EAAE,cAAc,EAAE,sBAAsB,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAEnM,YAAY;IACZ,MAAM,oBAAoB,CAAC,OAAe,aAAqB;QAC7D,iBAAiB;YACf,MAAM;YACN;YACA;YACA;QACF;IACF;IAEA,MAAM,qBAAqB;QACzB,iBAAiB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAM,CAAC;IACpD;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,SAAS;IACT,MAAM,WAAW;QACf,IAAI;YACF,WAAW;YACX,SAAS;YAET,oBAAoB;YACpB,MAAM,eAAe,MAAM,sBAAsB,SAAS,EAAE;YAE5D,SAAS;YACT,UAAU,aAAa,MAAM;YAE7B,SAAS;YACT,UAAU,aAAa,MAAM;YAE7B,SAAS;YACT,aAAa,aAAa,SAAS;QAErC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG;QAAC,SAAS,EAAE;QAAE;KAAsB;IAEvC,OAAO;IACP,MAAM,oBAAoB,OAAO;QAC/B,MAAM,gBAAgB;YACpB;YACA,mBAAmB;QACrB;QAEA,kBACE,QACA,sCACA;IAEJ;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,WAAW;YACX,MAAM,YAAY;YAElB,SAAS;YACT,MAAM;YAEN,oBAAoB;YACpB,iBAAiB,CAAA;gBACf,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,MAAM,CAAC;gBACd,OAAO;YACT;YAEA,SAAS;YACT,kBAAkB,CAAA;gBAChB,MAAM,UAAU;oBAAE,GAAG,IAAI;gBAAC;gBAC1B,OAAO,OAAO,CAAC,QAAQ;gBACvB,OAAO;YACT;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAIA,WAAW;IACX,MAAM,6BAA6B,OAAO;QACxC,MAAM,gBAAgB;YACpB;YACA,4BAA4B;QAC9B;QAEA,kBACE,UACA,8BACA;IAEJ;IAEA,MAAM,8BAA8B,OAAO;QACzC,IAAI;YACF,sBAAsB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC;YAEhD,MAAM,SAAS,MAAM,qBAAqB;YAE1C,IAAI,OAAO,OAAO,EAAE;gBAClB,uBAAuB,CAAA,OAAQ,CAAC;wBAC9B,GAAG,IAAI;wBACP,CAAC,QAAQ,EAAE,OAAO,OAAO;oBAC3B,CAAC;YACH,OAAO;gBACL,kBAAkB;gBAClB,MAAM;gBACN,sBAAsB,CAAA;oBACpB,MAAM,SAAS,IAAI,IAAI;oBACvB,OAAO,MAAM,CAAC;oBACd,OAAO;gBACT;YACF;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,qCAAqC;YACnD,sBAAsB,CAAA;gBACpB,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,MAAM,CAAC;gBACd,OAAO;YACT;QACF;IACF;IAEA,cAAc;IACd,MAAM,wBAAwB,CAAC,SAAiB;QAC9C,uBAAuB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC;QACjD,wBAAwB,CAAA,OAAQ,CAAC;gBAC/B,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE,iBAAiB;YAC9B,CAAC;IACH;IAEA,gBAAgB;IAChB,MAAM,8BAA8B,CAAC;QACnC,uBAAuB,CAAA;YACrB,MAAM,SAAS,IAAI,IAAI;YACvB,OAAO,MAAM,CAAC;YACd,OAAO;QACT;QACA,wBAAwB,CAAA;YACtB,MAAM,UAAU;gBAAE,GAAG,IAAI;YAAC;YAC1B,OAAO,OAAO,CAAC,QAAQ;YACvB,OAAO;QACT;IACF;IAEA,cAAc;IACd,MAAM,wBAAwB,OAAO;QACnC,MAAM,YAAY,oBAAoB,CAAC,QAAQ;QAC/C,IAAI,cAAc,WAAW;QAE7B,IAAI;YACF,WAAW;YACX,MAAM,YAAY,SAAS;gBAAE,QAAQ;YAAU;YAC/C,MAAM;YACN,4BAA4B;QAC9B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;IACT,MAAM,wBAAwB,OAAO;QACnC,MAAM,gBAAgB;YACpB;YACA,uBAAuB;QACzB;QAEA,kBACE,UACA,6BACA;IAEJ;IAEA,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,sBAAsB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC;YAChD,MAAM,SAAS,MAAM,gBAAgB;YAErC,IAAI,OAAO,OAAO,EAAE;gBAClB,uBAAuB,CAAA,OAAQ,CAAC;wBAC9B,GAAG,IAAI;wBACP,CAAC,QAAQ,EAAE,OAAO,OAAO;oBAC3B,CAAC;YACH,OAAO;gBACL,MAAM;gBACN,sBAAsB,CAAA;oBACpB,MAAM,SAAS,IAAI,IAAI;oBACvB,OAAO,MAAM,CAAC;oBACd,OAAO;gBACT;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,sBAAsB,CAAA;gBACpB,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,MAAM,CAAC;gBACd,OAAO;YACT;QACF;IACF;IAEA,SAAS;IACT,MAAM,yBAAyB,CAAC,YAAoB;QAClD,wBAAwB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC;QAClD,yBAAyB,CAAA,OAAQ,CAAC;gBAChC,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE,eAAe;YAC/B,CAAC;IACH;IAEA,WAAW;IACX,MAAM,+BAA+B,CAAC;QACpC,wBAAwB,CAAA;YACtB,MAAM,SAAS,IAAI,IAAI;YACvB,OAAO,MAAM,CAAC;YACd,OAAO;QACT;QACA,yBAAyB,CAAA;YACvB,MAAM,UAAU;gBAAE,GAAG,IAAI;YAAC;YAC1B,OAAO,OAAO,CAAC,WAAW;YAC1B,OAAO;QACT;IACF;IAEA,SAAS;IACT,MAAM,yBAAyB,OAAO;QACpC,MAAM,UAAU,qBAAqB,CAAC,WAAW;QACjD,IAAI,YAAY,WAAW;QAE3B,IAAI;YACF,WAAW;YACX,MAAM,eAAe,YAAY;gBAAE,UAAU;YAAQ;YACrD,MAAM;YACN,6BAA6B;QAC/B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;IACT,MAAM,wBAAwB,OAAO;QACnC,MAAM,gBAAgB;YACpB;YACA,uBAAuB;QACzB;QAEA,kBACE,UACA,gCACA;IAEJ;IAEA,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,sBAAsB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC;YAChD,MAAM,SAAS,MAAM,gBAAgB;YAErC,IAAI,OAAO,OAAO,EAAE;gBAClB,uBAAuB,CAAA,OAAQ,CAAC;wBAC9B,GAAG,IAAI;wBACP,CAAC,WAAW,EAAE,OAAO,OAAO;oBAC9B,CAAC;YACH,OAAO;gBACL,MAAM;gBACN,sBAAsB,CAAA;oBACpB,MAAM,SAAS,IAAI,IAAI;oBACvB,OAAO,MAAM,CAAC;oBACd,OAAO;gBACT;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,sBAAsB,CAAA;gBACpB,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,MAAM,CAAC;gBACd,OAAO;YACT;QACF;IACF;IAEA,SAAS;IACT,MAAM,+BAA+B;QACnC,MAAM,gBAAgB;YACpB;YACA;QACF;QAEA,kBACE,UACA,+BACA;IAEJ;IAEA,MAAM,gCAAgC;QACpC,IAAI;YACF,4BAA4B;YAC5B,MAAM,SAAS,MAAM,uBAAuB,SAAS,EAAE;YAEvD,IAAI,OAAO,OAAO,EAAE;gBAClB,wBAAwB,OAAO,OAAO;YACxC,OAAO;gBACL,MAAM;gBACN,4BAA4B;YAC9B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,uCAAuC;YACrD,4BAA4B;QAC9B;IACF;IAEA,SAAS;IACT,MAAM,kBAAkB,OAAO;QAC7B,MAAM,YAAY,cAAc,CAAC,QAAQ;QACzC,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,WAAW;YACX,MAAM,YAAY,SAAS;gBACzB,OAAO,UAAU,KAAK;gBACtB,QAAQ,UAAU,MAAM;YAC1B;YAEA,SAAS;YACT,MAAM;YAEN,SAAS;YACT,iBAAiB,CAAA;gBACf,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,MAAM,CAAC;gBACd,OAAO;YACT;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,gBAAgB;IAChB,MAAM,wBAAwB,CAAC,SAAiB;QAC9C,uBAAuB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC;QACjD,wBAAwB,CAAA,OAAQ,CAAC;gBAC/B,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE;YACb,CAAC;IACH;IAEA,gBAAgB;IAChB,MAAM,8BAA8B,CAAC;QACnC,uBAAuB,CAAA;YACrB,MAAM,SAAS,IAAI,IAAI;YACvB,OAAO,MAAM,CAAC;YACd,OAAO;QACT;QACA,wBAAwB,CAAA;YACtB,MAAM,UAAU;gBAAE,GAAG,IAAI;YAAC;YAC1B,OAAO,OAAO,CAAC,QAAQ;YACvB,OAAO;QACT;IACF;IAEA,iBAAiB;IACjB,MAAM,wBAAwB,OAAO;QACnC,MAAM,YAAY,oBAAoB,CAAC,QAAQ;QAC/C,IAAI,CAAC,WAAW;QAEhB,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACxC,IAAI,CAAC,OAAO;QAEZ,IAAI;YACF,WAAW;YACX,MAAM,YAAY,SAAS;gBACzB,OAAO,MAAM,KAAK;gBAClB,QAAQ;YACV;YAEA,SAAS;YACT,MAAM;YAEN,SAAS;YACT,4BAA4B;QAE9B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;IACT,MAAM,qBAAqB;QACzB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,aAAa;gBACjB,UAAU,iBAAiB,QAAQ,CAAC,IAAI;gBACxC,WAAW,iBAAiB,SAAS,CAAC,IAAI,MAAM;YAClD;YAEA,MAAM,kBAAkB,MAAM,eAAe,SAAS,EAAE,EAAE;YAE1D,cAAc;YACd,mBAAmB;YAEnB,aAAa;YACb,IAAI,mBAAmB;gBACrB,kBAAkB;YACpB;YAEA,eAAe;YACf,MAAM;QAER,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,sSAAC;YAAI,WAAU;sBACb,cAAA,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,wSAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,sSAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;;;;;;IAIlC;IAEA,IAAI,OAAO;QACT,qBACE,sSAAC;YAAI,WAAU;sBACb,cAAA,sSAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BACf,cAAA,sSAAC,oIAAA,CAAA,mBAAgB;8BAAE;;;;;;;;;;;;;;;;IAI3B;IAEA,qBACE,sSAAC;QAAI,WAAU;;0BAEb,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC;oBAAI,WAAU;;sCACb,sSAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,SAAS;;8CACzC,sSAAC,uSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGnC,sSAAC,qKAAA,CAAA,oBAAiB;4BAChB,YAAY,SAAS,EAAE;4BACvB,gBAAgB;;;;;;sCAElB,sSAAC;;8CACC,sSAAC;oCAAG,WAAU;8CAAmC,SAAS,QAAQ;;;;;;8CAClE,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;;gDAAU;gDAAK,SAAS,EAAE;;;;;;;wCACxC,SAAS,SAAS,kBACjB,sSAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;;8DACb,sSAAC,+RAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAClB,SAAS,SAAS;;;;;;;sDAGvB,sSAAC;4CAAI,WAAU;;8DACb,sSAAC,iSAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDACnB,WAAW,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM1C,sSAAC;gBAAI,WAAU;;kCAEb,sSAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,sSAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,sSAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,sSAAC,+RAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAInC,sSAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,sSAAC;wCAAI,WAAU;;0DAEb,sSAAC;;kEACC,sSAAC;wDAAM,WAAU;kEAA4C;;;;;;kEAC7D,sSAAC;wDAAI,WAAU;kEACb,cAAA,sSAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAChC,SAAS,SAAS,IAAI;gEAAE;;;;;;;;;;;;;;;;;;0DAM/B,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAM,WAAU;kEAA4C;;;;;;kEAC7D,sSAAC;wDAAI,WAAU;kEACZ,SAAS,gBAAgB,iBACxB,sSAAC;4DAAE,WAAU;sEACV,SAAS,gBAAgB;;;;;iFAG5B,sSAAC;4DAAE,WAAU;sEAAuC;;;;;;;;;;;;;;;;;;;;;;;kDAS5D,sSAAC;;0DACC,sSAAC;gDAAM,WAAU;0DAA4C;;;;;;0DAC7D,sSAAC;gDAAI,WAAU;;oDACZ,SAAS,eAAe,iBACvB,sSAAC;wDAAI,WAAU;kEACb,cAAA,sSAAC;4DACC,KAAK,SAAS,eAAe;4DAC7B,KAAI;4DACJ,WAAU;;;;;;;;;;6EAId,sSAAC;wDAAI,WAAU;kEACb,cAAA,sSAAC,6RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAItB,sSAAC;wDAAI,WAAU;;4DACZ,SAAS,gBAAgB,kBACxB,sSAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAS;gEACT,UAAU;0EAET,yCACC;;sFACE,sSAAC,wSAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;wEAA8B;;iGAInD;;sFACE,sSAAC,6RAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEACjB,SAAS,eAAe,GAAG,SAAS;;;;;;;;4DAM5C,CAAC,SAAS,gBAAgB,kBACzB,sSAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;4CAQlD,sCACC,sSAAC;gDAAI,WAAU;0DACb,cAAA,sSAAC,2JAAA,CAAA,cAAW;oDACV,QAAQ;oDACR,YAAY;wDACV,4BAA4B;wDAC5B,wBAAwB;wDACxB,YAAY,OAAO;oDACrB;oDACA,SAAS;wDACP,4BAA4B;wDAC5B,wBAAwB;oDAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQZ,sSAAC,mIAAA,CAAA,OAAI;wBAAC,OAAO;wBAAW,eAAe;;0CACrC,sSAAC,mIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,sSAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;;4CAChB,EAAE;4CAAU;4CAAG,OAAO,MAAM;4CAAC;;;;;;;kDAEhC,sSAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;;4CAChB,EAAE;4CAAU;4CAAG,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,EAAE,MAAM;4CAAC;;;;;;;kDAErD,sSAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;;4CAChB,EAAE;4CAAU;4CAAG,OAAO,MAAM;4CAAC;;;;;;;kDAEhC,sSAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;;4CAChB,EAAE;4CAAa;4CAAG,UAAU,MAAM;4CAAC;;;;;;;;;;;;;0CAIxC,sSAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAS,WAAU;0CACpC,cAAA,sSAAC;oCAAI,WAAU;8CACZ,OAAO,MAAM,KAAK,kBACjB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,6RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,sSAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,sSAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;6DAGvC,sSAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC;4CACX,aAAa;4CACb,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK,MAAM,EAAE;4CACxE,qBAAqB;4CACrB,MAAM,mBAAmB,UAAU,MAAM,CAAC,CAAA,IACxC,cAAc,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,EAAE,QAAQ;4CAErD,MAAM,YAAY,cAAc,GAAG,CAAC,MAAM,EAAE;4CAE5C,qBACE,sSAAC,mIAAA,CAAA,OAAI;;kEACH,sSAAC,mIAAA,CAAA,aAAU;wDAAC,WAAU;kEACpB,cAAA,sSAAC;4DAAI,WAAU;;8EACb,sSAAC,mIAAA,CAAA,YAAS;oEAAC,WAAU;;wEAAY;wEACvB,MAAM,EAAE;;;;;;;8EAElB,sSAAC;oEAAI,WAAU;;sFACb,sSAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;sFAChC,WAAW,MAAM,WAAW;;;;;;sFAE/B,sSAAC,qIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS;gFACP,IAAI,WAAW;oFACb,iBAAiB,CAAA;wFACf,MAAM,SAAS,IAAI,IAAI;wFACvB,OAAO,MAAM,CAAC,MAAM,EAAE;wFACtB,OAAO;oFACT;gFACF,OAAO;oFACL,iBAAiB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC,MAAM,EAAE;oFACnD,kBAAkB,CAAA,OAAQ,CAAC;4FACzB,GAAG,IAAI;4FACP,CAAC,MAAM,EAAE,CAAC,EAAE;gGACV,OAAO,MAAM,KAAK;gGAClB,QAAQ,MAAM,MAAM;4FACtB;wFACF,CAAC;gFACH;4EACF;sFAEC,0BAAY,sSAAC,mRAAA,CAAA,IAAC;gFAAC,WAAU;;;;;qGAAe,sSAAC,yRAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;;;;;;sFAG5D,sSAAC,qIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,kBAAkB,MAAM,EAAE;4EACzC,WAAU;sFAEV,cAAA,sSAAC,iSAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAK1B,sSAAC,mIAAA,CAAA,cAAW;wDAAC,WAAU;;0EAErB,sSAAC;;kFACC,sSAAC;wEAAM,WAAU;kFAA4C;;;;;;oEAC5D,0BACC,sSAAC,oIAAA,CAAA,QAAK;wEACJ,OAAO,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,SAAS,MAAM,KAAK;wEACrD,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;oFAC1C,GAAG,IAAI;oFACP,CAAC,MAAM,EAAE,CAAC,EAAE;wFACV,OAAO,EAAE,MAAM,CAAC,KAAK;wFACrB,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,UAAU,MAAM,MAAM;oFAChD;gFACF,CAAC;wEACD,WAAU;;;;;6FAGZ,sSAAC;wEAAE,WAAU;kFAA4B,MAAM,KAAK;;;;;;;;;;;;0EAKxD,sSAAC;;kFACC,sSAAC;wEAAM,WAAU;kFAA4C;;;;;;oEAC5D,0BACC,sSAAC,uIAAA,CAAA,WAAQ;wEACP,OAAO,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,UAAU,MAAM,MAAM;wEACvD,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;oFAC1C,GAAG,IAAI;oFACP,CAAC,MAAM,EAAE,CAAC,EAAE;wFACV,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,SAAS,MAAM,KAAK;wFAC3C,QAAQ,EAAE,MAAM,CAAC,KAAK;oFACxB;gFACF,CAAC;wEACD,WAAU;wEACV,MAAM;;;;;6FAGR,sSAAC;wEAAE,WAAU;kFAAsC,MAAM,MAAM;;;;;;;;;;;;0EAKnE,sSAAC;gEAAI,WAAU;;kFACb,sSAAC;wEAAI,WAAU;;0FACb,sSAAC,2RAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;0FACjB,sSAAC;;oFAAM,MAAM,KAAK,GAAG,IAAI;oFAAE;;;;;;;;;;;;;kFAE7B,sSAAC;wEAAI,WAAU;;0FACb,sSAAC,2RAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;0FACjB,sSAAC;;oFAAM,cAAc,MAAM;oFAAC;;;;;;;;;;;;;kFAE9B,sSAAC;wEAAI,WAAU;;0FACb,sSAAC,kSAAA,CAAA,YAAS;gFAAC,WAAU;;;;;;0FACrB,sSAAC;;oFAAM,iBAAiB,MAAM;oFAAC;;;;;;;;;;;;;;;;;;;4DAKlC,2BACC,sSAAC;gEAAI,WAAU;;kFACb,sSAAC,qIAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAS,IAAM,gBAAgB,MAAM,EAAE;wEACvC,UAAU;;0FAEV,sSAAC,yRAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;kFAGnC,sSAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS;4EACP,iBAAiB,CAAA;gFACf,MAAM,SAAS,IAAI,IAAI;gFACvB,OAAO,MAAM,CAAC,MAAM,EAAE;gFACtB,OAAO;4EACT;wEACF;wEACA,UAAU;kFACX;;;;;;;;;;;;;;;;;;;+CA9HE,MAAM,EAAE;;;;;wCAsIvB;;;;;;;;;;;;;;;;0CAMR,sSAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAS,WAAU;0CACpC,cAAA,sSAAC;oCAAI,WAAU;8CACZ,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK,EAAE,MAAM,KAAK,kBAC9C,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,2RAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,sSAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,sSAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;6DAGvC,sSAAC;wCAAI,WAAU;kDACZ,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK,EAAE,GAAG,CAAC,CAAC;4CAExC,qBACE,sSAAC,mIAAA,CAAA,OAAI;gDAAgB,WAAU;;kEAC7B,sSAAC;wDAAI,WAAU;;0EACb,sSAAC;gEACC,KAAK,MAAM,KAAK;gEAChB,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC;gEAC9B,WAAU;gEACV,SAAS,CAAC;oEACR,MAAM,SAAS,EAAE,MAAM;oEACvB,OAAO,KAAK,CAAC,OAAO,GAAG;oEACvB,MAAM,SAAS,OAAO,aAAa;oEACnC,IAAI,QAAQ;wEACV,OAAO,SAAS,GAAG,CAAC;;;;;;;;;;;gCAWpB,CAAC;oEACH;gEACF;;;;;;0EAEF,sSAAC;gEAAI,WAAU;0EACb,cAAA,sSAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;;wEAAU;wEACrC,MAAM,EAAE;;;;;;;;;;;;;;;;;;kEAKtB,sSAAC,mIAAA,CAAA,cAAW;wDAAC,WAAU;kEACrB,cAAA,sSAAC;4DAAI,WAAU;;8EACb,sSAAC;;sFACC,sSAAC;4EAAE,WAAU;sFACV,MAAM,KAAK;;;;;;wEAIb,oBAAoB,GAAG,CAAC,MAAM,EAAE,kBAC/B,sSAAC;4EAAI,WAAU;;8FACb,sSAAC,uIAAA,CAAA,WAAQ;oFACP,OAAO,oBAAoB,CAAC,MAAM,EAAE,CAAC,IAAI,MAAM,MAAM;oFACrD,UAAU,CAAC,IAAM,wBAAwB,CAAA,OAAQ,CAAC;gGAChD,GAAG,IAAI;gGACP,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;4FAC5B,CAAC;oFACD,aAAY;oFACZ,WAAU;;;;;;8FAEZ,sSAAC;oFAAI,WAAU;;sGACb,sSAAC,qIAAA,CAAA,SAAM;4FACL,MAAK;4FACL,SAAS,IAAM,sBAAsB,MAAM,EAAE;4FAC7C,WAAU;;8GAEV,sSAAC,yRAAA,CAAA,OAAI;oGAAC,WAAU;;;;;;gGAAiB;;;;;;;sGAGnC,sSAAC,qIAAA,CAAA,SAAM;4FACL,SAAQ;4FACR,MAAK;4FACL,SAAS,IAAM,4BAA4B,MAAM,EAAE;4FACnD,WAAU;;8GAEV,sSAAC,mRAAA,CAAA,IAAC;oGAAC,WAAU;;;;;;gGAAiB;;;;;;;;;;;;;;;;;;iGAMpC,sSAAC;4EAAI,WAAU;;8FACb,sSAAC;oFAAE,WAAU;8FACV,MAAM,MAAM;;;;;;8FAEf,sSAAC;oFAAI,WAAU;;sGACb,sSAAC,qIAAA,CAAA,SAAM;4FACL,SAAQ;4FACR,MAAK;4FACL,SAAS,IAAM,sBAAsB,MAAM,EAAE,EAAE,MAAM,MAAM;4FAC3D,WAAU;;8GAEV,sSAAC,yRAAA,CAAA,QAAK;oGAAC,WAAU;;;;;;gGAAiB;;;;;;;sGAIpC,sSAAC,qIAAA,CAAA,SAAM;4FACL,SAAQ;4FACR,MAAK;4FACL,SAAS,IAAM,2BAA2B,MAAM,EAAE;4FAClD,UAAU,mBAAmB,GAAG,CAAC,MAAM,EAAE;4FACzC,WAAU;;gGAET,mBAAmB,GAAG,CAAC,MAAM,EAAE,kBAC9B,sSAAC,wSAAA,CAAA,UAAO;oGAAC,WAAU;;;;;yHAEnB,sSAAC,uSAAA,CAAA,YAAS;oGAAC,WAAU;;;;;;gGACrB;;;;;;;;;;;;;;;;;;;;;;;;;gEASX,mBAAmB,GAAG,CAAC,MAAM,EAAE,KAAK,mBAAmB,CAAC,MAAM,EAAE,CAAC,kBAChE,sSAAC;oEAAI,WAAU;8EACb,cAAA,sSAAC,2JAAA,CAAA,cAAW;wEACV,QAAQ,mBAAmB,CAAC,MAAM,EAAE,CAAC,IAAI;wEACzC,YAAY;4EACV,sBAAsB,CAAA;gFACpB,MAAM,SAAS,IAAI,IAAI;gFACvB,OAAO,MAAM,CAAC,MAAM,EAAE;gFACtB,OAAO;4EACT;4EACA,uBAAuB,CAAA;gFACrB,MAAM,SAAS;oFAAE,GAAG,IAAI;gFAAC;gFACzB,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC;gFACvB,OAAO;4EACT;4EACA,YAAY,OAAO;wEACrB;wEACA,SAAS;4EACP,sBAAsB,CAAA;gFACpB,MAAM,SAAS,IAAI,IAAI;gFACvB,OAAO,MAAM,CAAC,MAAM,EAAE;gFACtB,OAAO;4EACT;4EACA,uBAAuB,CAAA;gFACrB,MAAM,SAAS;oFAAE,GAAG,IAAI;gFAAC;gFACzB,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC;gFACvB,OAAO;4EACT;wEACF;;;;;;;;;;;8EAKN,sSAAC;oEAAI,WAAU;;sFACb,sSAAC;4EAAK,WAAU;;8FACd,sSAAC,iSAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;gFACnB,WAAW,MAAM,WAAW;;;;;;;wEAE9B,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,MAAM,EAAE,mBACvC,sSAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;sFAAU;;;;;;;;;;;;8EAOjD,sSAAC;oEAAI,WAAU;8EACb,cAAA,sSAAC;wEAAE,WAAU;kFACV,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;+CA5JX,MAAM,EAAE;;;;;wCAmKvB;;;;;;;;;;;;;;;;0CAMR,sSAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAS,WAAU;0CACpC,cAAA,sSAAC;oCAAI,WAAU;8CACZ,OAAO,MAAM,KAAK,kBACjB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,2RAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,sSAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,sSAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;6DAGvC,sSAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC;4CACX,MAAM,eAAe,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,QAAQ;4CAE7D,qBACE,sSAAC,mIAAA,CAAA,OAAI;gDAAgB,WAAU;;kEAC7B,sSAAC;wDAAI,WAAU;;0EACb,sSAAC;gEACC,KAAK,MAAM,KAAK;gEAChB,WAAU;gEACV,QAAQ;gEACR,SAAS,CAAC;oEACR,MAAM,SAAS,EAAE,MAAM;oEACvB,OAAO,KAAK,CAAC,OAAO,GAAG;oEACvB,MAAM,SAAS,OAAO,aAAa;oEACnC,IAAI,QAAQ;wEACV,OAAO,SAAS,GAAG,CAAC;;;;;;;;;;;gCAWpB,CAAC;oEACH;gEACF;;;;;;0EAEF,sSAAC;gEAAI,WAAU;0EACb,cAAA,sSAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;;wEAAU;wEACrC,MAAM,EAAE;;;;;;;;;;;;;;;;;;kEAKtB,sSAAC,mIAAA,CAAA,cAAW;wDAAC,WAAU;kEACrB,cAAA,sSAAC;4DAAI,WAAU;;8EACb,sSAAC;;sFACC,sSAAC;4EAAE,WAAU;sFACV,eAAe,aAAa,KAAK,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;;;;;;wEAI1D,oBAAoB,GAAG,CAAC,MAAM,EAAE,kBAC/B,sSAAC;4EAAI,WAAU;;8FACb,sSAAC,uIAAA,CAAA,WAAQ;oFACP,OAAO,oBAAoB,CAAC,MAAM,EAAE,CAAC,IAAI,MAAM,MAAM,IAAI;oFACzD,UAAU,CAAC,IAAM,wBAAwB,CAAA,OAAQ,CAAC;gGAChD,GAAG,IAAI;gGACP,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;4FAC5B,CAAC;oFACD,aAAY;oFACZ,WAAU;;;;;;8FAEZ,sSAAC;oFAAI,WAAU;;sGACb,sSAAC,qIAAA,CAAA,SAAM;4FACL,MAAK;4FACL,SAAS,IAAM,sBAAsB,MAAM,EAAE;4FAC7C,WAAU;;8GAEV,sSAAC,yRAAA,CAAA,OAAI;oGAAC,WAAU;;;;;;gGAAiB;;;;;;;sGAGnC,sSAAC,qIAAA,CAAA,SAAM;4FACL,SAAQ;4FACR,MAAK;4FACL,SAAS,IAAM,4BAA4B,MAAM,EAAE;4FACnD,WAAU;;8GAEV,sSAAC,mRAAA,CAAA,IAAC;oGAAC,WAAU;;;;;;gGAAiB;;;;;;;;;;;;;;;;;;iGAMpC,sSAAC;4EAAI,WAAU;;gFACZ,MAAM,MAAM,kBACX,sSAAC;;sGACC,sSAAC;4FAAE,WAAU;sGAA4C;;;;;;sGACzD,sSAAC;4FAAE,WAAU;sGACV,MAAM,MAAM;;;;;;;;;;;;8FAInB,sSAAC;oFAAI,WAAU;;sGACb,sSAAC,qIAAA,CAAA,SAAM;4FACL,SAAQ;4FACR,MAAK;4FACL,SAAS,IAAM,sBAAsB,MAAM,EAAE,EAAE,MAAM,MAAM,IAAI;4FAC/D,WAAU;;8GAEV,sSAAC,yRAAA,CAAA,QAAK;oGAAC,WAAU;;;;;;gGAAiB;;;;;;;sGAGpC,sSAAC,qIAAA,CAAA,SAAM;4FACL,SAAQ;4FACR,MAAK;4FACL,SAAS,IAAM,sBAAsB,MAAM,EAAE;4FAC7C,UAAU,mBAAmB,GAAG,CAAC,MAAM,EAAE;4FACzC,WAAU;;gGAET,mBAAmB,GAAG,CAAC,MAAM,EAAE,kBAC9B,sSAAC,wSAAA,CAAA,UAAO;oGAAC,WAAU;;;;;yHAEnB,sSAAC,uSAAA,CAAA,YAAS;oGAAC,WAAU;;;;;;gGACrB;;;;;;;;;;;;;;;;;;;;;;;;;gEASX,mBAAmB,GAAG,CAAC,MAAM,EAAE,KAAK,mBAAmB,CAAC,MAAM,EAAE,CAAC,kBAChE,sSAAC;oEAAI,WAAU;8EACb,cAAA,sSAAC,2JAAA,CAAA,cAAW;wEACV,QAAQ,mBAAmB,CAAC,MAAM,EAAE,CAAC,IAAI;wEACzC,YAAY;4EACV,sBAAsB,CAAA;gFACpB,MAAM,SAAS,IAAI,IAAI;gFACvB,OAAO,MAAM,CAAC,MAAM,EAAE;gFACtB,OAAO;4EACT;4EACA,uBAAuB,CAAA;gFACrB,MAAM,SAAS;oFAAE,GAAG,IAAI;gFAAC;gFACzB,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC;gFACvB,OAAO;4EACT;4EACA,YAAY,OAAO;wEACrB;wEACA,SAAS;4EACP,sBAAsB,CAAA;gFACpB,MAAM,SAAS,IAAI,IAAI;gFACvB,OAAO,MAAM,CAAC,MAAM,EAAE;gFACtB,OAAO;4EACT;4EACA,uBAAuB,CAAA;gFACrB,MAAM,SAAS;oFAAE,GAAG,IAAI;gFAAC;gFACzB,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC;gFACvB,OAAO;4EACT;wEACF;;;;;;;;;;;8EAKN,sSAAC;oEAAI,WAAU;;sFACb,sSAAC;4EAAK,WAAU;;8FACd,sSAAC,iSAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;gFACnB,WAAW,MAAM,WAAW;;;;;;;wEAE9B,cAAc,uBACb,sSAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;sFAAU;;;;;;;;;;;;8EAOjD,sSAAC;oEAAI,WAAU;8EACb,cAAA,sSAAC;wEAAE,WAAU;kFACV,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;+CAhKX,MAAM,EAAE;;;;;wCAuKvB;;;;;;;;;;;;;;;;0CAMR,sSAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;0CACvC,cAAA,sSAAC;oCAAI,WAAU;8CACZ,UAAU,MAAM,KAAK,kBACpB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,kSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,sSAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,sSAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;6DAGvC,sSAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC;4CACd,MAAM,eAAe,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,SAAS,QAAQ;4CACxE,MAAM,eAAe,eAAe,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,QAAQ,IAAI;4CACvF,MAAM,YAAY,qBAAqB,GAAG,CAAC,SAAS,EAAE;4CAEtD,qBACE,sSAAC,mIAAA,CAAA,OAAI;gDAAmB,WAAU;;kEAEhC,sSAAC;wDAAI,WAAU;;0EACb,sSAAC;gEAAI,WAAU;0EACZ,0BACC,sSAAC,uIAAA,CAAA,WAAQ;oEACP,OAAO,qBAAqB,CAAC,SAAS,EAAE,CAAC,IAAI,SAAS,QAAQ,IAAI;oEAClE,UAAU,CAAC,IAAM,yBAAyB,CAAA,OAAQ,CAAC;gFACjD,GAAG,IAAI;gFACP,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;4EAC/B,CAAC;oEACD,WAAU;oEACV,MAAM;oEACN,aAAY;;;;;yFAGd,sSAAC;oEAAE,WAAU;8EACV,SAAS,QAAQ,IAAI;;;;;;;;;;;0EAM5B,sSAAC;gEAAI,WAAU;0EACb,cAAA,sSAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;;wEAAU;wEAClC,SAAS,EAAE;;;;;;;;;;;;;;;;;;kEAK5B,sSAAC,mIAAA,CAAA,cAAW;wDAAC,WAAU;kEACrB,cAAA,sSAAC;4DAAI,WAAU;;8EAEb,sSAAC;oEAAI,WAAU;;sFACb,sSAAC;4EAAI,WAAU;;gFACZ,8BACC,sSAAC;;wFAAK;wFAAO,aAAa,KAAK;;;;;;;8FAEjC,sSAAC,oIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,WAAU;;wFAAU;wFACnC,SAAS,QAAQ;;;;;;;;;;;;;sFAG7B,sSAAC;4EAAK,WAAU;;8FACd,sSAAC,iSAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;gFACnB,WAAW,SAAS,WAAW;;;;;;;;;;;;;gEAKnC,SAAS,KAAK,kBACb,sSAAC;8EACC,cAAA,sSAAC;wEACC,KAAK,SAAS,KAAK;wEACnB,QAAQ;wEACR,WAAU;wEACV,SAAS,CAAC;4EACR,MAAM,SAAS,EAAE,MAAM;4EACvB,OAAO,KAAK,CAAC,OAAO,GAAG;4EACvB,MAAM,SAAS,OAAO,aAAa;4EACnC,IAAI,QAAQ;gFACV,OAAO,SAAS,GAAG,CAAC;;;;sCAIpB,CAAC;4EACH;wEACF;;;;;;;;;;;8EAMN,sSAAC;oEAAI,WAAU;8EACd,0BACC;;0FACE,sSAAC,qIAAA,CAAA,SAAM;gFACL,MAAK;gFACL,SAAS,IAAM,uBAAuB,SAAS,EAAE;;kGAEjD,sSAAC,yRAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAGnC,sSAAC,qIAAA,CAAA,SAAM;gFACL,SAAQ;gFACR,MAAK;gFACL,SAAS,IAAM,6BAA6B,SAAS,EAAE;;kGAEvD,sSAAC,mRAAA,CAAA,IAAC;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;qGAKlC;;0FACE,sSAAC,qIAAA,CAAA,SAAM;gFACL,SAAQ;gFACR,MAAK;gFACL,SAAS,IAAM,uBAAuB,SAAS,EAAE,EAAE,SAAS,QAAQ,IAAI;;kGAExE,sSAAC,yRAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAGpC,sSAAC,qIAAA,CAAA,SAAM;gFACL,SAAQ;gFACR,MAAK;gFACL,SAAS,IAAM,sBAAsB,SAAS,EAAE;gFAChD,UAAU,mBAAmB,GAAG,CAAC,SAAS,EAAE;;oFAE3C,mBAAmB,GAAG,CAAC,SAAS,EAAE,kBACjC,sSAAC,wSAAA,CAAA,UAAO;wFAAC,WAAU;;;;;6GAEnB,sSAAC,uSAAA,CAAA,YAAS;wFAAC,WAAU;;;;;;oFACrB;;;;;;;;;;;;;;gEAQT,mBAAmB,GAAG,CAAC,SAAS,EAAE,KAAK,mBAAmB,CAAC,SAAS,EAAE,CAAC,kBACtE,sSAAC;oEAAI,WAAU;8EACb,cAAA,sSAAC,2JAAA,CAAA,cAAW;wEACV,QAAQ,mBAAmB,CAAC,SAAS,EAAE,CAAC,IAAI;wEAC5C,YAAY;4EACV,sBAAsB,CAAA;gFACpB,MAAM,SAAS,IAAI,IAAI;gFACvB,OAAO,MAAM,CAAC,SAAS,EAAE;gFACzB,OAAO;4EACT;4EACA,uBAAuB,CAAA;gFACrB,MAAM,UAAU;oFAAE,GAAG,IAAI;gFAAC;gFAC1B,OAAO,OAAO,CAAC,SAAS,EAAE,CAAC;gFAC3B,OAAO;4EACT;4EACA;wEACF;wEACA,SAAS,CAAC;4EACR,sBAAsB,CAAA;gFACpB,MAAM,SAAS,IAAI,IAAI;gFACvB,OAAO,MAAM,CAAC,SAAS,EAAE;gFACzB,OAAO;4EACT;4EACA,SAAS,CAAC,QAAQ,EAAE,OAAO;wEAC7B;;;;;;;;;;;;;;;;;;;;;;;+CA/IC,SAAS,EAAE;;;;;wCAuJ1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASZ,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,iKAAA,CAAA,oBAAiB;oBAChB,YAAY,SAAS,EAAE;oBACvB,WAAW;;;;;;;;;;;0BAKf,sSAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM,cAAc,IAAI;gBAAE,cAAc;0BAC9C,cAAA,sSAAC,qIAAA,CAAA,gBAAa;;sCACZ,sSAAC,qIAAA,CAAA,eAAY;;8CACX,sSAAC,qIAAA,CAAA,cAAW;8CAAE,cAAc,KAAK;;;;;;8CACjC,sSAAC,qIAAA,CAAA,oBAAiB;8CAAE,cAAc,WAAW;;;;;;;;;;;;sCAE/C,sSAAC,qIAAA,CAAA,eAAY;;8CACX,sSAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAoB;;;;;;8CAGvD,sSAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAc,SAAS,cAAc,SAAS;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5E;GA94CgB;;QACJ,wTAAA,CAAA,kBAAe;QAuDgK,yJAAA,CAAA,cAAW;;;KAxDtL", "debugId": null}}, {"offset": {"line": 5419, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/app/workflow/components/workflow-main.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { CreativesList } from \"./creatives-list\";\nimport { CreativeDetail } from \"./creative-detail\";\nimport { useVideoAPI } from \"../hooks/use-video-api\";\nimport { Alert, AlertDescription } from \"~/components/ui/alert\";\nimport { Loader2 } from \"lucide-react\";\n\nexport interface Creative {\n  id: number;\n  creative: string;\n  art_style?: string;\n  update_time: string;\n}\n\nexport interface Scene {\n  id: number;\n  creative_id: number;\n  scene: string;\n  prompt: string;\n  image?: string;\n  update_time: string;\n}\n\nexport interface SceneImage {\n  id: number;\n  scene_id: number;\n  image?: string;\n  video?: string;\n  update_time: string;\n}\n\nexport interface Subtitle {\n  id: number;\n  image_id: number;\n  subtitle?: string;\n  audio?: string;\n  update_time: string;\n}\n\nexport default function WorkflowMain() {\n  const [selectedCreative, setSelectedCreative] = useState<Creative | null>(null);\n  const [creatives, setCreatives] = useState<Creative[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const { fetchCreatives } = useVideoAPI();\n\n  // 加载创意列表\n  useEffect(() => {\n    const loadCreatives = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n        const data = await fetchCreatives();\n        setCreatives(data.items || []);\n      } catch (err) {\n        console.error(\"Failed to load creatives:\", err);\n        setError(\"Failed to load creatives. Please check your API connection.\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadCreatives();\n  }, [fetchCreatives]);\n\n  // 处理创意选择\n  const handleCreativeSelect = (creative: Creative) => {\n    setSelectedCreative(creative);\n  };\n\n  // 处理返回列表\n  const handleBackToList = () => {\n    setSelectedCreative(null);\n  };\n\n  // 处理创意创建成功\n  const handleCreativeCreated = async () => {\n    try {\n      setError(null);\n      const data = await fetchCreatives();\n      setCreatives(data.items || []);\n    } catch (err) {\n      console.error(\"Failed to refresh creatives after creation:\", err);\n      setError(\"Failed to refresh creatives list.\");\n    }\n  };\n\n  // 处理创意更新成功\n  const handleCreativeUpdated = (updatedCreative: Creative) => {\n    // 更新创意列表中的对应项\n    setCreatives(prev =>\n      prev.map(creative =>\n        creative.id === updatedCreative.id ? updatedCreative : creative\n      )\n    );\n\n    // 更新当前选中的创意\n    setSelectedCreative(updatedCreative);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex h-full w-full items-center justify-center\">\n        <div className=\"flex items-center gap-2\">\n          <Loader2 className=\"h-6 w-6 animate-spin\" />\n          <span className=\"text-lg\">Loading creatives...</span>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"flex h-full w-full items-center justify-center p-6\">\n        <Alert className=\"max-w-md\">\n          <AlertDescription>{error}</AlertDescription>\n        </Alert>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-full w-full\">\n      {selectedCreative ? (\n        <CreativeDetail\n          creative={selectedCreative}\n          onBack={handleBackToList}\n          onCreativeUpdated={handleCreativeUpdated}\n        />\n      ) : (\n        <CreativesList\n          creatives={creatives}\n          onCreativeSelect={handleCreativeSelect}\n          onRefresh={() => window.location.reload()}\n          onCreativeCreated={handleCreativeCreated}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAyCe,SAAS;;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAErC,SAAS;IACT,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;wDAAgB;oBACpB,IAAI;wBACF,WAAW;wBACX,SAAS;wBACT,MAAM,OAAO,MAAM;wBACnB,aAAa,KAAK,KAAK,IAAI,EAAE;oBAC/B,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,6BAA6B;wBAC3C,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;iCAAG;QAAC;KAAe;IAEnB,SAAS;IACT,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;IACtB;IAEA,SAAS;IACT,MAAM,mBAAmB;QACvB,oBAAoB;IACtB;IAEA,WAAW;IACX,MAAM,wBAAwB;QAC5B,IAAI;YACF,SAAS;YACT,MAAM,OAAO,MAAM;YACnB,aAAa,KAAK,KAAK,IAAI,EAAE;QAC/B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,SAAS;QACX;IACF;IAEA,WAAW;IACX,MAAM,wBAAwB,CAAC;QAC7B,cAAc;QACd,aAAa,CAAA,OACX,KAAK,GAAG,CAAC,CAAA,WACP,SAAS,EAAE,KAAK,gBAAgB,EAAE,GAAG,kBAAkB;QAI3D,YAAY;QACZ,oBAAoB;IACtB;IAEA,IAAI,SAAS;QACX,qBACE,sSAAC;YAAI,WAAU;sBACb,cAAA,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,wSAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,sSAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;;;;;;IAIlC;IAEA,IAAI,OAAO;QACT,qBACE,sSAAC;YAAI,WAAU;sBACb,cAAA,sSAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BACf,cAAA,sSAAC,oIAAA,CAAA,mBAAgB;8BAAE;;;;;;;;;;;;;;;;IAI3B;IAEA,qBACE,sSAAC;QAAI,WAAU;kBACZ,iCACC,sSAAC,8JAAA,CAAA,iBAAc;YACb,UAAU;YACV,QAAQ;YACR,mBAAmB;;;;;iCAGrB,sSAAC,6JAAA,CAAA,gBAAa;YACZ,WAAW;YACX,kBAAkB;YAClB,WAAW,IAAM,OAAO,QAAQ,CAAC,MAAM;YACvC,mBAAmB;;;;;;;;;;;AAK7B;GArGwB;;QAMK,yJAAA,CAAA,cAAW;;;KANhB", "debugId": null}}]}