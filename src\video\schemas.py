"""
Pydantic模式定义，用于API请求和响应
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field


# VideoCreative 相关模式
class VideoCreativeBase(BaseModel):
    creative: str = Field(..., description="创意描述")
    art_style: Optional[str] = Field(None, description="艺术风格（如：写实、卡通等）")
    scene_cnt: int = Field(5, description="场景数量", ge=1, le=10)
    character_prompt: str = Field(..., description="主角形象描述提示词")
    character_image: Optional[str] = Field(None, description="主角的图片地址")


class VideoCreativeCreate(VideoCreativeBase):
    pass


class VideoCreativeUpdate(BaseModel):
    creative: Optional[str] = Field(None, description="创意描述")
    art_style: Optional[str] = Field(None, description="艺术风格（如：写实、卡通等）")
    scene_cnt: Optional[int] = Field(None, description="场景数量", ge=1, le=10)
    character_prompt: Optional[str] = Field(None, description="主角形象描述提示词")
    character_image: Optional[str] = Field(None, description="主角的图片地址")


class VideoCreativeResponse(VideoCreativeBase):
    id: int = Field(..., description="主键ID")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


# VideoScene 相关模式
class VideoSceneBase(BaseModel):
    creative_id: int = Field(..., description="关联的创意ID")
    scene: str = Field(..., description="场景描述或名称")
    prompt: str = Field(..., description="文生图的提示词")
    image: Optional[str] = Field(None, description="生成的图片存储路径或URL")


class VideoSceneCreate(VideoSceneBase):
    pass


class VideoSceneUpdate(BaseModel):
    creative_id: Optional[int] = Field(None, description="关联的创意ID")
    scene: Optional[str] = Field(None, description="场景描述或名称")
    prompt: Optional[str] = Field(None, description="文生图的提示词")
    image: Optional[str] = Field(None, description="生成的图片存储路径或URL")


class VideoSceneResponse(VideoSceneBase):
    id: int = Field(..., description="主键ID")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


# VideoVideo 相关模式
class VideoVideoBase(BaseModel):
    scene_id: int = Field(..., description="关联的场景ID")
    prompt: Optional[str] = Field(None, description="图生视频的提示词")
    video: Optional[str] = Field(None, description="生成的视频存储路径或URL")


class VideoVideoCreate(VideoVideoBase):
    pass


class VideoVideoUpdate(BaseModel):
    scene_id: Optional[int] = Field(None, description="关联的场景ID")
    prompt: Optional[str] = Field(None, description="图生视频的提示词")
    video: Optional[str] = Field(None, description="生成的视频存储路径或URL")


class VideoVideoResponse(VideoVideoBase):
    id: int = Field(..., description="主键ID")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


# VideoSubtitle 相关模式
class VideoSubtitleBase(BaseModel):
    video_id: int = Field(..., description="关联的视频ID")
    subtitle: Optional[str] = Field(None, description="字幕文本内容")
    audio: Optional[str] = Field(None, description="对应字幕的音频文件存储路径或URL")


class VideoSubtitleCreate(VideoSubtitleBase):
    pass


class VideoSubtitleUpdate(BaseModel):
    video_id: Optional[int] = Field(None, description="关联的视频ID")
    subtitle: Optional[str] = Field(None, description="字幕文本内容")
    audio: Optional[str] = Field(None, description="对应字幕的音频文件存储路径或URL")


class VideoSubtitleResponse(VideoSubtitleBase):
    id: int = Field(..., description="主键ID")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


# 分页响应模式
class PaginatedResponse(BaseModel):
    items: List[dict] = Field(..., description="数据列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")

class GenerateRequest(BaseModel):
    creative_id: int
