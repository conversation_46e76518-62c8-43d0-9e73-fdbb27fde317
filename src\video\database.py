"""
数据库配置和连接管理
"""
import os
from typing import Generator
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from dotenv import load_dotenv

load_dotenv()

# 数据库配置
VIDEOFLOW_SERVER = os.getenv("VIDEOFLOW_SERVER", "rm-2ze2h0oa62u0cx5w2.mysql.rds.aliyuncs.com")
VIDEOFLOW_PORT = os.getenv("VIDEOFLOW_PORT", "3306")
VIDEOFLOW_USER = os.getenv("VIDEOFLOW_USER", "microlens")
VIDEOFLOW_PWD = os.getenv("VIDEOFLOW_PWD", "m7i*C#r4o9")
VIDEOFLOW_DB = os.getenv("VIDEOFLOW_DB", "video_flow")

# 构建数据库连接URL
DATABASE_URL = f"mysql+pymysql://{VIDEOFLOW_USER}:{VIDEOFLOW_PWD}@{VIDEOFLOW_SERVER}:{VIDEOFLOW_PORT}/{VIDEOFLOW_DB}?charset=utf8mb4"

# 创建数据库引擎
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=10,
    max_overflow=20,
    echo=False  # 设置为True可以看到SQL语句
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def init_db():
    """
    初始化数据库，创建所有表
    """
    Base.metadata.create_all(bind=engine)
