"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Plus, Loader2 } from "lucide-react";
import { useVideoAPI } from "../hooks/use-video-api";
import { Alert, AlertDescription } from "~/components/ui/alert";

interface CreateSceneDialogProps {
  creativeId: number;
  onSceneCreated: () => void;
}

export function CreateSceneDialog({ creativeId, onSceneCreated }: CreateSceneDialogProps) {
  const t = useTranslations("workflow.detail.create");
  const tScenes = useTranslations("workflow.detail");
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    scene: "",
    prompt: "",
  });

  const { addScene } = useVideoAPI();

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.scene.trim()) {
      setError(t("errorSceneRequired"));
      return;
    }

    if (!formData.prompt.trim()) {
      setError(t("errorPromptRequired"));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const data = {
        creative_id: creativeId,
        scene: formData.scene.trim(),
        prompt: formData.prompt.trim(),
      };

      await addScene(data);
      
      // 成功后重置表单并关闭对话框
      setFormData({ scene: "", prompt: "" });
      setOpen(false);
      onSceneCreated();
    } catch (err) {
      console.error("Failed to create scene:", err);
      setError(t("errorFailed"));
    } finally {
      setLoading(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除错误信息
    if (error) setError(null);
  };

  // 重置表单
  const resetForm = () => {
    setFormData({ scene: "", prompt: "" });
    setError(null);
  };

  return (
    <Dialog open={open} onOpenChange={(newOpen) => {
      setOpen(newOpen);
      if (!newOpen) {
        resetForm();
      }
    }}>
      <DialogTrigger asChild>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          {tScenes("addScene")}
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t("title")}</DialogTitle>
          <DialogDescription>
            {t("description")}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 场景标题 */}
          <div className="space-y-2">
            <Label htmlFor="scene">{t("sceneLabel")} {t("required")}</Label>
            <Input
              id="scene"
              placeholder={t("scenePlaceholder")}
              value={formData.scene}
              onChange={(e) => handleInputChange("scene", e.target.value)}
              disabled={loading}
            />
          </div>

          {/* 场景提示词 */}
          <div className="space-y-2">
            <Label htmlFor="prompt">{t("promptLabel")} {t("required")}</Label>
            <Textarea
              id="prompt"
              placeholder={t("promptPlaceholder")}
              value={formData.prompt}
              onChange={(e) => handleInputChange("prompt", e.target.value)}
              rows={4}
              disabled={loading}
              className="resize-none"
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              {t("cancel")}
            </Button>
            <Button type="submit" disabled={loading || !formData.scene.trim() || !formData.prompt.trim()}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  {t("creating")}
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  {t("create")}
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
