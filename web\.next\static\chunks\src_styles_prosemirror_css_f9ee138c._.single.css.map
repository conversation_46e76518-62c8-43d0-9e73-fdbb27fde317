{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/prosemirror.css"], "sourcesContent": [".prose {\r\n  max-width: inherit;\r\n}\r\n\r\n.prose.inline-editor * {\r\n  margin: 0;\r\n}\r\n\r\n.prose.inline-editor .is-empty {\r\n  display: none;\r\n}\r\n\r\n.prose.inline-editor .is-empty.placeholder {\r\n  display: block;\r\n  opacity: 0.65;\r\n  font-size: 14px;\r\n}\r\n\r\n.ProseMirror {\r\n  line-height: 1.75;\r\n}\r\n\r\n.ProseMirror .is-editor-empty:first-child::before {\r\n  content: attr(data-placeholder);\r\n  float: left;\r\n  color: hsl(var(--muted-foreground));\r\n  pointer-events: none;\r\n  height: 0;\r\n}\r\n\r\n.ProseMirror p.is-empty::before {\r\n  content: attr(data-placeholder);\r\n  float: left;\r\n  color: hsl(var(--muted-foreground));\r\n  pointer-events: none;\r\n  height: 0;\r\n}\r\n\r\n.ProseMirror .mention {\r\n  background-color: var(--purple-light);\r\n  border-radius: 0.4rem;\r\n  box-decoration-break: clone;\r\n  color: var(--brand);\r\n  padding: 0.1rem 0.3rem;\r\n}\r\n\r\n/* Custom image styles */\r\n\r\n.ProseMirror img {\r\n  transition: filter 0.1s ease-in-out;\r\n\r\n  &:hover {\r\n    cursor: pointer;\r\n    filter: brightness(90%);\r\n  }\r\n\r\n  &.ProseMirror-selectednode {\r\n    outline: 3px solid #5abbf7;\r\n    filter: brightness(90%);\r\n  }\r\n}\r\n\r\n.img-placeholder {\r\n  position: relative;\r\n\r\n  &:before {\r\n    content: \"\";\r\n    box-sizing: border-box;\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    width: 36px;\r\n    height: 36px;\r\n    border-radius: 50%;\r\n    border: 3px solid var(--novel-stone-200);\r\n    border-top-color: var(--novel-stone-800);\r\n    animation: spinning 0.6s linear infinite;\r\n  }\r\n}\r\n\r\n.ProseMirror pre {\r\n  background: #0d0d0d;\r\n  border-radius: 0.5rem;\r\n  color: #fff;\r\n  font-family: \"JetBrainsMono\", monospace;\r\n  padding: 0.75rem 1rem;\r\n\r\n  code {\r\n    background: none;\r\n    color: inherit;\r\n    font-size: 0.8rem;\r\n    padding: 0;\r\n  }\r\n\r\n  .hljs-comment,\r\n  .hljs-quote {\r\n    color: #616161;\r\n  }\r\n\r\n  .hljs-variable,\r\n  .hljs-template-variable,\r\n  .hljs-attribute,\r\n  .hljs-tag,\r\n  .hljs-name,\r\n  .hljs-regexp,\r\n  .hljs-link,\r\n  .hljs-name,\r\n  .hljs-selector-id,\r\n  .hljs-selector-class {\r\n    color: #f98181;\r\n  }\r\n\r\n  .hljs-number,\r\n  .hljs-meta,\r\n  .hljs-built_in,\r\n  .hljs-builtin-name,\r\n  .hljs-literal,\r\n  .hljs-type,\r\n  .hljs-params {\r\n    color: #fbbc88;\r\n  }\r\n\r\n  .hljs-string,\r\n  .hljs-symbol,\r\n  .hljs-bullet {\r\n    color: #b9f18d;\r\n  }\r\n\r\n  .hljs-title,\r\n  .hljs-section {\r\n    color: #faf594;\r\n  }\r\n\r\n  .hljs-keyword,\r\n  .hljs-selector-tag {\r\n    color: #70cff8;\r\n  }\r\n\r\n  .hljs-emphasis {\r\n    font-style: italic;\r\n  }\r\n\r\n  .hljs-strong {\r\n    font-weight: 700;\r\n  }\r\n}\r\n\r\n@keyframes spinning {\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* Custom TODO list checkboxes – shoutout to this awesome tutorial: https://moderncss.dev/pure-css-custom-checkbox-style/ */\r\n\r\nul[data-type=\"taskList\"] li > label {\r\n  margin-right: 0.2rem;\r\n  user-select: none;\r\n}\r\n\r\n@media screen and (max-width: 768px) {\r\n  ul[data-type=\"taskList\"] li > label {\r\n    margin-right: 0.5rem;\r\n  }\r\n}\r\n\r\nul[data-type=\"taskList\"] li > label input[type=\"checkbox\"] {\r\n  -webkit-appearance: none;\r\n  appearance: none;\r\n  background-color: hsl(var(--background));\r\n  margin: 0;\r\n  cursor: pointer;\r\n  width: 1.2em;\r\n  height: 1.2em;\r\n  position: relative;\r\n  top: 5px;\r\n  border: 2px solid hsl(var(--border));\r\n  margin-right: 0.3rem;\r\n  display: grid;\r\n  place-content: center;\r\n\r\n  &:hover {\r\n    background-color: hsl(var(--accent));\r\n  }\r\n\r\n  &:active {\r\n    background-color: hsl(var(--accent));\r\n  }\r\n\r\n  &::before {\r\n    content: \"\";\r\n    width: 0.65em;\r\n    height: 0.65em;\r\n    transform: scale(0);\r\n    transition: 120ms transform ease-in-out;\r\n    box-shadow: inset 1em 1em;\r\n    transform-origin: center;\r\n    clip-path: polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0%, 43% 62%);\r\n  }\r\n\r\n  &:checked::before {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\nul[data-type=\"taskList\"] li[data-checked=\"true\"] > div > p {\r\n  color: var(--muted-foreground);\r\n  text-decoration: line-through;\r\n  text-decoration-thickness: 2px;\r\n}\r\n\r\n/* Overwrite tippy-box original max-width */\r\n\r\n.tippy-box {\r\n  max-width: 400px !important;\r\n}\r\n\r\n.ProseMirror:not(.dragging) .ProseMirror-selectednode {\r\n  outline: none !important;\r\n  background-color: var(--novel-highlight-blue);\r\n  transition: background-color 0.2s;\r\n  box-shadow: none;\r\n}\r\n\r\n.drag-handle {\r\n  position: fixed;\r\n  opacity: 1;\r\n  transition: opacity ease-in 0.2s;\r\n  border-radius: 0.25rem;\r\n\r\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 10' style='fill: rgba(0, 0, 0, 0.5)'%3E%3Cpath d='M3,2 C2.44771525,2 2,1.55228475 2,1 C2,0.44771525 2.44771525,0 3,0 C3.55228475,0 4,0.44771525 4,1 C4,1.55228475 3.55228475,2 3,2 Z M3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 C3.55228475,4 4,4.44771525 4,5 C4,5.55228475 3.55228475,6 3,6 Z M3,10 C2.44771525,10 2,9.55228475 2,9 C2,8.44771525 2.44771525,8 3,8 C3.55228475,8 4,8.44771525 4,9 C4,9.55228475 3.55228475,10 3,10 Z M7,2 C6.44771525,2 6,1.55228475 6,1 C6,0.44771525 6.44771525,0 7,0 C7.55228475,0 8,0.44771525 8,1 C8,1.55228475 7.55228475,2 7,2 Z M7,6 C6.44771525,6 6,5.55228475 6,5 C6,4.44771525 6.44771525,4 7,4 C7.55228475,4 8,4.44771525 8,5 C8,5.55228475 7.55228475,6 7,6 Z M7,10 C6.44771525,10 6,9.55228475 6,9 C6,8.44771525 6.44771525,8 7,8 C7.55228475,8 8,8.44771525 8,9 C8,9.55228475 7.55228475,10 7,10 Z'%3E%3C/path%3E%3C/svg%3E\");\r\n  background-size: calc(0.5em + 0.375rem) calc(0.5em + 0.375rem);\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  width: 1.2rem;\r\n  height: 1.5rem;\r\n  z-index: 50;\r\n  cursor: grab;\r\n\r\n  &:hover {\r\n    background-color: var(--novel-stone-100);\r\n    transition: background-color 0.2s;\r\n  }\r\n\r\n  &:active {\r\n    background-color: var(--novel-stone-200);\r\n    transition: background-color 0.2s;\r\n    cursor: grabbing;\r\n  }\r\n\r\n  &.hide {\r\n    opacity: 0;\r\n    pointer-events: none;\r\n  }\r\n\r\n  @media screen and (max-width: 600px) {\r\n    display: none;\r\n    pointer-events: none;\r\n  }\r\n}\r\n\r\n.dark .drag-handle {\r\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 10' style='fill: rgba(255, 255, 255, 0.5)'%3E%3Cpath d='M3,2 C2.44771525,2 2,1.55228475 2,1 C2,0.44771525 2.44771525,0 3,0 C3.55228475,0 4,0.44771525 4,1 C4,1.55228475 3.55228475,2 3,2 Z M3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 C3.55228475,4 4,4.44771525 4,5 C4,5.55228475 3.55228475,6 3,6 Z M3,10 C2.44771525,10 2,9.55228475 2,9 C2,8.44771525 2.44771525,8 3,8 C3.55228475,8 4,8.44771525 4,9 C4,9.55228475 3.55228475,10 3,10 Z M7,2 C6.44771525,2 6,1.55228475 6,1 C6,0.44771525 6.44771525,0 7,0 C7.55228475,0 8,0.44771525 8,1 C8,1.55228475 7.55228475,2 7,2 Z M7,6 C6.44771525,6 6,5.55228475 6,5 C6,4.44771525 6.44771525,4 7,4 C7.55228475,4 8,4.44771525 8,5 C8,5.55228475 7.55228475,6 7,6 Z M7,10 C6.44771525,10 6,9.55228475 6,9 C6,8.44771525 6.44771525,8 7,8 C7.55228475,8 8,8.44771525 8,9 C8,9.55228475 7.55228475,10 7,10 Z'%3E%3C/path%3E%3C/svg%3E\");\r\n}\r\n\r\n/* Custom Youtube Video CSS */\r\niframe {\r\n  border: 8px solid #ffd00027;\r\n  border-radius: 4px;\r\n  min-width: 200px;\r\n  min-height: 200px;\r\n  display: block;\r\n  outline: 0px solid transparent;\r\n}\r\n\r\ndiv[data-youtube-video] > iframe {\r\n  cursor: move;\r\n  aspect-ratio: 16 / 9;\r\n  width: 100%;\r\n}\r\n\r\n.ProseMirror-selectednode iframe {\r\n  transition: outline 0.15s;\r\n  outline: 6px solid #fbbf24;\r\n}\r\n\r\n@media only screen and (max-width: 480px) {\r\n  div[data-youtube-video] > iframe {\r\n    max-height: 50px;\r\n  }\r\n}\r\n\r\n@media only screen and (max-width: 720px) {\r\n  div[data-youtube-video] > iframe {\r\n    max-height: 100px;\r\n  }\r\n}\r\n\r\n/* CSS for bold coloring and highlighting issue*/\r\nspan[style] > strong {\r\n  color: inherit;\r\n}\r\n\r\nmark[style] > strong {\r\n  color: inherit;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;;;AAgBA;;;;;;;;AAUA;;;;AAGE;;;;;AAKA;;;;;AAMF;;;;AAGE;;;;;;;;;;;;;;AAeF;;;;;;;;AAOE;;;;;;;AAOA;;;;AAKA;;;;AAaA;;;;AAUA;;;;AAMA;;;;AAKA;;;;AAKA;;;;AAIA;;;;AAKF;;;;;;AAQA;;;;;AAKA;EACE;;;;;AAKF;;;;;;;;;;;;;;;AAeE;;;;AAQA;;;;;;;;;;;AAWA;;;;AAKF;;;;;AAQA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;;;;;;AAeE;;;;;AAKA;;;;;;AAMA;;;;;AAKA;EAAsC;;;;;;AAMxC;;;;AAKA;;;;;;;;;AASA;;;;;;AAMA;;;;;AAKA;EACE;;;;;AAKF;EACE;;;;;AAMF"}}]}