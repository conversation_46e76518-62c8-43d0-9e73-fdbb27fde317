#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\dataset\video\video-flow\web\node_modules\.pnpm\next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1\node_modules\next\dist\bin\node_modules;D:\dataset\video\video-flow\web\node_modules\.pnpm\next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1\node_modules\next\dist\node_modules;D:\dataset\video\video-flow\web\node_modules\.pnpm\next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1\node_modules\next\node_modules;D:\dataset\video\video-flow\web\node_modules\.pnpm\next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1\node_modules;D:\dataset\video\video-flow\web\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/dataset/video/video-flow/web/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/bin/node_modules:/mnt/d/dataset/video/video-flow/web/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/node_modules:/mnt/d/dataset/video/video-flow/web/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/node_modules:/mnt/d/dataset/video/video-flow/web/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules:/mnt/d/dataset/video/video-flow/web/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../next/dist/bin/next" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../next/dist/bin/next" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../next/dist/bin/next" $args
  } else {
    & "node$exe"  "$basedir/../next/dist/bin/next" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
