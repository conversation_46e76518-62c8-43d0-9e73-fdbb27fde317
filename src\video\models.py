"""
数据库模型定义
"""
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, TIMESTAMP, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base


class VideoCreative(Base):
    """视频创意表"""
    __tablename__ = "video_creative"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID，自增")
    creative = Column(Text, nullable=False, comment="创意描述")
    art_style = Column(String(128), comment="艺术风格（如：写实、卡通等）")
    scene_cnt = Column(Integer, nullable=False, default=5, comment="场景数量")
    character_prompt = Column(Text, nullable=False, comment="主角形象描述提示词")
    character_image = Column(String(256), comment="主角的图片地址")

    update_time = Column(
        TIMESTAMP,
        default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="记录创建或更新的时间"
    )

    # 关联关系
    scenes = relationship("VideoScene", back_populates="creative", cascade="all, delete-orphan")


class VideoScene(Base):
    """视频场景表"""
    __tablename__ = "video_scenes"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID，自增")
    creative_id = Column(Integer, ForeignKey("video_creative.id", ondelete="CASCADE"), nullable=False, comment="关联的创意ID")
    scene = Column(Text, nullable=False, comment="场景描述或名称")
    prompt = Column(Text, nullable=False, comment="文生图的提示词")
    image = Column(String(256), comment="生成的图片存储路径或URL")
    update_time = Column(
        TIMESTAMP,
        default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="记录创建或更新的时间"
    )

    # 关联关系
    creative = relationship("VideoCreative", back_populates="scenes")
    videos = relationship("VideoVideo", back_populates="scene", cascade="all, delete-orphan")


class VideoVideo(Base):
    """视频表"""
    __tablename__ = "video_videos"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID，自增")
    scene_id = Column(Integer, ForeignKey("video_scenes.id", ondelete="CASCADE"), nullable=False, comment="关联的场景ID")
    prompt = Column(Text, comment="图生视频的提示词")
    video = Column(String(512), comment="生成的视频存储路径或URL")
    update_time = Column(
        TIMESTAMP,
        default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="记录创建或更新的时间"
    )

    # 关联关系
    scene = relationship("VideoScene", back_populates="videos")
    subtitles = relationship("VideoSubtitle", back_populates="video", cascade="all, delete-orphan")


class VideoSubtitle(Base):
    """视频字幕表"""
    __tablename__ = "video_subtitles"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID，自增")
    video_id = Column(Integer, ForeignKey("video_videos.id", ondelete="CASCADE"), nullable=False, comment="关联的视频ID")
    subtitle = Column(Text, comment="字幕文本内容")
    audio = Column(String(512), comment="对应字幕的音频文件存储路径或URL")
    update_time = Column(
        TIMESTAMP,
        default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="记录创建或更新的时间"
    )

    # 关联关系
    video = relationship("VideoVideo", back_populates="subtitles")
