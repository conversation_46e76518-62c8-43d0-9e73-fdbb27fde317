# 场景卡片重新生图按钮功能实现总结

## 功能概述

为每个场景卡片添加了重新生图按钮，用户可以直接在场景列表中重新生成场景图片，无需切换到图片标签页。

## 实现内容

### 1. 场景卡片头部按钮区域更新 ✅

#### 添加重新生图按钮
在场景卡片头部的按钮区域添加了重新生图按钮，位置在编辑和删除按钮之前：

```tsx
<div className="flex items-center gap-2">
  <Badge variant="outline" className="text-xs">
    {formatDate(scene.update_time)}
  </Badge>
  
  {/* 重新生图按钮 */}
  <Button
    variant="ghost"
    size="sm"
    onClick={() => handleRegenerateSceneImage(scene.id)}
    disabled={regeneratingScenes.has(scene.id)}
    title={scene.image ? "重新生成图片" : "生成图片"}
  >
    {regeneratingScenes.has(scene.id) ? (
      <Loader2 className="h-3 w-3 animate-spin" />
    ) : (
      <Camera className="h-3 w-3" />
    )}
  </Button>
  
  {/* 编辑按钮 */}
  <Button variant="ghost" size="sm" onClick={...}>
    {isEditing ? <X className="h-3 w-3" /> : <Edit2 className="h-3 w-3" />}
  </Button>

  {/* 删除按钮 */}
  <Button variant="ghost" size="sm" onClick={...}>
    <Trash2 className="h-3 w-3" />
  </Button>
</div>
```

#### 按钮特性
- **图标**：使用 Camera 图标表示图片生成功能
- **状态**：生成中时显示 Loader2 动画图标
- **禁用**：生成中时按钮禁用，防止重复点击
- **提示**：有 tooltip 提示，根据是否已有图片显示不同文字
- **样式**：与其他按钮保持一致的 ghost 样式

### 2. 场景图片预览区域 ✅

#### 图片显示
在场景卡片内容区域添加了图片预览功能：

```tsx
{/* 场景图片预览 */}
{scene.image && (
  <div className="mt-3">
    <label className="text-xs font-medium text-muted-foreground">Generated Image</label>
    <div className="mt-1 relative">
      <img 
        src={scene.image} 
        alt={`Scene ${scene.id} image`}
        className="w-full h-32 object-cover rounded-md border"
      />
      {regeneratingScenes.has(scene.id) && (
        <div className="absolute inset-0 bg-black/50 rounded-md flex items-center justify-center">
          <div className="text-white text-xs flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            重新生成中...
          </div>
        </div>
      )}
    </div>
  </div>
)}
```

#### 图片特性
- **尺寸**：宽度100%，高度32（128px）
- **样式**：圆角边框，object-cover 保持比例
- **遮罩**：生成中时显示半透明黑色遮罩
- **提示**：生成中时显示"重新生成中..."文字和动画

### 3. 进度跟踪集成 ✅

#### 进度条显示
在图片预览下方添加了进度条：

```tsx
{/* 重新生成图片进度条 */}
{regeneratingScenes.has(scene.id) && regenerationTaskIds[scene.id] && (
  <div className="mt-3">
    <ProgressBar
      taskId={regenerationTaskIds[scene.id] || null}
      onComplete={() => {
        setRegeneratingScenes(prev => {
          const newSet = new Set(prev);
          newSet.delete(scene.id);
          return newSet;
        });
        setRegenerationTaskIds(prev => {
          const newIds = { ...prev };
          delete newIds[scene.id];
          return newIds;
        });
        loadData(); // 刷新数据
      }}
      onError={() => {
        setRegeneratingScenes(prev => {
          const newSet = new Set(prev);
          newSet.delete(scene.id);
          return newSet;
        });
        setRegenerationTaskIds(prev => {
          const newIds = { ...prev };
          delete newIds[scene.id];
          return newIds;
        });
      }}
    />
  </div>
)}
```

#### 进度特性
- **实时更新**：显示任务执行进度
- **自动清理**：完成或错误时自动清理状态
- **数据刷新**：完成时自动刷新场景数据
- **错误处理**：错误时清理状态，不影响其他操作

### 4. 状态管理复用 ✅

#### 复用现有状态
重新生图功能复用了已有的状态管理：

```tsx
// 重新生成状态（已存在）
const [regeneratingScenes, setRegeneratingScenes] = useState<Set<number>>(new Set());
const [regenerationTaskIds, setRegenerationTaskIds] = useState<{[key: number]: string}>({});

// 重新生成函数（已存在）
const handleRegenerateSceneImage = async (sceneId: number) => {
  const confirmAction = () => {
    closeConfirmDialog();
    performRegenerateSceneImage(sceneId);
  };

  showConfirmDialog(
    "重新生成图片",
    "确定要重新生成这个场景的图片吗？这将替换现有的图片。",
    confirmAction
  );
};
```

#### 状态特性
- **多场景支持**：支持多个场景同时重新生成
- **状态隔离**：每个场景的状态独立管理
- **确认对话框**：操作前显示确认对话框
- **API 集成**：使用现有的 regenerateSceneImage API

## UI 布局设计

### 场景卡片头部布局
```
┌─────────────────────────────────────────────────────┐
│ Scene #1                              [2024-01-15]  │
│                              [📷] [✏️] [🗑️]        │
└─────────────────────────────────────────────────────┘
```

### 场景卡片完整布局
```
┌─────────────────────────────────────────────────────┐
│ Scene #1                              [2024-01-15]  │
│                              [📷] [✏️] [🗑️]        │
├─────────────────────────────────────────────────────┤
│ Scene Title: 魔法森林中的神秘小屋                    │
│ Prompt: 一个被藤蔓覆盖的古老小屋...                 │
│                                                     │
│ Generated Image:                                    │
│ ┌─────────────────────────────────────────────────┐ │
│ │                                                 │ │
│ │           [场景图片预览]                         │ │
│ │                                                 │ │
│ └─────────────────────────────────────────────────┘ │
│                                                     │
│ [████████████████████████████████████] 85%         │ │
│ 正在重新生成图片...                                  │
│                                                     │
│ 📷1 image  🎬0 videos  📝0 subtitles               │
└─────────────────────────────────────────────────────┘
```

## 用户交互流程

### 1. 查看场景
1. **进入创意详情页面**
2. **切换到 Scenes 标签页**
3. **查看场景列表**：每个场景卡片显示基本信息

### 2. 重新生成图片
1. **点击重新生图按钮**：场景卡片头部的 Camera 图标
2. **确认操作**：显示确认对话框
3. **开始生成**：
   - 按钮变为动画图标并禁用
   - 如果已有图片，显示遮罩效果
   - 显示进度条
4. **查看进度**：实时显示生成进度
5. **完成生成**：
   - 自动刷新数据
   - 显示新生成的图片
   - 清理生成状态

### 3. 多场景操作
1. **同时操作多个场景**：可以同时为多个场景重新生成图片
2. **独立状态管理**：每个场景的生成状态独立
3. **不影响其他操作**：生成过程中可以进行其他操作

## 技术实现

### 1. 组件结构
```tsx
// 场景卡片组件结构
<Card key={scene.id}>
  <CardHeader>
    {/* 标题和按钮区域 */}
    <div className="flex items-start justify-between">
      <CardTitle>Scene #{scene.id}</CardTitle>
      <div className="flex items-center gap-2">
        <Badge>{formatDate(scene.update_time)}</Badge>
        <Button onClick={handleRegenerateSceneImage}>📷</Button>  {/* 新增 */}
        <Button onClick={handleEdit}>✏️</Button>
        <Button onClick={handleDelete}>🗑️</Button>
      </div>
    </div>
  </CardHeader>
  
  <CardContent>
    {/* 场景信息 */}
    <div>Scene Title & Prompt</div>
    
    {/* 图片预览 */}                                    {/* 新增 */}
    {scene.image && (
      <div>
        <img src={scene.image} />
        {regenerating && <div>重新生成中...</div>}
      </div>
    )}
    
    {/* 进度条 */}                                      {/* 新增 */}
    {regenerating && <ProgressBar />}
    
    {/* 统计信息 */}
    <div>📷1 image 🎬0 videos 📝0 subtitles</div>
  </CardContent>
</Card>
```

### 2. 状态管理
```tsx
// 重新生成状态
const [regeneratingScenes, setRegeneratingScenes] = useState<Set<number>>(new Set());
const [regenerationTaskIds, setRegenerationTaskIds] = useState<{[key: number]: string}>({});

// 按钮状态
const isRegenerating = regeneratingScenes.has(scene.id);
const taskId = regenerationTaskIds[scene.id];

// 状态更新
const startRegeneration = (sceneId: number, taskId: string) => {
  setRegeneratingScenes(prev => new Set(prev).add(sceneId));
  setRegenerationTaskIds(prev => ({ ...prev, [sceneId]: taskId }));
};

const completeRegeneration = (sceneId: number) => {
  setRegeneratingScenes(prev => {
    const newSet = new Set(prev);
    newSet.delete(sceneId);
    return newSet;
  });
  setRegenerationTaskIds(prev => {
    const newIds = { ...prev };
    delete newIds[sceneId];
    return newIds;
  });
};
```

### 3. API 集成
```tsx
// API 调用
const { regenerateSceneImage } = useVideoAPI();

const performRegenerateSceneImage = async (sceneId: number) => {
  try {
    setRegeneratingScenes(prev => new Set(prev).add(sceneId));
    const result = await regenerateSceneImage(sceneId);
    
    if (result.task_id) {
      setRegenerationTaskIds(prev => ({
        ...prev,
        [sceneId]: result.task_id
      }));
    }
  } catch (error) {
    // 错误处理
    setRegeneratingScenes(prev => {
      const newSet = new Set(prev);
      newSet.delete(sceneId);
      return newSet;
    });
  }
};
```

## 测试验证

### 测试脚本
提供了 `test_scene_regenerate_button.py` 脚本，测试：
- 创建创意和生成场景
- 生成初始图片
- 重新生成场景图片
- 前端集成功能
- UI 布局和交互

### 运行测试
```bash
python test_scene_regenerate_button.py
```

### 手动测试步骤
1. **创建创意并生成场景**
2. **生成场景图片**
3. **在 Scenes 标签页查看场景卡片**
4. **点击重新生图按钮**
5. **确认操作**
6. **观察生成过程**：
   - 按钮状态变化
   - 图片遮罩效果
   - 进度条显示
7. **验证结果**：图片更新成功

## 功能特性

### ✅ 用户体验优化
- **便捷操作**：无需切换标签页即可重新生图
- **直观反馈**：清晰的视觉状态反馈
- **进度跟踪**：实时显示生成进度
- **错误处理**：完善的错误处理机制

### ✅ 界面设计
- **一致性**：按钮样式与现有设计保持一致
- **可访问性**：有 tooltip 提示和键盘支持
- **响应式**：适配不同屏幕尺寸
- **性能**：状态管理高效，不影响其他功能

### ✅ 技术实现
- **代码复用**：复用现有的重新生成逻辑
- **状态管理**：清晰的状态管理和生命周期
- **API 集成**：与后端 API 完美集成
- **类型安全**：完整的 TypeScript 类型支持

## 总结

✅ **实现完成**：
- 在每个场景卡片添加重新生图按钮
- 集成图片预览和进度跟踪
- 复用现有的状态管理和 API
- 提供良好的用户体验

🎯 **用户价值**：
- 提高操作效率，无需切换标签页
- 直观的视觉反馈和进度显示
- 支持多场景同时操作
- 完整的错误处理和状态管理

🔧 **技术优势**：
- 代码复用，减少重复开发
- 一致的设计语言和交互模式
- 高效的状态管理
- 良好的可维护性和扩展性

现在用户可以直接在场景列表中重新生成任何场景的图片，大大提升了操作便利性！🚀
