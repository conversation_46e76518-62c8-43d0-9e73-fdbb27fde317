import { useCallback } from "react";
import { env } from "~/env";
import type { Creative, Scene, VideoItem, Subtitle } from "../components/workflow-main"
// API 基础 URL
const getApiUrl = () => {
  return env.NEXT_PUBLIC_API_URL || "http://localhost:8000/api";
};

// API 响应类型
interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

interface CreativeCreate {
  creative: string;
  art_style?: string;
  scene_cnt?: number;
  character_prompt: string;
}

interface CreativeUpdate {
  creative?: string;
  art_style?: string;
  scene_cnt?: number;
  character_prompt?: string;
}

interface GenerationTask {
  task_id: string;
  status: "pending" | "running" | "completed" | "failed";
  progress: number;
  message?: string;
  result?: any;
}


interface SceneCreate {
  creative_id: number;
  scene: string;
  prompt: string;
}

export function useVideoAPI() {
  // 通用的 fetch 函数 - GET 请求
  const apiRequest = useCallback(async <T>(endpoint: string): Promise<T> => {
    const url = `${getApiUrl()}${endpoint}`;

    try {
      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }, []);

  // 通用的 POST 请求函数
  const apiPostRequest = useCallback(async <T>(endpoint: string, data: any): Promise<T> => {
    const url = `${getApiUrl()}${endpoint}`;

    try {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API POST request failed for ${endpoint}:`, error);
      throw error;
    }
  }, []);

  // 通用的 PUT 请求函数
  const apiPutRequest = useCallback(async <T>(endpoint: string, data: any): Promise<T> => {
    const url = `${getApiUrl()}${endpoint}`;

    try {
      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API PUT request failed for ${endpoint}:`, error);
      throw error;
    }
  }, []);

    // 通用的 DELETE 请求函数
  const apiDeleteRequest = useCallback(async <T>(endpoint: string, data: any): Promise<T> => {
    const url = `${getApiUrl()}${endpoint}`;

    try {
      const response = await fetch(url, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API DELETE request failed for ${endpoint}:`, error);
      throw error;
    }
  }, []);

  // 更新视频
  const updateVideo = useCallback(
    async (id: number, data: { prompt?: string; video?: string }): Promise<VideoItem> => {
      return apiPutRequest(`/video/videos/${id}`, data);
    },
    [apiPutRequest]
  );

  // 重新生成单个视频
  const regenerateVideo = useCallback(
    async (videoId: number): Promise<GenerationTask> => {
      return apiPostRequest(`/video/generate/videos/video/${videoId}`, {});
    },
    [apiPostRequest]
  );

  // 更新字幕
  const updateSubtitle = useCallback(
    async (id: number, data: { subtitle?: string; audio?: string }): Promise<Subtitle> => {
      return apiPutRequest(`/video/subtitles/${id}`, data);
    },
    [apiPutRequest]
  );

  // 生成主角图片
  const generateCharacterImage = useCallback(
    async (creativeId: number): Promise<GenerationTask> => {
      return apiPostRequest(`/video/generate/character-image/${creativeId}`, {});
    },
    [apiPostRequest]
  );

  // 重新生成音频
  const regenerateAudio = useCallback(
    async (subtitleId: number): Promise<GenerationTask> => {
      return apiPostRequest(`/video/generate/audios/subtitle/${subtitleId}`, {});
    },
    [apiPostRequest]
  );

  // 获取创意列表
  const fetchCreatives = useCallback(
    async (page = 1, size = 20): Promise<PaginatedResponse<Creative>> => {
      return apiRequest(`/video/creatives?page=${page}&size=${size}`);
    },
    [apiRequest]
  );

  // 获取单个创意
  const fetchCreative = useCallback(
    async (id: number): Promise<Creative> => {
      return apiRequest(`/video/creatives/${id}`);
    },
    [apiRequest]
  );

  // 获取创意完整信息（包含场景、视频、字幕）
  const fetchCreativeComplete = useCallback(
    async (id: number): Promise<{
      creative: Creative;
      scenes: Scene[];
      videos: VideoItem[];
      subtitles: Subtitle[];
    }> => {
      return apiRequest(`/video/creatives/${id}/complete`);
    },
    [apiRequest]
  );

  // 创建新创意
  const createCreative = useCallback(
    async (data: CreativeCreate): Promise<Creative> => {
      return apiPostRequest(`/video/creatives`, data);
    },
    [apiPostRequest]
  );

  // 更新创意
  const updateCreative = useCallback(
    async (id: number, data: CreativeUpdate): Promise<Creative> => {
      return apiPutRequest(`/video/creatives/${id}`, data);
    },
    [apiPutRequest]
  );

  // 获取创意的场景列表
  const fetchScenes = useCallback(
    async (creativeId: number, page = 1, size = 50): Promise<PaginatedResponse<Scene>> => {
      return apiRequest(`/video/scenes?creative_id=${creativeId}&page=${page}&size=${size}`);
    },
    [apiRequest]
  );

  // 更新场景
  const updateScene = useCallback(
    async (id: number, data: { scene?: string; prompt?: string }): Promise<Scene> => {
      return apiPutRequest(`/video/scenes/${id}`, data);
    },
    [apiPutRequest]
  );

  // 添加场景
  const addScene = useCallback(
    async (data: SceneCreate): Promise<Scene> => {
      return apiPostRequest(`/video/scenes`, data);
    },
    [apiPostRequest]
  );

  // 删除场景
  const deleteScene = useCallback(
    async (id: number): Promise<void> => {
      return apiDeleteRequest(`/video/scenes/${id}`, {});
    },
    [apiDeleteRequest]
  );

  // 获取视频列表
  const fetchVideos = useCallback(
    async (creativeId?: number, sceneId?: number, page = 1, size = 50): Promise<PaginatedResponse<VideoItem>> => {
      const params = new URLSearchParams();
      if (creativeId) params.append('creative_id', creativeId.toString());
      if (sceneId) params.append('scene_id', sceneId.toString());
      params.append('page', page.toString());
      params.append('size', size.toString());
      return apiRequest(`/video/videos?${params.toString()}`);
    },
    [apiRequest]
  );

  // 获取字幕列表
  const fetchSubtitles = useCallback(
    async (creativeId?: number, videoId?: number, page = 1, size = 50): Promise<PaginatedResponse<Subtitle>> => {
      const params = new URLSearchParams();
      if (creativeId) params.append('creative_id', creativeId.toString());
      if (videoId) params.append('video_id', videoId.toString());
      params.append('page', page.toString());
      params.append('size', size.toString());
      return apiRequest(`/video/subtitles?${params.toString()}`);
    },
    [apiRequest]
  );

  // 生成场景
  const generateScenes = useCallback(
    async (creativeId: number): Promise<GenerationTask> => {
      return apiPostRequest(`/video/generate/scenes`, { creative_id: creativeId });
    },
    [apiPostRequest]
  );

  // 生成图片
  const generateImages = useCallback(
    async (creativeId: number): Promise<GenerationTask> => {
      return apiPostRequest(`/video/generate/images`, { creative_id: creativeId });
    },
    [apiPostRequest]
  );

  // 重新生成单个场景的图片
  const regenerateSceneImage = useCallback(
    async (sceneId: number): Promise<GenerationTask> => {
      return apiPostRequest(`/video/generate/images/scene/${sceneId}`, {});
    },
    [apiPostRequest]
  );

    // 重新生成单个场景的图片
  const regenerateSceneVideo = useCallback(
    async (sceneId: number): Promise<GenerationTask> => {
      return apiPostRequest(`/video/generate/images/video/${sceneId}`, {});
    },
    [apiPostRequest]
  );

    // 重新生成单个视频的字幕
  const regenerateVideoSubtitle = useCallback(
    async (videoId: number): Promise<GenerationTask> => {
      return apiPostRequest(`/video/generate/video/subtitle/${videoId}`, {});
    },
    [apiPostRequest]
  );
  
  // 生成视频
  const generateVideos = useCallback(
    async (creativeId: number): Promise<GenerationTask> => {
      return apiPostRequest(`/video/generate/videos`, { creative_id: creativeId });
    },
    [apiPostRequest]
  );

  // 生成字幕
  const generateSubtitles = useCallback(
    async (creativeId: number): Promise<GenerationTask> => {
      return apiPostRequest(`/video/generate/subtitles`, { creative_id: creativeId });
    },
    [apiPostRequest]
  );

  // 获取任务状态
  const getTaskStatus = useCallback(
    async (taskId: string): Promise<GenerationTask> => {
      return apiRequest(`/video/tasks/${taskId}`);
    },
    [apiRequest]
  );

  return {
    fetchCreatives,
    fetchCreative,
    fetchCreativeComplete,
    createCreative,
    updateCreative,
    fetchScenes,
    updateScene,
    addScene,
    deleteScene,
    fetchVideos,
    fetchSubtitles,
    generateScenes,
    generateImages,
    regenerateSceneImage,
    regenerateSceneVideo,
    generateVideos,
    generateSubtitles,
    getTaskStatus,
    updateVideo,
    regenerateVideo,
    updateSubtitle,
    generateCharacterImage,
    regenerateAudio,
    regenerateVideoSubtitle
  };
}
