{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/createChainableState.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/CommandManager.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/EventEmitter.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getExtensionField.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/splitExtensions.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getAttributesFromExtensions.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getNodeType.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/mergeAttributes.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getRenderedAttributes.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/isFunction.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/callOrReturn.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/isEmptyObject.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/fromString.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/injectExtensionAttributesToParseRule.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getSchemaByResolvedExtensions.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getSchemaTypeByName.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/isExtensionRulesEnabled.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getHTMLFromFragment.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getTextContentFromNodes.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/isRegExp.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/InputRule.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/isPlainObject.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/mergeDeep.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/Mark.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/isNumber.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/PasteRule.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/findDuplicates.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/ExtensionManager.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/Extension.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getTextBetween.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getTextSerializersFromSchema.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/extensions/clipboardTextSerializer.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/blur.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/clearContent.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/clearNodes.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/command.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/createParagraphNear.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/cut.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/deleteCurrentNode.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/deleteNode.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/deleteRange.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/deleteSelection.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/enter.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/exitCode.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/objectIncludes.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getMarkRange.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getMarkType.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/extendMarkRange.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/first.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/isTextSelection.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/minMax.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/resolveFocusPosition.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/isAndroid.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/isiOS.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/focus.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/forEach.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/insertContent.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/elementFromString.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/createNodeFromContent.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/selectionToInsertionEnd.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/insertContentAt.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/join.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/joinItemBackward.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/joinItemForward.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/joinTextblockBackward.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/joinTextblockForward.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/isMacOS.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/keyboardShortcut.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/isNodeActive.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/lift.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/liftEmptyBlock.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/liftListItem.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/newlineInCode.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getSchemaTypeNameByName.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/deleteProps.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/resetAttributes.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/scrollIntoView.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/selectAll.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/selectNodeBackward.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/selectNodeForward.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/selectParentNode.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/selectTextblockEnd.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/selectTextblockStart.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/createDocument.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/setContent.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getMarkAttributes.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/combineTransactionSteps.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/defaultBlockAt.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/findChildren.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/findChildrenInRange.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/findParentNodeClosestToPos.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/findParentNode.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getSchema.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/generateHTML.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/generateJSON.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getText.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/generateText.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getNodeAttributes.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getAttributes.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/removeDuplicates.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getChangedRanges.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getDebugJSON.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getMarksBetween.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getNodeAtPosition.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/getSplittedAttributes.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/isMarkActive.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/isActive.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/isAtEndOfNode.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/isAtStartOfNode.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/isList.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/isNodeEmpty.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/isNodeSelection.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/posToDOMRect.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/helpers/rewriteUnknownContent.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/setMark.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/setMeta.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/setNode.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/setNodeSelection.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/setTextSelection.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/sinkListItem.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/splitBlock.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/splitListItem.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/toggleList.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/toggleMark.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/toggleNode.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/toggleWrap.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/undoInputRule.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/unsetAllMarks.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/unsetMark.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/updateAttributes.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/wrapIn.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/commands/wrapInList.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/extensions/commands.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/extensions/drop.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/extensions/editable.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/extensions/focusEvents.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/extensions/keymap.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/extensions/paste.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/extensions/tabindex.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/NodePos.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/style.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/createStyleTag.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/Editor.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/inputRules/markInputRule.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/inputRules/nodeInputRule.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/inputRules/textblockTypeInputRule.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/inputRules/textInputRule.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/inputRules/wrappingInputRule.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/Node.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/NodeView.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/pasteRules/markPasteRule.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/escapeForRegEx.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/utilities/isString.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/pasteRules/nodePasteRule.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/pasteRules/textPasteRule.ts", "file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/%40tiptap%2Bcore%402.11.7_%40tiptap%2Bpm%402.11.7/node_modules/%40tiptap/core/src/Tracker.ts"], "sourcesContent": ["import { EditorState, Transaction } from '@tiptap/pm/state'\n\n/**\n * Takes a Transaction & Editor State and turns it into a chainable state object\n * @param config The transaction and state to create the chainable state from\n * @returns A chainable Editor state object\n */\nexport function createChainableState(config: {\n  transaction: Transaction\n  state: EditorState\n}): EditorState {\n  const { state, transaction } = config\n  let { selection } = transaction\n  let { doc } = transaction\n  let { storedMarks } = transaction\n\n  return {\n    ...state,\n    apply: state.apply.bind(state),\n    applyTransaction: state.applyTransaction.bind(state),\n    plugins: state.plugins,\n    schema: state.schema,\n    reconfigure: state.reconfigure.bind(state),\n    toJSON: state.toJSON.bind(state),\n    get storedMarks() {\n      return storedMarks\n    },\n    get selection() {\n      return selection\n    },\n    get doc() {\n      return doc\n    },\n    get tr() {\n      selection = transaction.selection\n      doc = transaction.doc\n      storedMarks = transaction.storedMarks\n\n      return transaction\n    },\n  }\n}\n", "import { EditorState, Transaction } from '@tiptap/pm/state'\n\nimport { Editor } from './Editor.js'\nimport { createChainableState } from './helpers/createChainableState.js'\nimport {\n  AnyCommands, CanCommands, ChainedCommands, CommandProps, SingleCommands,\n} from './types.js'\n\nexport class CommandManager {\n  editor: Editor\n\n  rawCommands: AnyCommands\n\n  customState?: EditorState\n\n  constructor(props: { editor: Editor; state?: EditorState }) {\n    this.editor = props.editor\n    this.rawCommands = this.editor.extensionManager.commands\n    this.customState = props.state\n  }\n\n  get hasCustomState(): boolean {\n    return !!this.customState\n  }\n\n  get state(): EditorState {\n    return this.customState || this.editor.state\n  }\n\n  get commands(): SingleCommands {\n    const { rawCommands, editor, state } = this\n    const { view } = editor\n    const { tr } = state\n    const props = this.buildProps(tr)\n\n    return Object.fromEntries(\n      Object.entries(rawCommands).map(([name, command]) => {\n        const method = (...args: any[]) => {\n          const callback = command(...args)(props)\n\n          if (!tr.getMeta('preventDispatch') && !this.hasCustomState) {\n            view.dispatch(tr)\n          }\n\n          return callback\n        }\n\n        return [name, method]\n      }),\n    ) as unknown as SingleCommands\n  }\n\n  get chain(): () => ChainedCommands {\n    return () => this.createChain()\n  }\n\n  get can(): () => CanCommands {\n    return () => this.createCan()\n  }\n\n  public createChain(startTr?: Transaction, shouldDispatch = true): ChainedCommands {\n    const { rawCommands, editor, state } = this\n    const { view } = editor\n    const callbacks: boolean[] = []\n    const hasStartTransaction = !!startTr\n    const tr = startTr || state.tr\n\n    const run = () => {\n      if (\n        !hasStartTransaction\n        && shouldDispatch\n        && !tr.getMeta('preventDispatch')\n        && !this.hasCustomState\n      ) {\n        view.dispatch(tr)\n      }\n\n      return callbacks.every(callback => callback === true)\n    }\n\n    const chain = {\n      ...Object.fromEntries(\n        Object.entries(rawCommands).map(([name, command]) => {\n          const chainedCommand = (...args: never[]) => {\n            const props = this.buildProps(tr, shouldDispatch)\n            const callback = command(...args)(props)\n\n            callbacks.push(callback)\n\n            return chain\n          }\n\n          return [name, chainedCommand]\n        }),\n      ),\n      run,\n    } as unknown as ChainedCommands\n\n    return chain\n  }\n\n  public createCan(startTr?: Transaction): CanCommands {\n    const { rawCommands, state } = this\n    const dispatch = false\n    const tr = startTr || state.tr\n    const props = this.buildProps(tr, dispatch)\n    const formattedCommands = Object.fromEntries(\n      Object.entries(rawCommands).map(([name, command]) => {\n        return [name, (...args: never[]) => command(...args)({ ...props, dispatch: undefined })]\n      }),\n    ) as unknown as SingleCommands\n\n    return {\n      ...formattedCommands,\n      chain: () => this.createChain(tr, dispatch),\n    } as CanCommands\n  }\n\n  public buildProps(tr: Transaction, shouldDispatch = true): CommandProps {\n    const { rawCommands, editor, state } = this\n    const { view } = editor\n\n    const props: CommandProps = {\n      tr,\n      editor,\n      view,\n      state: createChainableState({\n        state,\n        transaction: tr,\n      }),\n      dispatch: shouldDispatch ? () => undefined : undefined,\n      chain: () => this.createChain(tr, shouldDispatch),\n      can: () => this.createCan(tr),\n      get commands() {\n        return Object.fromEntries(\n          Object.entries(rawCommands).map(([name, command]) => {\n            return [name, (...args: never[]) => command(...args)(props)]\n          }),\n        ) as unknown as SingleCommands\n      },\n    }\n\n    return props\n  }\n}\n", "type StringKeyOf<T> = Extract<keyof T, string>\ntype CallbackType<\n  T extends Record<string, any>,\n  EventName extends StringKeyOf<T>,\n> = T[EventName] extends any[] ? T[EventName] : [T[EventName]]\ntype CallbackFunction<\n  T extends Record<string, any>,\n  EventName extends StringKeyOf<T>,\n> = (...props: CallbackType<T, EventName>) => any\n\nexport class EventEmitter<T extends Record<string, any>> {\n\n  private callbacks: { [key: string]: Array<(...args: any[])=>void> } = {}\n\n  public on<EventName extends StringKeyOf<T>>(event: EventName, fn: CallbackFunction<T, EventName>): this {\n    if (!this.callbacks[event]) {\n      this.callbacks[event] = []\n    }\n\n    this.callbacks[event].push(fn)\n\n    return this\n  }\n\n  public emit<EventName extends StringKeyOf<T>>(event: EventName, ...args: CallbackType<T, EventName>): this {\n    const callbacks = this.callbacks[event]\n\n    if (callbacks) {\n      callbacks.forEach(callback => callback.apply(this, args))\n    }\n\n    return this\n  }\n\n  public off<EventName extends StringKeyOf<T>>(event: EventName, fn?: CallbackFunction<T, EventName>): this {\n    const callbacks = this.callbacks[event]\n\n    if (callbacks) {\n      if (fn) {\n        this.callbacks[event] = callbacks.filter(callback => callback !== fn)\n      } else {\n        delete this.callbacks[event]\n      }\n    }\n\n    return this\n  }\n\n  public once<EventName extends StringKeyOf<T>>(event: EventName, fn: CallbackFunction<T, EventName>): this {\n    const onceFn = (...args: CallbackType<T, EventName>) => {\n      this.off(event, onceFn)\n      fn.apply(this, args)\n    }\n\n    return this.on(event, onceFn)\n  }\n\n  public removeAllListeners(): void {\n    this.callbacks = {}\n  }\n}\n", "import { AnyExtension, MaybeThisParameterType, RemoveThis } from '../types.js'\n\n/**\n * Returns a field from an extension\n * @param extension The Tiptap extension\n * @param field The field, for example `renderHTML` or `priority`\n * @param context The context object that should be passed as `this` into the function\n * @returns The field value\n */\nexport function getExtensionField<T = any>(\n  extension: AnyExtension,\n  field: string,\n  context?: Omit<MaybeThisParameterType<T>, 'parent'>,\n): RemoveThis<T> {\n\n  if (extension.config[field] === undefined && extension.parent) {\n    return getExtensionField(extension.parent, field, context)\n  }\n\n  if (typeof extension.config[field] === 'function') {\n    const value = extension.config[field].bind({\n      ...context,\n      parent: extension.parent\n        ? getExtensionField(extension.parent, field, context)\n        : null,\n    })\n\n    return value\n  }\n\n  return extension.config[field]\n}\n", "import { Extension } from '../Extension.js'\nimport { Mark } from '../Mark.js'\nimport { Node } from '../Node.js'\nimport { Extensions } from '../types.js'\n\nexport function splitExtensions(extensions: Extensions) {\n  const baseExtensions = extensions.filter(extension => extension.type === 'extension') as Extension[]\n  const nodeExtensions = extensions.filter(extension => extension.type === 'node') as Node[]\n  const markExtensions = extensions.filter(extension => extension.type === 'mark') as Mark[]\n\n  return {\n    baseExtensions,\n    nodeExtensions,\n    markExtensions,\n  }\n}\n", "import { MarkConfig, NodeConfig } from '../index.js'\nimport {\n  AnyConfig,\n  Attribute,\n  Attributes,\n  ExtensionAttribute,\n  Extensions,\n} from '../types.js'\nimport { getExtensionField } from './getExtensionField.js'\nimport { splitExtensions } from './splitExtensions.js'\n\n/**\n * Get a list of all extension attributes defined in `addAttribute` and `addGlobalAttribute`.\n * @param extensions List of extensions\n */\nexport function getAttributesFromExtensions(extensions: Extensions): ExtensionAttribute[] {\n  const extensionAttributes: ExtensionAttribute[] = []\n  const { nodeExtensions, markExtensions } = splitExtensions(extensions)\n  const nodeAndMarkExtensions = [...nodeExtensions, ...markExtensions]\n  const defaultAttribute: Required<Attribute> = {\n    default: null,\n    rendered: true,\n    renderHTML: null,\n    parseHTML: null,\n    keepOnSplit: true,\n    isRequired: false,\n  }\n\n  extensions.forEach(extension => {\n    const context = {\n      name: extension.name,\n      options: extension.options,\n      storage: extension.storage,\n      extensions: nodeAndMarkExtensions,\n    }\n\n    const addGlobalAttributes = getExtensionField<AnyConfig['addGlobalAttributes']>(\n      extension,\n      'addGlobalAttributes',\n      context,\n    )\n\n    if (!addGlobalAttributes) {\n      return\n    }\n\n    const globalAttributes = addGlobalAttributes()\n\n    globalAttributes.forEach(globalAttribute => {\n      globalAttribute.types.forEach(type => {\n        Object\n          .entries(globalAttribute.attributes)\n          .forEach(([name, attribute]) => {\n            extensionAttributes.push({\n              type,\n              name,\n              attribute: {\n                ...defaultAttribute,\n                ...attribute,\n              },\n            })\n          })\n      })\n    })\n  })\n\n  nodeAndMarkExtensions.forEach(extension => {\n    const context = {\n      name: extension.name,\n      options: extension.options,\n      storage: extension.storage,\n    }\n\n    const addAttributes = getExtensionField<NodeConfig['addAttributes'] | MarkConfig['addAttributes']>(\n      extension,\n      'addAttributes',\n      context,\n    )\n\n    if (!addAttributes) {\n      return\n    }\n\n    // TODO: remove `as Attributes`\n    const attributes = addAttributes() as Attributes\n\n    Object\n      .entries(attributes)\n      .forEach(([name, attribute]) => {\n        const mergedAttr = {\n          ...defaultAttribute,\n          ...attribute,\n        }\n\n        if (typeof mergedAttr?.default === 'function') {\n          mergedAttr.default = mergedAttr.default()\n        }\n\n        if (mergedAttr?.isRequired && mergedAttr?.default === undefined) {\n          delete mergedAttr.default\n        }\n\n        extensionAttributes.push({\n          type: extension.name,\n          name,\n          attribute: mergedAttr,\n        })\n      })\n  })\n\n  return extensionAttributes\n}\n", "import { NodeType, Schema } from '@tiptap/pm/model'\n\nexport function getNodeType(nameOrType: string | NodeType, schema: Schema): NodeType {\n  if (typeof nameOrType === 'string') {\n    if (!schema.nodes[nameOrType]) {\n      throw Error(\n        `There is no node type named '${nameOrType}'. Maybe you forgot to add the extension?`,\n      )\n    }\n\n    return schema.nodes[nameOrType]\n  }\n\n  return nameOrType\n}\n", "export function mergeAttributes(...objects: Record<string, any>[]): Record<string, any> {\n  return objects\n    .filter(item => !!item)\n    .reduce((items, item) => {\n      const mergedAttributes = { ...items }\n\n      Object.entries(item).forEach(([key, value]) => {\n        const exists = mergedAttributes[key]\n\n        if (!exists) {\n          mergedAttributes[key] = value\n\n          return\n        }\n\n        if (key === 'class') {\n          const valueClasses: string[] = value ? String(value).split(' ') : []\n          const existingClasses: string[] = mergedAttributes[key] ? mergedAttributes[key].split(' ') : []\n\n          const insertClasses = valueClasses.filter(\n            valueClass => !existingClasses.includes(valueClass),\n          )\n\n          mergedAttributes[key] = [...existingClasses, ...insertClasses].join(' ')\n        } else if (key === 'style') {\n          const newStyles: string[] = value ? value.split(';').map((style: string) => style.trim()).filter(Boolean) : []\n          const existingStyles: string[] = mergedAttributes[key] ? mergedAttributes[key].split(';').map((style: string) => style.trim()).filter(Boolean) : []\n\n          const styleMap = new Map<string, string>()\n\n          existingStyles.forEach(style => {\n            const [property, val] = style.split(':').map(part => part.trim())\n\n            styleMap.set(property, val)\n          })\n\n          newStyles.forEach(style => {\n            const [property, val] = style.split(':').map(part => part.trim())\n\n            styleMap.set(property, val)\n          })\n\n          mergedAttributes[key] = Array.from(styleMap.entries()).map(([property, val]) => `${property}: ${val}`).join('; ')\n        } else {\n          mergedAttributes[key] = value\n        }\n      })\n\n      return mergedAttributes\n    }, {})\n}\n", "import { Mark, Node } from '@tiptap/pm/model'\n\nimport { ExtensionAttribute } from '../types.js'\nimport { mergeAttributes } from '../utilities/mergeAttributes.js'\n\nexport function getRenderedAttributes(\n  nodeOrMark: Node | Mark,\n  extensionAttributes: ExtensionAttribute[],\n): Record<string, any> {\n  return extensionAttributes\n    .filter(\n      attribute => attribute.type === nodeOrMark.type.name,\n    )\n    .filter(item => item.attribute.rendered)\n    .map(item => {\n      if (!item.attribute.renderHTML) {\n        return {\n          [item.name]: nodeOrMark.attrs[item.name],\n        }\n      }\n\n      return item.attribute.renderHTML(nodeOrMark.attrs) || {}\n    })\n    .reduce((attributes, attribute) => mergeAttributes(attributes, attribute), {})\n}\n", "// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nexport function isFunction(value: any): value is Function {\n  return typeof value === 'function'\n}\n", "import { MaybeReturnType } from '../types.js'\nimport { isFunction } from './isFunction.js'\n\n/**\n * Optionally calls `value` as a function.\n * Otherwise it is returned directly.\n * @param value Function or any value.\n * @param context Optional context to bind to function.\n * @param props Optional props to pass to function.\n */\nexport function callOrReturn<T>(value: T, context: any = undefined, ...props: any[]): MaybeReturnType<T> {\n  if (isFunction(value)) {\n    if (context) {\n      return value.bind(context)(...props)\n    }\n\n    return value(...props)\n  }\n\n  return value as MaybeReturnType<T>\n}\n", "export function isEmptyObject(value = {}): boolean {\n  return Object.keys(value).length === 0 && value.constructor === Object\n}\n", "export function fromString(value: any): any {\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  if (value.match(/^[+-]?(?:\\d*\\.)?\\d+$/)) {\n    return Number(value)\n  }\n\n  if (value === 'true') {\n    return true\n  }\n\n  if (value === 'false') {\n    return false\n  }\n\n  return value\n}\n", "import { ParseRule } from '@tiptap/pm/model'\n\nimport { ExtensionAttribute } from '../types.js'\nimport { fromString } from '../utilities/fromString.js'\n\n/**\n * This function merges extension attributes into parserule attributes (`attrs` or `getAttrs`).\n * Cancels when `getAttrs` returned `false`.\n * @param parseRule ProseMirror ParseRule\n * @param extensionAttributes List of attributes to inject\n */\nexport function injectExtensionAttributesToParseRule(\n  parseRule: ParseRule,\n  extensionAttributes: ExtensionAttribute[],\n): ParseRule {\n  if ('style' in parseRule) {\n    return parseRule\n  }\n\n  return {\n    ...parseRule,\n    getAttrs: (node: HTMLElement) => {\n      const oldAttributes = parseRule.getAttrs ? parseRule.getAttrs(node) : parseRule.attrs\n\n      if (oldAttributes === false) {\n        return false\n      }\n\n      const newAttributes = extensionAttributes.reduce((items, item) => {\n        const value = item.attribute.parseHTML\n          ? item.attribute.parseHTML(node)\n          : fromString((node).getAttribute(item.name))\n\n        if (value === null || value === undefined) {\n          return items\n        }\n\n        return {\n          ...items,\n          [item.name]: value,\n        }\n      }, {})\n\n      return { ...oldAttributes, ...newAttributes }\n    },\n  }\n}\n", "import {\n  <PERSON><PERSON><PERSON>, <PERSON>de<PERSON><PERSON>, <PERSON>hem<PERSON>, TagParseRule,\n} from '@tiptap/pm/model'\n\nimport { Editor, MarkConfig, NodeConfig } from '../index.js'\nimport { AnyConfig, Extensions } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\nimport { isEmptyObject } from '../utilities/isEmptyObject.js'\nimport { getAttributesFromExtensions } from './getAttributesFromExtensions.js'\nimport { getExtensionField } from './getExtensionField.js'\nimport { getRenderedAttributes } from './getRenderedAttributes.js'\nimport { injectExtensionAttributesToParseRule } from './injectExtensionAttributesToParseRule.js'\nimport { splitExtensions } from './splitExtensions.js'\n\nfunction cleanUpSchemaItem<T>(data: T) {\n  return Object.fromEntries(\n    // @ts-ignore\n    Object.entries(data).filter(([key, value]) => {\n      if (key === 'attrs' && isEmptyObject(value as object | undefined)) {\n        return false\n      }\n\n      return value !== null && value !== undefined\n    }),\n  ) as T\n}\n\n/**\n * Creates a new Prosemirror schema based on the given extensions.\n * @param extensions An array of Tiptap extensions\n * @param editor The editor instance\n * @returns A Prosemirror schema\n */\nexport function getSchemaByResolvedExtensions(extensions: Extensions, editor?: Editor): Schema {\n  const allAttributes = getAttributesFromExtensions(extensions)\n  const { nodeExtensions, markExtensions } = splitExtensions(extensions)\n  const topNode = nodeExtensions.find(extension => getExtensionField(extension, 'topNode'))?.name\n\n  const nodes = Object.fromEntries(\n    nodeExtensions.map(extension => {\n      const extensionAttributes = allAttributes.filter(\n        attribute => attribute.type === extension.name,\n      )\n      const context = {\n        name: extension.name,\n        options: extension.options,\n        storage: extension.storage,\n        editor,\n      }\n\n      const extraNodeFields = extensions.reduce((fields, e) => {\n        const extendNodeSchema = getExtensionField<AnyConfig['extendNodeSchema']>(\n          e,\n          'extendNodeSchema',\n          context,\n        )\n\n        return {\n          ...fields,\n          ...(extendNodeSchema ? extendNodeSchema(extension) : {}),\n        }\n      }, {})\n\n      const schema: NodeSpec = cleanUpSchemaItem({\n        ...extraNodeFields,\n        content: callOrReturn(\n          getExtensionField<NodeConfig['content']>(extension, 'content', context),\n        ),\n        marks: callOrReturn(getExtensionField<NodeConfig['marks']>(extension, 'marks', context)),\n        group: callOrReturn(getExtensionField<NodeConfig['group']>(extension, 'group', context)),\n        inline: callOrReturn(getExtensionField<NodeConfig['inline']>(extension, 'inline', context)),\n        atom: callOrReturn(getExtensionField<NodeConfig['atom']>(extension, 'atom', context)),\n        selectable: callOrReturn(\n          getExtensionField<NodeConfig['selectable']>(extension, 'selectable', context),\n        ),\n        draggable: callOrReturn(\n          getExtensionField<NodeConfig['draggable']>(extension, 'draggable', context),\n        ),\n        code: callOrReturn(getExtensionField<NodeConfig['code']>(extension, 'code', context)),\n        whitespace: callOrReturn(getExtensionField<NodeConfig['whitespace']>(extension, 'whitespace', context)),\n        linebreakReplacement: callOrReturn(getExtensionField<NodeConfig['linebreakReplacement']>(extension, 'linebreakReplacement', context)),\n        defining: callOrReturn(\n          getExtensionField<NodeConfig['defining']>(extension, 'defining', context),\n        ),\n        isolating: callOrReturn(\n          getExtensionField<NodeConfig['isolating']>(extension, 'isolating', context),\n        ),\n        attrs: Object.fromEntries(\n          extensionAttributes.map(extensionAttribute => {\n            return [extensionAttribute.name, { default: extensionAttribute?.attribute?.default }]\n          }),\n        ),\n      })\n\n      const parseHTML = callOrReturn(\n        getExtensionField<NodeConfig['parseHTML']>(extension, 'parseHTML', context),\n      )\n\n      if (parseHTML) {\n        schema.parseDOM = parseHTML.map(parseRule => injectExtensionAttributesToParseRule(parseRule, extensionAttributes)) as TagParseRule[]\n      }\n\n      const renderHTML = getExtensionField<NodeConfig['renderHTML']>(\n        extension,\n        'renderHTML',\n        context,\n      )\n\n      if (renderHTML) {\n        schema.toDOM = node => renderHTML({\n          node,\n          HTMLAttributes: getRenderedAttributes(node, extensionAttributes),\n        })\n      }\n\n      const renderText = getExtensionField<NodeConfig['renderText']>(\n        extension,\n        'renderText',\n        context,\n      )\n\n      if (renderText) {\n        schema.toText = renderText\n      }\n\n      return [extension.name, schema]\n    }),\n  )\n\n  const marks = Object.fromEntries(\n    markExtensions.map(extension => {\n      const extensionAttributes = allAttributes.filter(\n        attribute => attribute.type === extension.name,\n      )\n      const context = {\n        name: extension.name,\n        options: extension.options,\n        storage: extension.storage,\n        editor,\n      }\n\n      const extraMarkFields = extensions.reduce((fields, e) => {\n        const extendMarkSchema = getExtensionField<AnyConfig['extendMarkSchema']>(\n          e,\n          'extendMarkSchema',\n          context,\n        )\n\n        return {\n          ...fields,\n          ...(extendMarkSchema ? extendMarkSchema(extension as any) : {}),\n        }\n      }, {})\n\n      const schema: MarkSpec = cleanUpSchemaItem({\n        ...extraMarkFields,\n        inclusive: callOrReturn(\n          getExtensionField<MarkConfig['inclusive']>(extension, 'inclusive', context),\n        ),\n        excludes: callOrReturn(\n          getExtensionField<MarkConfig['excludes']>(extension, 'excludes', context),\n        ),\n        group: callOrReturn(getExtensionField<MarkConfig['group']>(extension, 'group', context)),\n        spanning: callOrReturn(\n          getExtensionField<MarkConfig['spanning']>(extension, 'spanning', context),\n        ),\n        code: callOrReturn(getExtensionField<MarkConfig['code']>(extension, 'code', context)),\n        attrs: Object.fromEntries(\n          extensionAttributes.map(extensionAttribute => {\n            return [extensionAttribute.name, { default: extensionAttribute?.attribute?.default }]\n          }),\n        ),\n      })\n\n      const parseHTML = callOrReturn(\n        getExtensionField<MarkConfig['parseHTML']>(extension, 'parseHTML', context),\n      )\n\n      if (parseHTML) {\n        schema.parseDOM = parseHTML.map(parseRule => injectExtensionAttributesToParseRule(parseRule, extensionAttributes))\n      }\n\n      const renderHTML = getExtensionField<MarkConfig['renderHTML']>(\n        extension,\n        'renderHTML',\n        context,\n      )\n\n      if (renderHTML) {\n        schema.toDOM = mark => renderHTML({\n          mark,\n          HTMLAttributes: getRenderedAttributes(mark, extensionAttributes),\n        })\n      }\n\n      return [extension.name, schema]\n    }),\n  )\n\n  return new Schema({\n    topNode,\n    nodes,\n    marks,\n  })\n}\n", "import { MarkType, NodeType, Schema } from '@tiptap/pm/model'\n\n/**\n * Tries to get a node or mark type by its name.\n * @param name The name of the node or mark type\n * @param schema The Prosemiror schema to search in\n * @returns The node or mark type, or null if it doesn't exist\n */\nexport function getSchemaTypeByName(name: string, schema: Schema): NodeType | MarkType | null {\n  return schema.nodes[name] || schema.marks[name] || null\n}\n", "import { AnyExtension, EnableRules } from '../types.js'\n\nexport function isExtensionRulesEnabled(extension: AnyExtension, enabled: EnableRules): boolean {\n  if (Array.isArray(enabled)) {\n    return enabled.some(enabledExtension => {\n      const name = typeof enabledExtension === 'string'\n        ? enabledExtension\n        : enabledExtension.name\n\n      return name === extension.name\n    })\n  }\n\n  return enabled\n}\n", "import { DOMSerializer, Fragment, Schema } from '@tiptap/pm/model'\n\nexport function getHTMLFromFragment(fragment: Fragment, schema: Schema): string {\n  const documentFragment = DOMSerializer.fromSchema(schema).serializeFragment(fragment)\n\n  const temporaryDocument = document.implementation.createHTMLDocument()\n  const container = temporaryDocument.createElement('div')\n\n  container.appendChild(documentFragment)\n\n  return container.innerHTML\n}\n", "import { ResolvedPos } from '@tiptap/pm/model'\n\n/**\n * Returns the text content of a resolved prosemirror position\n * @param $from The resolved position to get the text content from\n * @param maxMatch The maximum number of characters to match\n * @returns The text content\n */\nexport const getTextContentFromNodes = ($from: ResolvedPos, maxMatch = 500) => {\n  let textBefore = ''\n\n  const sliceEndPos = $from.parentOffset\n\n  $from.parent.nodesBetween(\n    Math.max(0, sliceEndPos - maxMatch),\n    sliceEndPos,\n    (node, pos, parent, index) => {\n      const chunk = node.type.spec.toText?.({\n        node,\n        pos,\n        parent,\n        index,\n      })\n        || node.textContent\n        || '%leaf%'\n\n      textBefore += node.isAtom && !node.isText ? chunk : chunk.slice(0, Math.max(0, sliceEndPos - pos))\n    },\n  )\n\n  return textBefore\n}\n", "export function isRegExp(value: any): value is RegExp {\n  return Object.prototype.toString.call(value) === '[object RegExp]'\n}\n", "import { Fragment, Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { EditorState, Plugin, TextSelection } from '@tiptap/pm/state'\n\nimport { CommandManager } from './CommandManager.js'\nimport { Editor } from './Editor.js'\nimport { createChainableState } from './helpers/createChainableState.js'\nimport { getHTMLFromFragment } from './helpers/getHTMLFromFragment.js'\nimport { getTextContentFromNodes } from './helpers/getTextContentFromNodes.js'\nimport {\n  CanCommands,\n  ChainedCommands,\n  ExtendedRegExpMatchArray,\n  Range,\n  SingleCommands,\n} from './types.js'\nimport { isRegExp } from './utilities/isRegExp.js'\n\nexport type InputRuleMatch = {\n  index: number;\n  text: string;\n  replaceWith?: string;\n  match?: RegExpMatchArray;\n  data?: Record<string, any>;\n};\n\nexport type InputRuleFinder = RegExp | ((text: string) => InputRuleMatch | null);\n\nexport class InputRule {\n  find: InputRuleFinder\n\n  handler: (props: {\n    state: EditorState;\n    range: Range;\n    match: ExtendedRegExpMatchArray;\n    commands: SingleCommands;\n    chain: () => ChainedCommands;\n    can: () => CanCommands;\n  }) => void | null\n\n  constructor(config: {\n    find: InputRuleFinder;\n    handler: (props: {\n      state: EditorState;\n      range: Range;\n      match: ExtendedRegExpMatchArray;\n      commands: SingleCommands;\n      chain: () => ChainedCommands;\n      can: () => CanCommands;\n    }) => void | null;\n  }) {\n    this.find = config.find\n    this.handler = config.handler\n  }\n}\n\nconst inputRuleMatcherHandler = (\n  text: string,\n  find: InputRuleFinder,\n): ExtendedRegExpMatchArray | null => {\n  if (isRegExp(find)) {\n    return find.exec(text)\n  }\n\n  const inputRuleMatch = find(text)\n\n  if (!inputRuleMatch) {\n    return null\n  }\n\n  const result: ExtendedRegExpMatchArray = [inputRuleMatch.text]\n\n  result.index = inputRuleMatch.index\n  result.input = text\n  result.data = inputRuleMatch.data\n\n  if (inputRuleMatch.replaceWith) {\n    if (!inputRuleMatch.text.includes(inputRuleMatch.replaceWith)) {\n      console.warn(\n        '[tiptap warn]: \"inputRuleMatch.replaceWith\" must be part of \"inputRuleMatch.text\".',\n      )\n    }\n\n    result.push(inputRuleMatch.replaceWith)\n  }\n\n  return result\n}\n\nfunction run(config: {\n  editor: Editor;\n  from: number;\n  to: number;\n  text: string;\n  rules: InputRule[];\n  plugin: Plugin;\n}): boolean {\n  const {\n    editor, from, to, text, rules, plugin,\n  } = config\n  const { view } = editor\n\n  if (view.composing) {\n    return false\n  }\n\n  const $from = view.state.doc.resolve(from)\n\n  if (\n    // check for code node\n    $from.parent.type.spec.code\n    // check for code mark\n    || !!($from.nodeBefore || $from.nodeAfter)?.marks.find(mark => mark.type.spec.code)\n  ) {\n    return false\n  }\n\n  let matched = false\n\n  const textBefore = getTextContentFromNodes($from) + text\n\n  rules.forEach(rule => {\n    if (matched) {\n      return\n    }\n\n    const match = inputRuleMatcherHandler(textBefore, rule.find)\n\n    if (!match) {\n      return\n    }\n\n    const tr = view.state.tr\n    const state = createChainableState({\n      state: view.state,\n      transaction: tr,\n    })\n    const range = {\n      from: from - (match[0].length - text.length),\n      to,\n    }\n\n    const { commands, chain, can } = new CommandManager({\n      editor,\n      state,\n    })\n\n    const handler = rule.handler({\n      state,\n      range,\n      match,\n      commands,\n      chain,\n      can,\n    })\n\n    // stop if there are no changes\n    if (handler === null || !tr.steps.length) {\n      return\n    }\n\n    // store transform as meta data\n    // so we can undo input rules within the `undoInputRules` command\n    tr.setMeta(plugin, {\n      transform: tr,\n      from,\n      to,\n      text,\n    })\n\n    view.dispatch(tr)\n    matched = true\n  })\n\n  return matched\n}\n\n/**\n * Create an input rules plugin. When enabled, it will cause text\n * input that matches any of the given rules to trigger the rule’s\n * action.\n */\nexport function inputRulesPlugin(props: { editor: Editor; rules: InputRule[] }): Plugin {\n  const { editor, rules } = props\n  const plugin = new Plugin({\n    state: {\n      init() {\n        return null\n      },\n      apply(tr, prev, state) {\n        const stored = tr.getMeta(plugin)\n\n        if (stored) {\n          return stored\n        }\n\n        // if InputRule is triggered by insertContent()\n        const simulatedInputMeta = tr.getMeta('applyInputRules') as\n          | undefined\n          | {\n              from: number;\n              text: string | ProseMirrorNode | Fragment;\n            }\n        const isSimulatedInput = !!simulatedInputMeta\n\n        if (isSimulatedInput) {\n          setTimeout(() => {\n            let { text } = simulatedInputMeta\n\n            if (typeof text === 'string') {\n              text = text as string\n            } else {\n              text = getHTMLFromFragment(Fragment.from(text), state.schema)\n            }\n\n            const { from } = simulatedInputMeta\n            const to = from + text.length\n\n            run({\n              editor,\n              from,\n              to,\n              text,\n              rules,\n              plugin,\n            })\n          })\n        }\n\n        return tr.selectionSet || tr.docChanged ? null : prev\n      },\n    },\n\n    props: {\n      handleTextInput(view, from, to, text) {\n        return run({\n          editor,\n          from,\n          to,\n          text,\n          rules,\n          plugin,\n        })\n      },\n\n      handleDOMEvents: {\n        compositionend: view => {\n          setTimeout(() => {\n            const { $cursor } = view.state.selection as TextSelection\n\n            if ($cursor) {\n              run({\n                editor,\n                from: $cursor.pos,\n                to: $cursor.pos,\n                text: '',\n                rules,\n                plugin,\n              })\n            }\n          })\n\n          return false\n        },\n      },\n\n      // add support for input rules to trigger on enter\n      // this is useful for example for code blocks\n      handleKeyDown(view, event) {\n        if (event.key !== 'Enter') {\n          return false\n        }\n\n        const { $cursor } = view.state.selection as TextSelection\n\n        if ($cursor) {\n          return run({\n            editor,\n            from: $cursor.pos,\n            to: $cursor.pos,\n            text: '\\n',\n            rules,\n            plugin,\n          })\n        }\n\n        return false\n      },\n    },\n\n    // @ts-ignore\n    isInputRules: true,\n  }) as Plugin\n\n  return plugin\n}\n", "// see: https://github.com/mesqueeb/is-what/blob/88d6e4ca92fb2baab6003c54e02eedf4e729e5ab/src/index.ts\n\nfunction getType(value: any): string {\n  return Object.prototype.toString.call(value).slice(8, -1)\n}\n\nexport function isPlainObject(value: any): value is Record<string, any> {\n  if (getType(value) !== 'Object') {\n    return false\n  }\n\n  return value.constructor === Object && Object.getPrototypeOf(value) === Object.prototype\n}\n", "import { isPlainObject } from './isPlainObject.js'\n\nexport function mergeDeep(target: Record<string, any>, source: Record<string, any>): Record<string, any> {\n  const output = { ...target }\n\n  if (isPlainObject(target) && isPlainObject(source)) {\n    Object.keys(source).forEach(key => {\n      if (isPlainObject(source[key]) && isPlainObject(target[key])) {\n        output[key] = mergeDeep(target[key], source[key])\n      } else {\n        output[key] = source[key]\n      }\n    })\n  }\n\n  return output\n}\n", "import {\n  DOMOutputSpec, Mark as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Mark<PERSON><PERSON>,\n} from '@tiptap/pm/model'\nimport { Plugin, Transaction } from '@tiptap/pm/state'\n\nimport { Editor } from './Editor.js'\nimport { getExtensionField } from './helpers/getExtensionField.js'\nimport { MarkConfig } from './index.js'\nimport { InputRule } from './InputRule.js'\nimport { Node } from './Node.js'\nimport { PasteRule } from './PasteRule.js'\nimport {\n  AnyConfig,\n  Attributes,\n  Extensions,\n  GlobalAttributes,\n  KeyboardShortcutCommand,\n  ParentConfig,\n  RawCommands,\n} from './types.js'\nimport { callOrReturn } from './utilities/callOrReturn.js'\nimport { mergeDeep } from './utilities/mergeDeep.js'\n\ndeclare module '@tiptap/core' {\n  export interface MarkConfig<Options = any, Storage = any> {\n    // @ts-ignore - this is a dynamic key\n    [key: string]: any\n\n    /**\n     * The extension name - this must be unique.\n     * It will be used to identify the extension.\n     *\n     * @example 'myExtension'\n     */\n    name: string\n\n    /**\n     * The priority of your extension. The higher, the earlier it will be called\n     * and will take precedence over other extensions with a lower priority.\n     * @default 100\n     * @example 101\n     */\n    priority?: number\n\n    /**\n     * The default options for this extension.\n     * @example\n     * defaultOptions: {\n     *   myOption: 'foo',\n     *   myOtherOption: 10,\n     * }\n     */\n    defaultOptions?: Options\n\n    /**\n     * This method will add options to this extension\n     * @see https://tiptap.dev/guide/custom-extensions#settings\n     * @example\n     * addOptions() {\n     *  return {\n     *    myOption: 'foo',\n     *    myOtherOption: 10,\n     * }\n     */\n    addOptions?: (this: {\n      name: string\n      parent: Exclude<ParentConfig<MarkConfig<Options, Storage>>['addOptions'], undefined>\n    }) => Options\n\n    /**\n     * The default storage this extension can save data to.\n     * @see https://tiptap.dev/guide/custom-extensions#storage\n     * @example\n     * defaultStorage: {\n     *   prefetchedUsers: [],\n     *   loading: false,\n     * }\n     */\n    addStorage?: (this: {\n      name: string\n      options: Options\n      parent: Exclude<ParentConfig<MarkConfig<Options, Storage>>['addStorage'], undefined>\n    }) => Storage\n\n    /**\n     * This function adds globalAttributes to specific nodes.\n     * @see https://tiptap.dev/guide/custom-extensions#global-attributes\n     * @example\n     * addGlobalAttributes() {\n     *   return [\n     *     {\n             // Extend the following extensions\n     *       types: [\n     *         'heading',\n     *         'paragraph',\n     *       ],\n     *       // … with those attributes\n     *       attributes: {\n     *         textAlign: {\n     *           default: 'left',\n     *           renderHTML: attributes => ({\n     *             style: `text-align: ${attributes.textAlign}`,\n     *           }),\n     *           parseHTML: element => element.style.textAlign || 'left',\n     *         },\n     *       },\n     *     },\n     *   ]\n     * }\n     */\n    addGlobalAttributes?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      extensions: (Node | Mark)[]\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addGlobalAttributes']\n    }) => GlobalAttributes\n\n    /**\n     * This function adds commands to the editor\n     * @see https://tiptap.dev/guide/custom-extensions#keyboard-shortcuts\n     * @example\n     * addCommands() {\n     *   return {\n     *     myCommand: () => ({ chain }) => chain().setMark('type', 'foo').run(),\n     *   }\n     * }\n     */\n    addCommands?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: MarkType\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addCommands']\n    }) => Partial<RawCommands>\n\n    /**\n     * This function registers keyboard shortcuts.\n     * @see https://tiptap.dev/guide/custom-extensions#keyboard-shortcuts\n     * @example\n     * addKeyboardShortcuts() {\n     *   return {\n     *     'Mod-l': () => this.editor.commands.toggleBulletList(),\n     *   }\n     * },\n     */\n    addKeyboardShortcuts?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: MarkType\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addKeyboardShortcuts']\n    }) => {\n      [key: string]: KeyboardShortcutCommand\n    }\n\n    /**\n     * This function adds input rules to the editor.\n     * @see https://tiptap.dev/guide/custom-extensions#input-rules\n     * @example\n     * addInputRules() {\n     *   return [\n     *     markInputRule({\n     *       find: inputRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addInputRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: MarkType\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addInputRules']\n    }) => InputRule[]\n\n    /**\n     * This function adds paste rules to the editor.\n     * @see https://tiptap.dev/guide/custom-extensions#paste-rules\n     * @example\n     * addPasteRules() {\n     *   return [\n     *     markPasteRule({\n     *       find: pasteRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addPasteRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: MarkType\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addPasteRules']\n    }) => PasteRule[]\n\n    /**\n     * This function adds Prosemirror plugins to the editor\n     * @see https://tiptap.dev/guide/custom-extensions#prosemirror-plugins\n     * @example\n     * addProseMirrorPlugins() {\n     *   return [\n     *     customPlugin(),\n     *   ]\n     * }\n     */\n    addProseMirrorPlugins?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: MarkType\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addProseMirrorPlugins']\n    }) => Plugin[]\n\n    /**\n     * This function adds additional extensions to the editor. This is useful for\n     * building extension kits.\n     * @example\n     * addExtensions() {\n     *   return [\n     *     BulletList,\n     *     OrderedList,\n     *     ListItem\n     *   ]\n     * }\n     */\n    addExtensions?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addExtensions']\n    }) => Extensions\n\n    /**\n     * This function extends the schema of the node.\n     * @example\n     * extendNodeSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendNodeSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<MarkConfig<Options, Storage>>['extendNodeSchema']\n          },\n          extension: Node,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * This function extends the schema of the mark.\n     * @example\n     * extendMarkSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendMarkSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<MarkConfig<Options, Storage>>['extendMarkSchema']\n          },\n          extension: Mark,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * The editor is not ready yet.\n     */\n    onBeforeCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: MarkType\n          parent: ParentConfig<MarkConfig<Options, Storage>>['onBeforeCreate']\n        }) => void)\n      | null\n\n    /**\n     * The editor is ready.\n     */\n    onCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: MarkType\n          parent: ParentConfig<MarkConfig<Options, Storage>>['onCreate']\n        }) => void)\n      | null\n\n    /**\n     * The content has changed.\n     */\n    onUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: MarkType\n          parent: ParentConfig<MarkConfig<Options, Storage>>['onUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The selection has changed.\n     */\n    onSelectionUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: MarkType\n          parent: ParentConfig<MarkConfig<Options, Storage>>['onSelectionUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The editor state has changed.\n     */\n    onTransaction?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: MarkType\n            parent: ParentConfig<MarkConfig<Options, Storage>>['onTransaction']\n          },\n          props: {\n            editor: Editor\n            transaction: Transaction\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is focused.\n     */\n    onFocus?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: MarkType\n            parent: ParentConfig<MarkConfig<Options, Storage>>['onFocus']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor isn’t focused anymore.\n     */\n    onBlur?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: MarkType\n            parent: ParentConfig<MarkConfig<Options, Storage>>['onBlur']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is destroyed.\n     */\n    onDestroy?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: MarkType\n          parent: ParentConfig<MarkConfig<Options, Storage>>['onDestroy']\n        }) => void)\n      | null\n\n    /**\n     * Keep mark after split node\n     */\n    keepOnSplit?: boolean | (() => boolean)\n\n    /**\n     * Inclusive\n     */\n    inclusive?:\n      | MarkSpec['inclusive']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<MarkConfig<Options, Storage>>['inclusive']\n          editor?: Editor\n        }) => MarkSpec['inclusive'])\n\n    /**\n     * Excludes\n     */\n    excludes?:\n      | MarkSpec['excludes']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<MarkConfig<Options, Storage>>['excludes']\n          editor?: Editor\n        }) => MarkSpec['excludes'])\n\n    /**\n     * Marks this Mark as exitable\n     */\n    exitable?: boolean | (() => boolean)\n\n    /**\n     * Group\n     */\n    group?:\n      | MarkSpec['group']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<MarkConfig<Options, Storage>>['group']\n          editor?: Editor\n        }) => MarkSpec['group'])\n\n    /**\n     * Spanning\n     */\n    spanning?:\n      | MarkSpec['spanning']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<MarkConfig<Options, Storage>>['spanning']\n          editor?: Editor\n        }) => MarkSpec['spanning'])\n\n    /**\n     * Code\n     */\n    code?:\n      | boolean\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<MarkConfig<Options, Storage>>['code']\n          editor?: Editor\n        }) => boolean)\n\n    /**\n     * Parse HTML\n     */\n    parseHTML?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<MarkConfig<Options, Storage>>['parseHTML']\n      editor?: Editor\n    }) => MarkSpec['parseDOM']\n\n    /**\n     * Render HTML\n     */\n    renderHTML?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<MarkConfig<Options, Storage>>['renderHTML']\n            editor?: Editor\n          },\n          props: {\n            mark: ProseMirrorMark\n            HTMLAttributes: Record<string, any>\n          },\n        ) => DOMOutputSpec)\n      | null\n\n    /**\n     * Attributes\n     */\n    addAttributes?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addAttributes']\n      editor?: Editor\n    // eslint-disable-next-line @typescript-eslint/no-empty-object-type\n    }) => Attributes | {}\n  }\n}\n\n/**\n * The Mark class is used to create custom mark extensions.\n * @see https://tiptap.dev/api/extensions#create-a-new-extension\n */\nexport class Mark<Options = any, Storage = any> {\n  type = 'mark'\n\n  name = 'mark'\n\n  parent: Mark | null = null\n\n  child: Mark | null = null\n\n  options: Options\n\n  storage: Storage\n\n  config: MarkConfig = {\n    name: this.name,\n    defaultOptions: {},\n  }\n\n  constructor(config: Partial<MarkConfig<Options, Storage>> = {}) {\n    this.config = {\n      ...this.config,\n      ...config,\n    }\n\n    this.name = this.config.name\n\n    if (config.defaultOptions && Object.keys(config.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${this.name}\".`,\n      )\n    }\n\n    // TODO: remove `addOptions` fallback\n    this.options = this.config.defaultOptions\n\n    if (this.config.addOptions) {\n      this.options = callOrReturn(\n        getExtensionField<AnyConfig['addOptions']>(this, 'addOptions', {\n          name: this.name,\n        }),\n      )\n    }\n\n    this.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(this, 'addStorage', {\n        name: this.name,\n        options: this.options,\n      }),\n    ) || {}\n  }\n\n  static create<O = any, S = any>(config: Partial<MarkConfig<O, S>> = {}) {\n    return new Mark<O, S>(config)\n  }\n\n  configure(options: Partial<Options> = {}) {\n    // return a new instance so we can use the same extension\n    // with different calls of `configure`\n    const extension = this.extend<Options, Storage>({\n      ...this.config,\n      addOptions: () => {\n        return mergeDeep(this.options as Record<string, any>, options) as Options\n      },\n    })\n\n    // Always preserve the current name\n    extension.name = this.name\n    // Set the parent to be our parent\n    extension.parent = this.parent\n\n    return extension\n  }\n\n  extend<ExtendedOptions = Options, ExtendedStorage = Storage>(\n    extendedConfig: Partial<MarkConfig<ExtendedOptions, ExtendedStorage>> = {},\n  ) {\n    const extension = new Mark<ExtendedOptions, ExtendedStorage>(extendedConfig)\n\n    extension.parent = this\n\n    this.child = extension\n\n    extension.name = extendedConfig.name ? extendedConfig.name : extension.parent.name\n\n    if (extendedConfig.defaultOptions && Object.keys(extendedConfig.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${extension.name}\".`,\n      )\n    }\n\n    extension.options = callOrReturn(\n      getExtensionField<AnyConfig['addOptions']>(extension, 'addOptions', {\n        name: extension.name,\n      }),\n    )\n\n    extension.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(extension, 'addStorage', {\n        name: extension.name,\n        options: extension.options,\n      }),\n    )\n\n    return extension\n  }\n\n  static handleExit({ editor, mark }: { editor: Editor; mark: Mark }) {\n    const { tr } = editor.state\n    const currentPos = editor.state.selection.$from\n    const isAtEnd = currentPos.pos === currentPos.end()\n\n    if (isAtEnd) {\n      const currentMarks = currentPos.marks()\n      const isInMark = !!currentMarks.find(m => m?.type.name === mark.name)\n\n      if (!isInMark) {\n        return false\n      }\n\n      const removeMark = currentMarks.find(m => m?.type.name === mark.name)\n\n      if (removeMark) {\n        tr.removeStoredMark(removeMark)\n      }\n      tr.insertText(' ', currentPos.pos)\n\n      editor.view.dispatch(tr)\n\n      return true\n    }\n\n    return false\n  }\n}\n", "export function isNumber(value: any): value is number {\n  return typeof value === 'number'\n}\n", "import { Fragment, Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { EditorState, Plugin } from '@tiptap/pm/state'\n\nimport { CommandManager } from './CommandManager.js'\nimport { Editor } from './Editor.js'\nimport { createChainableState } from './helpers/createChainableState.js'\nimport { getHTMLFromFragment } from './helpers/getHTMLFromFragment.js'\nimport {\n  CanCommands,\n  ChainedCommands,\n  ExtendedRegExpMatchArray,\n  Range,\n  SingleCommands,\n} from './types.js'\nimport { isNumber } from './utilities/isNumber.js'\nimport { isRegExp } from './utilities/isRegExp.js'\n\nexport type PasteRuleMatch = {\n  index: number;\n  text: string;\n  replaceWith?: string;\n  match?: RegExpMatchArray;\n  data?: Record<string, any>;\n};\n\nexport type PasteRuleFinder =\n  | RegExp\n  | ((text: string, event?: ClipboardEvent | null) => PasteRuleMatch[] | null | undefined);\n\n/**\n * Paste rules are used to react to pasted content.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules\n */\nexport class PasteRule {\n  find: PasteRuleFinder\n\n  handler: (props: {\n    state: EditorState;\n    range: Range;\n    match: ExtendedRegExpMatchArray;\n    commands: SingleCommands;\n    chain: () => ChainedCommands;\n    can: () => CanCommands;\n    pasteEvent: ClipboardEvent | null;\n    dropEvent: DragEvent | null;\n  }) => void | null\n\n  constructor(config: {\n    find: PasteRuleFinder;\n    handler: (props: {\n      can: () => CanCommands;\n      chain: () => ChainedCommands;\n      commands: SingleCommands;\n      dropEvent: DragEvent | null;\n      match: ExtendedRegExpMatchArray;\n      pasteEvent: ClipboardEvent | null;\n      range: Range;\n      state: EditorState;\n    }) => void | null;\n  }) {\n    this.find = config.find\n    this.handler = config.handler\n  }\n}\n\nconst pasteRuleMatcherHandler = (\n  text: string,\n  find: PasteRuleFinder,\n  event?: ClipboardEvent | null,\n): ExtendedRegExpMatchArray[] => {\n  if (isRegExp(find)) {\n    return [...text.matchAll(find)]\n  }\n\n  const matches = find(text, event)\n\n  if (!matches) {\n    return []\n  }\n\n  return matches.map(pasteRuleMatch => {\n    const result: ExtendedRegExpMatchArray = [pasteRuleMatch.text]\n\n    result.index = pasteRuleMatch.index\n    result.input = text\n    result.data = pasteRuleMatch.data\n\n    if (pasteRuleMatch.replaceWith) {\n      if (!pasteRuleMatch.text.includes(pasteRuleMatch.replaceWith)) {\n        console.warn(\n          '[tiptap warn]: \"pasteRuleMatch.replaceWith\" must be part of \"pasteRuleMatch.text\".',\n        )\n      }\n\n      result.push(pasteRuleMatch.replaceWith)\n    }\n\n    return result\n  })\n}\n\nfunction run(config: {\n  editor: Editor;\n  state: EditorState;\n  from: number;\n  to: number;\n  rule: PasteRule;\n  pasteEvent: ClipboardEvent | null;\n  dropEvent: DragEvent | null;\n}): boolean {\n  const {\n    editor, state, from, to, rule, pasteEvent, dropEvent,\n  } = config\n\n  const { commands, chain, can } = new CommandManager({\n    editor,\n    state,\n  })\n\n  const handlers: (void | null)[] = []\n\n  state.doc.nodesBetween(from, to, (node, pos) => {\n    if (!node.isTextblock || node.type.spec.code) {\n      return\n    }\n\n    const resolvedFrom = Math.max(from, pos)\n    const resolvedTo = Math.min(to, pos + node.content.size)\n    const textToMatch = node.textBetween(resolvedFrom - pos, resolvedTo - pos, undefined, '\\ufffc')\n\n    const matches = pasteRuleMatcherHandler(textToMatch, rule.find, pasteEvent)\n\n    matches.forEach(match => {\n      if (match.index === undefined) {\n        return\n      }\n\n      const start = resolvedFrom + match.index + 1\n      const end = start + match[0].length\n      const range = {\n        from: state.tr.mapping.map(start),\n        to: state.tr.mapping.map(end),\n      }\n\n      const handler = rule.handler({\n        state,\n        range,\n        match,\n        commands,\n        chain,\n        can,\n        pasteEvent,\n        dropEvent,\n      })\n\n      handlers.push(handler)\n    })\n  })\n\n  const success = handlers.every(handler => handler !== null)\n\n  return success\n}\n\n// When dragging across editors, must get another editor instance to delete selection content.\nlet tiptapDragFromOtherEditor: Editor | null = null\n\nconst createClipboardPasteEvent = (text: string) => {\n  const event = new ClipboardEvent('paste', {\n    clipboardData: new DataTransfer(),\n  })\n\n  event.clipboardData?.setData('text/html', text)\n\n  return event\n}\n\n/**\n * Create an paste rules plugin. When enabled, it will cause pasted\n * text that matches any of the given rules to trigger the rule’s\n * action.\n */\nexport function pasteRulesPlugin(props: { editor: Editor; rules: PasteRule[] }): Plugin[] {\n  const { editor, rules } = props\n  let dragSourceElement: Element | null = null\n  let isPastedFromProseMirror = false\n  let isDroppedFromProseMirror = false\n  let pasteEvent = typeof ClipboardEvent !== 'undefined' ? new ClipboardEvent('paste') : null\n  let dropEvent: DragEvent | null\n\n  try {\n    dropEvent = typeof DragEvent !== 'undefined' ? new DragEvent('drop') : null\n  } catch {\n    dropEvent = null\n  }\n\n  const processEvent = ({\n    state,\n    from,\n    to,\n    rule,\n    pasteEvt,\n  }: {\n    state: EditorState;\n    from: number;\n    to: { b: number };\n    rule: PasteRule;\n    pasteEvt: ClipboardEvent | null;\n  }) => {\n    const tr = state.tr\n    const chainableState = createChainableState({\n      state,\n      transaction: tr,\n    })\n\n    const handler = run({\n      editor,\n      state: chainableState,\n      from: Math.max(from - 1, 0),\n      to: to.b - 1,\n      rule,\n      pasteEvent: pasteEvt,\n      dropEvent,\n    })\n\n    if (!handler || !tr.steps.length) {\n      return\n    }\n\n    try {\n      dropEvent = typeof DragEvent !== 'undefined' ? new DragEvent('drop') : null\n    } catch {\n      dropEvent = null\n    }\n    pasteEvent = typeof ClipboardEvent !== 'undefined' ? new ClipboardEvent('paste') : null\n\n    return tr\n  }\n\n  const plugins = rules.map(rule => {\n    return new Plugin({\n      // we register a global drag handler to track the current drag source element\n      view(view) {\n        const handleDragstart = (event: DragEvent) => {\n          dragSourceElement = view.dom.parentElement?.contains(event.target as Element)\n            ? view.dom.parentElement\n            : null\n\n          if (dragSourceElement) {\n            tiptapDragFromOtherEditor = editor\n          }\n        }\n\n        const handleDragend = () => {\n          if (tiptapDragFromOtherEditor) {\n            tiptapDragFromOtherEditor = null\n          }\n        }\n\n        window.addEventListener('dragstart', handleDragstart)\n        window.addEventListener('dragend', handleDragend)\n\n        return {\n          destroy() {\n            window.removeEventListener('dragstart', handleDragstart)\n            window.removeEventListener('dragend', handleDragend)\n          },\n        }\n      },\n\n      props: {\n        handleDOMEvents: {\n          drop: (view, event: Event) => {\n            isDroppedFromProseMirror = dragSourceElement === view.dom.parentElement\n            dropEvent = event as DragEvent\n\n            if (!isDroppedFromProseMirror) {\n              const dragFromOtherEditor = tiptapDragFromOtherEditor\n\n              if (dragFromOtherEditor) {\n                // setTimeout to avoid the wrong content after drop, timeout arg can't be empty or 0\n                setTimeout(() => {\n                  const selection = dragFromOtherEditor.state.selection\n\n                  if (selection) {\n                    dragFromOtherEditor.commands.deleteRange({ from: selection.from, to: selection.to })\n                  }\n                }, 10)\n              }\n            }\n            return false\n          },\n\n          paste: (_view, event: Event) => {\n            const html = (event as ClipboardEvent).clipboardData?.getData('text/html')\n\n            pasteEvent = event as ClipboardEvent\n\n            isPastedFromProseMirror = !!html?.includes('data-pm-slice')\n\n            return false\n          },\n        },\n      },\n\n      appendTransaction: (transactions, oldState, state) => {\n        const transaction = transactions[0]\n        const isPaste = transaction.getMeta('uiEvent') === 'paste' && !isPastedFromProseMirror\n        const isDrop = transaction.getMeta('uiEvent') === 'drop' && !isDroppedFromProseMirror\n\n        // if PasteRule is triggered by insertContent()\n        const simulatedPasteMeta = transaction.getMeta('applyPasteRules') as\n          | undefined\n          | { from: number; text: string | ProseMirrorNode | Fragment }\n        const isSimulatedPaste = !!simulatedPasteMeta\n\n        if (!isPaste && !isDrop && !isSimulatedPaste) {\n          return\n        }\n\n        // Handle simulated paste\n        if (isSimulatedPaste) {\n          let { text } = simulatedPasteMeta\n\n          if (typeof text === 'string') {\n            text = text as string\n          } else {\n            text = getHTMLFromFragment(Fragment.from(text), state.schema)\n          }\n\n          const { from } = simulatedPasteMeta\n          const to = from + text.length\n\n          const pasteEvt = createClipboardPasteEvent(text)\n\n          return processEvent({\n            rule,\n            state,\n            from,\n            to: { b: to },\n            pasteEvt,\n          })\n        }\n\n        // handle actual paste/drop\n        const from = oldState.doc.content.findDiffStart(state.doc.content)\n        const to = oldState.doc.content.findDiffEnd(state.doc.content)\n\n        // stop if there is no changed range\n        if (!isNumber(from) || !to || from === to.b) {\n          return\n        }\n\n        return processEvent({\n          rule,\n          state,\n          from,\n          to,\n          pasteEvt: pasteEvent,\n        })\n      },\n    })\n  })\n\n  return plugins\n}\n", "export function findDuplicates(items: any[]): any[] {\n  const filtered = items.filter((el, index) => items.indexOf(el) !== index)\n\n  return Array.from(new Set(filtered))\n}\n", "import { keymap } from '@tiptap/pm/keymap'\nimport { Schema } from '@tiptap/pm/model'\nimport { Plugin } from '@tiptap/pm/state'\nimport { NodeViewConstructor } from '@tiptap/pm/view'\n\nimport type { Editor } from './Editor.js'\nimport { getAttributesFromExtensions } from './helpers/getAttributesFromExtensions.js'\nimport { getExtensionField } from './helpers/getExtensionField.js'\nimport { getNodeType } from './helpers/getNodeType.js'\nimport { getRenderedAttributes } from './helpers/getRenderedAttributes.js'\nimport { getSchemaByResolvedExtensions } from './helpers/getSchemaByResolvedExtensions.js'\nimport { getSchemaTypeByName } from './helpers/getSchemaTypeByName.js'\nimport { isExtensionRulesEnabled } from './helpers/isExtensionRulesEnabled.js'\nimport { splitExtensions } from './helpers/splitExtensions.js'\nimport type { NodeConfig } from './index.js'\nimport { InputRule, inputRulesPlugin } from './InputRule.js'\nimport { Mark } from './Mark.js'\nimport { PasteRule, pasteRulesPlugin } from './PasteRule.js'\nimport { AnyConfig, Extensions, RawCommands } from './types.js'\nimport { callOrReturn } from './utilities/callOrReturn.js'\nimport { findDuplicates } from './utilities/findDuplicates.js'\n\nexport class ExtensionManager {\n  editor: Editor\n\n  schema: Schema\n\n  extensions: Extensions\n\n  splittableMarks: string[] = []\n\n  constructor(extensions: Extensions, editor: Editor) {\n    this.editor = editor\n    this.extensions = ExtensionManager.resolve(extensions)\n    this.schema = getSchemaByResolvedExtensions(this.extensions, editor)\n    this.setupExtensions()\n  }\n\n  /**\n   * Returns a flattened and sorted extension list while\n   * also checking for duplicated extensions and warns the user.\n   * @param extensions An array of Tiptap extensions\n   * @returns An flattened and sorted array of Tiptap extensions\n   */\n  static resolve(extensions: Extensions): Extensions {\n    const resolvedExtensions = ExtensionManager.sort(ExtensionManager.flatten(extensions))\n    const duplicatedNames = findDuplicates(resolvedExtensions.map(extension => extension.name))\n\n    if (duplicatedNames.length) {\n      console.warn(\n        `[tiptap warn]: Duplicate extension names found: [${duplicatedNames\n          .map(item => `'${item}'`)\n          .join(', ')}]. This can lead to issues.`,\n      )\n    }\n\n    return resolvedExtensions\n  }\n\n  /**\n   * Create a flattened array of extensions by traversing the `addExtensions` field.\n   * @param extensions An array of Tiptap extensions\n   * @returns A flattened array of Tiptap extensions\n   */\n  static flatten(extensions: Extensions): Extensions {\n    return (\n      extensions\n        .map(extension => {\n          const context = {\n            name: extension.name,\n            options: extension.options,\n            storage: extension.storage,\n          }\n\n          const addExtensions = getExtensionField<AnyConfig['addExtensions']>(\n            extension,\n            'addExtensions',\n            context,\n          )\n\n          if (addExtensions) {\n            return [extension, ...this.flatten(addExtensions())]\n          }\n\n          return extension\n        })\n        // `Infinity` will break TypeScript so we set a number that is probably high enough\n        .flat(10)\n    )\n  }\n\n  /**\n   * Sort extensions by priority.\n   * @param extensions An array of Tiptap extensions\n   * @returns A sorted array of Tiptap extensions by priority\n   */\n  static sort(extensions: Extensions): Extensions {\n    const defaultPriority = 100\n\n    return extensions.sort((a, b) => {\n      const priorityA = getExtensionField<AnyConfig['priority']>(a, 'priority') || defaultPriority\n      const priorityB = getExtensionField<AnyConfig['priority']>(b, 'priority') || defaultPriority\n\n      if (priorityA > priorityB) {\n        return -1\n      }\n\n      if (priorityA < priorityB) {\n        return 1\n      }\n\n      return 0\n    })\n  }\n\n  /**\n   * Get all commands from the extensions.\n   * @returns An object with all commands where the key is the command name and the value is the command function\n   */\n  get commands(): RawCommands {\n    return this.extensions.reduce((commands, extension) => {\n      const context = {\n        name: extension.name,\n        options: extension.options,\n        storage: extension.storage,\n        editor: this.editor,\n        type: getSchemaTypeByName(extension.name, this.schema),\n      }\n\n      const addCommands = getExtensionField<AnyConfig['addCommands']>(\n        extension,\n        'addCommands',\n        context,\n      )\n\n      if (!addCommands) {\n        return commands\n      }\n\n      return {\n        ...commands,\n        ...addCommands(),\n      }\n    }, {} as RawCommands)\n  }\n\n  /**\n   * Get all registered Prosemirror plugins from the extensions.\n   * @returns An array of Prosemirror plugins\n   */\n  get plugins(): Plugin[] {\n    const { editor } = this\n\n    // With ProseMirror, first plugins within an array are executed first.\n    // In Tiptap, we provide the ability to override plugins,\n    // so it feels more natural to run plugins at the end of an array first.\n    // That’s why we have to reverse the `extensions` array and sort again\n    // based on the `priority` option.\n    const extensions = ExtensionManager.sort([...this.extensions].reverse())\n\n    const inputRules: InputRule[] = []\n    const pasteRules: PasteRule[] = []\n\n    const allPlugins = extensions\n      .map(extension => {\n        const context = {\n          name: extension.name,\n          options: extension.options,\n          storage: extension.storage,\n          editor,\n          type: getSchemaTypeByName(extension.name, this.schema),\n        }\n\n        const plugins: Plugin[] = []\n\n        const addKeyboardShortcuts = getExtensionField<AnyConfig['addKeyboardShortcuts']>(\n          extension,\n          'addKeyboardShortcuts',\n          context,\n        )\n\n        let defaultBindings: Record<string, () => boolean> = {}\n\n        // bind exit handling\n        if (extension.type === 'mark' && getExtensionField<AnyConfig['exitable']>(extension, 'exitable', context)) {\n          defaultBindings.ArrowRight = () => Mark.handleExit({ editor, mark: extension as Mark })\n        }\n\n        if (addKeyboardShortcuts) {\n          const bindings = Object.fromEntries(\n            Object.entries(addKeyboardShortcuts()).map(([shortcut, method]) => {\n              return [shortcut, () => method({ editor })]\n            }),\n          )\n\n          defaultBindings = { ...defaultBindings, ...bindings }\n        }\n\n        const keyMapPlugin = keymap(defaultBindings)\n\n        plugins.push(keyMapPlugin)\n\n        const addInputRules = getExtensionField<AnyConfig['addInputRules']>(\n          extension,\n          'addInputRules',\n          context,\n        )\n\n        if (isExtensionRulesEnabled(extension, editor.options.enableInputRules) && addInputRules) {\n          inputRules.push(...addInputRules())\n        }\n\n        const addPasteRules = getExtensionField<AnyConfig['addPasteRules']>(\n          extension,\n          'addPasteRules',\n          context,\n        )\n\n        if (isExtensionRulesEnabled(extension, editor.options.enablePasteRules) && addPasteRules) {\n          pasteRules.push(...addPasteRules())\n        }\n\n        const addProseMirrorPlugins = getExtensionField<AnyConfig['addProseMirrorPlugins']>(\n          extension,\n          'addProseMirrorPlugins',\n          context,\n        )\n\n        if (addProseMirrorPlugins) {\n          const proseMirrorPlugins = addProseMirrorPlugins()\n\n          plugins.push(...proseMirrorPlugins)\n        }\n\n        return plugins\n      })\n      .flat()\n\n    return [\n      inputRulesPlugin({\n        editor,\n        rules: inputRules,\n      }),\n      ...pasteRulesPlugin({\n        editor,\n        rules: pasteRules,\n      }),\n      ...allPlugins,\n    ]\n  }\n\n  /**\n   * Get all attributes from the extensions.\n   * @returns An array of attributes\n   */\n  get attributes() {\n    return getAttributesFromExtensions(this.extensions)\n  }\n\n  /**\n   * Get all node views from the extensions.\n   * @returns An object with all node views where the key is the node name and the value is the node view function\n   */\n  get nodeViews(): Record<string, NodeViewConstructor> {\n    const { editor } = this\n    const { nodeExtensions } = splitExtensions(this.extensions)\n\n    return Object.fromEntries(\n      nodeExtensions\n        .filter(extension => !!getExtensionField(extension, 'addNodeView'))\n        .map(extension => {\n          const extensionAttributes = this.attributes.filter(\n            attribute => attribute.type === extension.name,\n          )\n          const context = {\n            name: extension.name,\n            options: extension.options,\n            storage: extension.storage,\n            editor,\n            type: getNodeType(extension.name, this.schema),\n          }\n          const addNodeView = getExtensionField<NodeConfig['addNodeView']>(\n            extension,\n            'addNodeView',\n            context,\n          )\n\n          if (!addNodeView) {\n            return []\n          }\n\n          const nodeview: NodeViewConstructor = (\n            node,\n            view,\n            getPos,\n            decorations,\n            innerDecorations,\n          ) => {\n            const HTMLAttributes = getRenderedAttributes(node, extensionAttributes)\n\n            return addNodeView()({\n              // pass-through\n              node,\n              view,\n              getPos: getPos as () => number,\n              decorations,\n              innerDecorations,\n              // tiptap-specific\n              editor,\n              extension,\n              HTMLAttributes,\n            })\n          }\n\n          return [extension.name, nodeview]\n        }),\n    )\n  }\n\n  /**\n   * Go through all extensions, create extension storages & setup marks\n   * & bind editor event listener.\n   */\n  private setupExtensions() {\n    this.extensions.forEach(extension => {\n      // store extension storage in editor\n      this.editor.extensionStorage[extension.name] = extension.storage\n\n      const context = {\n        name: extension.name,\n        options: extension.options,\n        storage: extension.storage,\n        editor: this.editor,\n        type: getSchemaTypeByName(extension.name, this.schema),\n      }\n\n      if (extension.type === 'mark') {\n        const keepOnSplit = callOrReturn(getExtensionField(extension, 'keepOnSplit', context)) ?? true\n\n        if (keepOnSplit) {\n          this.splittableMarks.push(extension.name)\n        }\n      }\n\n      const onBeforeCreate = getExtensionField<AnyConfig['onBeforeCreate']>(\n        extension,\n        'onBeforeCreate',\n        context,\n      )\n      const onCreate = getExtensionField<AnyConfig['onCreate']>(extension, 'onCreate', context)\n      const onUpdate = getExtensionField<AnyConfig['onUpdate']>(extension, 'onUpdate', context)\n      const onSelectionUpdate = getExtensionField<AnyConfig['onSelectionUpdate']>(\n        extension,\n        'onSelectionUpdate',\n        context,\n      )\n      const onTransaction = getExtensionField<AnyConfig['onTransaction']>(\n        extension,\n        'onTransaction',\n        context,\n      )\n      const onFocus = getExtensionField<AnyConfig['onFocus']>(extension, 'onFocus', context)\n      const onBlur = getExtensionField<AnyConfig['onBlur']>(extension, 'onBlur', context)\n      const onDestroy = getExtensionField<AnyConfig['onDestroy']>(extension, 'onDestroy', context)\n\n      if (onBeforeCreate) {\n        this.editor.on('beforeCreate', onBeforeCreate)\n      }\n\n      if (onCreate) {\n        this.editor.on('create', onCreate)\n      }\n\n      if (onUpdate) {\n        this.editor.on('update', onUpdate)\n      }\n\n      if (onSelectionUpdate) {\n        this.editor.on('selectionUpdate', onSelectionUpdate)\n      }\n\n      if (onTransaction) {\n        this.editor.on('transaction', onTransaction)\n      }\n\n      if (onFocus) {\n        this.editor.on('focus', onFocus)\n      }\n\n      if (onBlur) {\n        this.editor.on('blur', onBlur)\n      }\n\n      if (onDestroy) {\n        this.editor.on('destroy', onDestroy)\n      }\n    })\n  }\n}\n", "import { Plugin, Transaction } from '@tiptap/pm/state'\n\nimport { Editor } from './Editor.js'\nimport { getExtensionField } from './helpers/getExtensionField.js'\nimport { ExtensionConfig } from './index.js'\nimport { InputRule } from './InputRule.js'\nimport { Mark } from './Mark.js'\nimport { Node } from './Node.js'\nimport { PasteRule } from './PasteRule.js'\nimport {\n  AnyConfig,\n  Extensions,\n  GlobalAttributes,\n  KeyboardShortcutCommand,\n  ParentConfig,\n  RawCommands,\n} from './types.js'\nimport { callOrReturn } from './utilities/callOrReturn.js'\nimport { mergeDeep } from './utilities/mergeDeep.js'\n\ndeclare module '@tiptap/core' {\n  interface ExtensionConfig<Options = any, Storage = any> {\n    // @ts-ignore - this is a dynamic key\n    [key: string]: any\n\n    /**\n     * The extension name - this must be unique.\n     * It will be used to identify the extension.\n     *\n     * @example 'myExtension'\n     */\n    name: string\n\n    /**\n     * The priority of your extension. The higher, the earlier it will be called\n     * and will take precedence over other extensions with a lower priority.\n     * @default 100\n     * @example 101\n     */\n    priority?: number\n\n    /**\n     * The default options for this extension.\n     * @example\n     * defaultOptions: {\n     *   myOption: 'foo',\n     *   myOtherOption: 10,\n     * }\n     */\n    defaultOptions?: Options\n\n    /**\n     * This method will add options to this extension\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#settings\n     * @example\n     * addOptions() {\n     *  return {\n     *    myOption: 'foo',\n     *    myOtherOption: 10,\n     * }\n     */\n    addOptions?: (this: {\n      name: string\n      parent: Exclude<ParentConfig<ExtensionConfig<Options, Storage>>['addOptions'], undefined>\n    }) => Options\n\n    /**\n     * The default storage this extension can save data to.\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#storage\n     * @example\n     * defaultStorage: {\n     *   prefetchedUsers: [],\n     *   loading: false,\n     * }\n     */\n    addStorage?: (this: {\n      name: string\n      options: Options\n      parent: Exclude<ParentConfig<ExtensionConfig<Options, Storage>>['addStorage'], undefined>\n    }) => Storage\n\n    /**\n     * This function adds globalAttributes to specific nodes.\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#global-attributes\n     * @example\n     * addGlobalAttributes() {\n     *   return [\n     *     {\n             // Extend the following extensions\n     *       types: [\n     *         'heading',\n     *         'paragraph',\n     *       ],\n     *       // … with those attributes\n     *       attributes: {\n     *         textAlign: {\n     *           default: 'left',\n     *           renderHTML: attributes => ({\n     *             style: `text-align: ${attributes.textAlign}`,\n     *           }),\n     *           parseHTML: element => element.style.textAlign || 'left',\n     *         },\n     *       },\n     *     },\n     *   ]\n     * }\n     */\n    addGlobalAttributes?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      extensions: (Node | Mark)[]\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addGlobalAttributes']\n    }) => GlobalAttributes\n\n    /**\n     * This function adds commands to the editor\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#commands\n     * @example\n     * addCommands() {\n     *   return {\n     *     myCommand: () => ({ chain }) => chain().setMark('type', 'foo').run(),\n     *   }\n     * }\n     */\n    addCommands?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addCommands']\n    }) => Partial<RawCommands>\n\n    /**\n     * This function registers keyboard shortcuts.\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#keyboard-shortcuts\n     * @example\n     * addKeyboardShortcuts() {\n     *   return {\n     *     'Mod-l': () => this.editor.commands.toggleBulletList(),\n     *   }\n     * },\n     */\n    addKeyboardShortcuts?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addKeyboardShortcuts']\n    }) => {\n      [key: string]: KeyboardShortcutCommand\n    }\n\n    /**\n     * This function adds input rules to the editor.\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#input-rules\n     * @example\n     * addInputRules() {\n     *   return [\n     *     markInputRule({\n     *       find: inputRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addInputRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addInputRules']\n    }) => InputRule[]\n\n    /**\n     * This function adds paste rules to the editor.\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#paste-rules\n     * @example\n     * addPasteRules() {\n     *   return [\n     *     markPasteRule({\n     *       find: pasteRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addPasteRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addPasteRules']\n    }) => PasteRule[]\n\n    /**\n     * This function adds Prosemirror plugins to the editor\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#prosemirror-plugins\n     * @example\n     * addProseMirrorPlugins() {\n     *   return [\n     *     customPlugin(),\n     *   ]\n     * }\n     */\n    addProseMirrorPlugins?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addProseMirrorPlugins']\n    }) => Plugin[]\n\n    /**\n     * This function adds additional extensions to the editor. This is useful for\n     * building extension kits.\n     * @example\n     * addExtensions() {\n     *   return [\n     *     BulletList,\n     *     OrderedList,\n     *     ListItem\n     *   ]\n     * }\n     */\n    addExtensions?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addExtensions']\n    }) => Extensions\n\n    /**\n     * This function extends the schema of the node.\n     * @example\n     * extendNodeSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendNodeSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<ExtensionConfig<Options, Storage>>['extendNodeSchema']\n          },\n          extension: Node,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * This function extends the schema of the mark.\n     * @example\n     * extendMarkSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendMarkSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<ExtensionConfig<Options, Storage>>['extendMarkSchema']\n          },\n          extension: Mark,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * The editor is not ready yet.\n     */\n    onBeforeCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          parent: ParentConfig<ExtensionConfig<Options, Storage>>['onBeforeCreate']\n        }) => void)\n      | null\n\n    /**\n     * The editor is ready.\n     */\n    onCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          parent: ParentConfig<ExtensionConfig<Options, Storage>>['onCreate']\n        }) => void)\n      | null\n\n    /**\n     * The content has changed.\n     */\n    onUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          parent: ParentConfig<ExtensionConfig<Options, Storage>>['onUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The selection has changed.\n     */\n    onSelectionUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          parent: ParentConfig<ExtensionConfig<Options, Storage>>['onSelectionUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The editor state has changed.\n     */\n    onTransaction?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            parent: ParentConfig<ExtensionConfig<Options, Storage>>['onTransaction']\n          },\n          props: {\n            editor: Editor\n            transaction: Transaction\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is focused.\n     */\n    onFocus?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            parent: ParentConfig<ExtensionConfig<Options, Storage>>['onFocus']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor isn’t focused anymore.\n     */\n    onBlur?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            parent: ParentConfig<ExtensionConfig<Options, Storage>>['onBlur']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is destroyed.\n     */\n    onDestroy?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          parent: ParentConfig<ExtensionConfig<Options, Storage>>['onDestroy']\n        }) => void)\n      | null\n  }\n}\n\n/**\n * The Extension class is the base class for all extensions.\n * @see https://tiptap.dev/api/extensions#create-a-new-extension\n */\nexport class Extension<Options = any, Storage = any> {\n  type = 'extension'\n\n  name = 'extension'\n\n  parent: Extension | null = null\n\n  child: Extension | null = null\n\n  options: Options\n\n  storage: Storage\n\n  config: ExtensionConfig = {\n    name: this.name,\n    defaultOptions: {},\n  }\n\n  constructor(config: Partial<ExtensionConfig<Options, Storage>> = {}) {\n    this.config = {\n      ...this.config,\n      ...config,\n    }\n\n    this.name = this.config.name\n\n    if (config.defaultOptions && Object.keys(config.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${this.name}\".`,\n      )\n    }\n\n    // TODO: remove `addOptions` fallback\n    this.options = this.config.defaultOptions\n\n    if (this.config.addOptions) {\n      this.options = callOrReturn(\n        getExtensionField<AnyConfig['addOptions']>(this, 'addOptions', {\n          name: this.name,\n        }),\n      )\n    }\n\n    this.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(this, 'addStorage', {\n        name: this.name,\n        options: this.options,\n      }),\n    ) || {}\n  }\n\n  static create<O = any, S = any>(config: Partial<ExtensionConfig<O, S>> = {}) {\n    return new Extension<O, S>(config)\n  }\n\n  configure(options: Partial<Options> = {}) {\n    // return a new instance so we can use the same extension\n    // with different calls of `configure`\n    const extension = this.extend<Options, Storage>({\n      ...this.config,\n      addOptions: () => {\n        return mergeDeep(this.options as Record<string, any>, options) as Options\n      },\n    })\n\n    // Always preserve the current name\n    extension.name = this.name\n    // Set the parent to be our parent\n    extension.parent = this.parent\n\n    return extension\n  }\n\n  extend<ExtendedOptions = Options, ExtendedStorage = Storage>(\n    extendedConfig: Partial<ExtensionConfig<ExtendedOptions, ExtendedStorage>> = {},\n  ) {\n    const extension = new Extension<ExtendedOptions, ExtendedStorage>({ ...this.config, ...extendedConfig })\n\n    extension.parent = this\n\n    this.child = extension\n\n    extension.name = extendedConfig.name ? extendedConfig.name : extension.parent.name\n\n    if (extendedConfig.defaultOptions && Object.keys(extendedConfig.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${extension.name}\".`,\n      )\n    }\n\n    extension.options = callOrReturn(\n      getExtensionField<AnyConfig['addOptions']>(extension, 'addOptions', {\n        name: extension.name,\n      }),\n    )\n\n    extension.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(extension, 'addStorage', {\n        name: extension.name,\n        options: extension.options,\n      }),\n    )\n\n    return extension\n  }\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { Range, TextSerializer } from '../types.js'\n\n/**\n * Gets the text between two positions in a Prosemirror node\n * and serializes it using the given text serializers and block separator (see getText)\n * @param startNode The Prosemirror node to start from\n * @param range The range of the text to get\n * @param options Options for the text serializer & block separator\n * @returns The text between the two positions\n */\nexport function getTextBetween(\n  startNode: ProseMirrorNode,\n  range: Range,\n  options?: {\n    blockSeparator?: string\n    textSerializers?: Record<string, TextSerializer>\n  },\n): string {\n  const { from, to } = range\n  const { blockSeparator = '\\n\\n', textSerializers = {} } = options || {}\n  let text = ''\n\n  startNode.nodesBetween(from, to, (node, pos, parent, index) => {\n    if (node.isBlock && pos > from) {\n      text += blockSeparator\n    }\n\n    const textSerializer = textSerializers?.[node.type.name]\n\n    if (textSerializer) {\n      if (parent) {\n        text += textSerializer({\n          node,\n          pos,\n          parent,\n          index,\n          range,\n        })\n      }\n      // do not descend into child nodes when there exists a serializer\n      return false\n    }\n\n    if (node.isText) {\n      text += node?.text?.slice(Math.max(from, pos) - pos, to - pos) // eslint-disable-line\n    }\n  })\n\n  return text\n}\n", "import { Schema } from '@tiptap/pm/model'\n\nimport { TextSerializer } from '../types.js'\n\n/**\n * Find text serializers `toText` in a Prosemirror schema\n * @param schema The Prosemirror schema to search in\n * @returns A record of text serializers by node name\n */\nexport function getTextSerializersFromSchema(schema: Schema): Record<string, TextSerializer> {\n  return Object.fromEntries(\n    Object.entries(schema.nodes)\n      .filter(([, node]) => node.spec.toText)\n      .map(([name, node]) => [name, node.spec.toText]),\n  )\n}\n", "import { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\nimport { getTextBetween } from '../helpers/getTextBetween.js'\nimport { getTextSerializersFromSchema } from '../helpers/getTextSerializersFromSchema.js'\n\nexport type ClipboardTextSerializerOptions = {\n  blockSeparator?: string,\n}\n\nexport const ClipboardTextSerializer = Extension.create<ClipboardTextSerializerOptions>({\n  name: 'clipboardTextSerializer',\n\n  addOptions() {\n    return {\n      blockSeparator: undefined,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('clipboardTextSerializer'),\n        props: {\n          clipboardTextSerializer: () => {\n            const { editor } = this\n            const { state, schema } = editor\n            const { doc, selection } = state\n            const { ranges } = selection\n            const from = Math.min(...ranges.map(range => range.$from.pos))\n            const to = Math.max(...ranges.map(range => range.$to.pos))\n            const textSerializers = getTextSerializersFromSchema(schema)\n            const range = { from, to }\n\n            return getTextBetween(doc, range, {\n              ...(this.options.blockSeparator !== undefined\n                ? { blockSeparator: this.options.blockSeparator }\n                : {}),\n              textSerializers,\n            })\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    blur: {\n      /**\n       * Removes focus from the editor.\n       * @example editor.commands.blur()\n       */\n      blur: () => ReturnType,\n    }\n  }\n}\n\nexport const blur: RawCommands['blur'] = () => ({ editor, view }) => {\n  requestAnimationFrame(() => {\n    if (!editor.isDestroyed) {\n      (view.dom as HTMLElement).blur()\n\n      // Browsers should remove the caret on blur but safari does not.\n      // See: https://github.com/ueberdosis/tiptap/issues/2405\n      window?.getSelection()?.removeAllRanges()\n    }\n  })\n\n  return true\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    clearContent: {\n      /**\n       * Clear the whole document.\n       * @param emitUpdate Whether to emit an update event.\n       * @example editor.commands.clearContent()\n       */\n      clearContent: (emitUpdate?: boolean) => ReturnType,\n    }\n  }\n}\n\nexport const clearContent: RawCommands['clearContent'] = (emitUpdate = false) => ({ commands }) => {\n  return commands.setContent('', emitUpdate)\n}\n", "import { liftTarget } from '@tiptap/pm/transform'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    clearNodes: {\n      /**\n       * Normalize nodes to a simple paragraph.\n       * @example editor.commands.clearNodes()\n       */\n      clearNodes: () => ReturnType,\n    }\n  }\n}\n\nexport const clearNodes: RawCommands['clearNodes'] = () => ({ state, tr, dispatch }) => {\n  const { selection } = tr\n  const { ranges } = selection\n\n  if (!dispatch) {\n    return true\n  }\n\n  ranges.forEach(({ $from, $to }) => {\n    state.doc.nodesBetween($from.pos, $to.pos, (node, pos) => {\n      if (node.type.isText) {\n        return\n      }\n\n      const { doc, mapping } = tr\n      const $mappedFrom = doc.resolve(mapping.map(pos))\n      const $mappedTo = doc.resolve(mapping.map(pos + node.nodeSize))\n      const nodeRange = $mappedFrom.blockRange($mappedTo)\n\n      if (!nodeRange) {\n        return\n      }\n\n      const targetLiftDepth = liftTarget(nodeRange)\n\n      if (node.type.isTextblock) {\n        const { defaultType } = $mappedFrom.parent.contentMatchAt($mappedFrom.index())\n\n        tr.setNodeMarkup(nodeRange.start, defaultType)\n      }\n\n      if (targetLiftDepth || targetLiftDepth === 0) {\n        tr.lift(nodeRange, targetLiftDepth)\n      }\n    })\n  })\n\n  return true\n}\n", "import { Command, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    command: {\n      /**\n       * Define a command inline.\n       * @param fn The command function.\n       * @example\n       * editor.commands.command(({ tr, state }) => {\n       *   ...\n       *   return true\n       * })\n       */\n      command: (fn: (props: Parameters<Command>[0]) => boolean) => ReturnType,\n    }\n  }\n}\n\nexport const command: RawCommands['command'] = fn => props => {\n  return fn(props)\n}\n", "import { createParagraphNear as originalCreateParagraph<PERSON>ear } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    createParagraphNear: {\n      /**\n       * Create a paragraph nearby.\n       * @example editor.commands.createParagraphNear()\n       */\n      createParagraphNear: () => ReturnType\n    }\n  }\n}\n\nexport const createParagraphNear: RawCommands['createParagraphNear'] = () => ({ state, dispatch }) => {\n  return originalCreateParagraphNear(state, dispatch)\n}\n", "import { TextSelection } from '@tiptap/pm/state'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    cut: {\n      /**\n       * Cuts content from a range and inserts it at a given position.\n       * @param range The range to cut.\n       * @param range.from The start position of the range.\n       * @param range.to The end position of the range.\n       * @param targetPos The position to insert the content at.\n       * @example editor.commands.cut({ from: 1, to: 3 }, 5)\n       */\n      cut: ({ from, to }: { from: number, to: number }, targetPos: number) => ReturnType,\n    }\n  }\n}\n\nexport const cut: RawCommands['cut'] = (originRange, targetPos) => ({ editor, tr }) => {\n  const { state } = editor\n\n  const contentSlice = state.doc.slice(originRange.from, originRange.to)\n\n  tr.deleteRange(originRange.from, originRange.to)\n  const newPos = tr.mapping.map(targetPos)\n\n  tr.insert(newPos, contentSlice.content)\n\n  tr.setSelection(new TextSelection(tr.doc.resolve(newPos - 1)))\n\n  return true\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    deleteCurrentNode: {\n      /**\n       * Delete the node that currently has the selection anchor.\n       * @example editor.commands.deleteCurrentNode()\n       */\n      deleteCurrentNode: () => ReturnType,\n    }\n  }\n}\n\nexport const deleteCurrentNode: RawCommands['deleteCurrentNode'] = () => ({ tr, dispatch }) => {\n  const { selection } = tr\n  const currentNode = selection.$anchor.node()\n\n  // if there is content inside the current node, break out of this command\n  if (currentNode.content.size > 0) {\n    return false\n  }\n\n  const $pos = tr.selection.$anchor\n\n  for (let depth = $pos.depth; depth > 0; depth -= 1) {\n    const node = $pos.node(depth)\n\n    if (node.type === currentNode.type) {\n      if (dispatch) {\n        const from = $pos.before(depth)\n        const to = $pos.after(depth)\n\n        tr.delete(from, to).scrollIntoView()\n      }\n\n      return true\n    }\n  }\n\n  return false\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    deleteNode: {\n      /**\n       * Delete a node with a given type or name.\n       * @param typeOrName The type or name of the node.\n       * @example editor.commands.deleteNode('paragraph')\n       */\n      deleteNode: (typeOrName: string | NodeType) => ReturnType,\n    }\n  }\n}\n\nexport const deleteNode: RawCommands['deleteNode'] = typeOrName => ({ tr, state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n  const $pos = tr.selection.$anchor\n\n  for (let depth = $pos.depth; depth > 0; depth -= 1) {\n    const node = $pos.node(depth)\n\n    if (node.type === type) {\n      if (dispatch) {\n        const from = $pos.before(depth)\n        const to = $pos.after(depth)\n\n        tr.delete(from, to).scrollIntoView()\n      }\n\n      return true\n    }\n  }\n\n  return false\n}\n", "import { Range, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    deleteRange: {\n      /**\n       * Delete a given range.\n       * @param range The range to delete.\n       * @example editor.commands.deleteRange({ from: 1, to: 3 })\n       */\n      deleteRange: (range: Range) => ReturnType,\n    }\n  }\n}\n\nexport const deleteRange: RawCommands['deleteRange'] = range => ({ tr, dispatch }) => {\n  const { from, to } = range\n\n  if (dispatch) {\n    tr.delete(from, to)\n  }\n\n  return true\n}\n", "import { deleteSelection as originalDeleteSelection } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    deleteSelection: {\n      /**\n       * Delete the selection, if there is one.\n       * @example editor.commands.deleteSelection()\n       */\n      deleteSelection: () => ReturnType\n    }\n  }\n}\n\nexport const deleteSelection: RawCommands['deleteSelection'] = () => ({ state, dispatch }) => {\n  return originalDeleteSelection(state, dispatch)\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    enter: {\n      /**\n       * Trigger enter.\n       * @example editor.commands.enter()\n       */\n      enter: () => ReturnType,\n    }\n  }\n}\n\nexport const enter: RawCommands['enter'] = () => ({ commands }) => {\n  return commands.keyboardShortcut('Enter')\n}\n", "import { exitCode as originalExitCode } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    exitCode: {\n      /**\n       * Exit from a code block.\n       * @example editor.commands.exitCode()\n       */\n      exitCode: () => ReturnType\n    }\n  }\n}\n\nexport const exitCode: RawCommands['exitCode'] = () => ({ state, dispatch }) => {\n  return originalExitCode(state, dispatch)\n}\n", "import { isRegExp } from './isRegExp.js'\n\n/**\n * Check if object1 includes object2\n * @param object1 Object\n * @param object2 Object\n */\nexport function objectIncludes(\n  object1: Record<string, any>,\n  object2: Record<string, any>,\n  options: { strict: boolean } = { strict: true },\n): boolean {\n  const keys = Object.keys(object2)\n\n  if (!keys.length) {\n    return true\n  }\n\n  return keys.every(key => {\n    if (options.strict) {\n      return object2[key] === object1[key]\n    }\n\n    if (isRegExp(object2[key])) {\n      return object2[key].test(object1[key])\n    }\n\n    return object2[key] === object1[key]\n  })\n}\n", "import { Mark as ProseMirrorMark, MarkType, ResolvedPos } from '@tiptap/pm/model'\n\nimport { Range } from '../types.js'\nimport { objectIncludes } from '../utilities/objectIncludes.js'\n\nfunction findMarkInSet(\n  marks: ProseMirrorMark[],\n  type: MarkType,\n  attributes: Record<string, any> = {},\n): ProseMirrorMark | undefined {\n  return marks.find(item => {\n    return (\n      item.type === type\n      && objectIncludes(\n        // Only check equality for the attributes that are provided\n        Object.fromEntries(Object.keys(attributes).map(k => [k, item.attrs[k]])),\n        attributes,\n      )\n    )\n  })\n}\n\nfunction isMarkInSet(\n  marks: ProseMirrorMark[],\n  type: MarkType,\n  attributes: Record<string, any> = {},\n): boolean {\n  return !!findMarkInSet(marks, type, attributes)\n}\n\n/**\n * Get the range of a mark at a resolved position.\n */\nexport function getMarkRange(\n  /**\n   * The position to get the mark range for.\n   */\n  $pos: ResolvedPos,\n  /**\n   * The mark type to get the range for.\n   */\n  type: MarkType,\n  /**\n   * The attributes to match against.\n   * If not provided, only the first mark at the position will be matched.\n   */\n  attributes?: Record<string, any>,\n): Range | void {\n  if (!$pos || !type) {\n    return\n  }\n  let start = $pos.parent.childAfter($pos.parentOffset)\n\n  // If the cursor is at the start of a text node that does not have the mark, look backward\n  if (!start.node || !start.node.marks.some(mark => mark.type === type)) {\n    start = $pos.parent.childBefore($pos.parentOffset)\n  }\n\n  // If there is no text node with the mark even backward, return undefined\n  if (!start.node || !start.node.marks.some(mark => mark.type === type)) {\n    return\n  }\n\n  // Default to only matching against the first mark's attributes\n  attributes = attributes || start.node.marks[0]?.attrs\n\n  // We now know that the cursor is either at the start, middle or end of a text node with the specified mark\n  // so we can look it up on the targeted mark\n  const mark = findMarkInSet([...start.node.marks], type, attributes)\n\n  if (!mark) {\n    return\n  }\n\n  let startIndex = start.index\n  let startPos = $pos.start() + start.offset\n  let endIndex = startIndex + 1\n  let endPos = startPos + start.node.nodeSize\n\n  while (\n    startIndex > 0\n    && isMarkInSet([...$pos.parent.child(startIndex - 1).marks], type, attributes)\n  ) {\n    startIndex -= 1\n    startPos -= $pos.parent.child(startIndex).nodeSize\n  }\n\n  while (\n    endIndex < $pos.parent.childCount\n    && isMarkInSet([...$pos.parent.child(endIndex).marks], type, attributes)\n  ) {\n    endPos += $pos.parent.child(endIndex).nodeSize\n    endIndex += 1\n  }\n\n  return {\n    from: startPos,\n    to: endPos,\n  }\n}\n", "import { MarkType, Schema } from '@tiptap/pm/model'\n\nexport function getMarkType(nameOrType: string | MarkType, schema: Schema): MarkType {\n  if (typeof nameOrType === 'string') {\n    if (!schema.marks[nameOrType]) {\n      throw Error(\n        `There is no mark type named '${nameOrType}'. Maybe you forgot to add the extension?`,\n      )\n    }\n\n    return schema.marks[nameOrType]\n  }\n\n  return nameOrType\n}\n", "import { MarkType } from '@tiptap/pm/model'\nimport { TextSelection } from '@tiptap/pm/state'\n\nimport { getMarkRange } from '../helpers/getMarkRange.js'\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    extendMarkRange: {\n      /**\n       * Extends the text selection to the current mark by type or name.\n       * @param typeOrName The type or name of the mark.\n       * @param attributes The attributes of the mark.\n       * @example editor.commands.extendMarkRange('bold')\n       * @example editor.commands.extendMarkRange('mention', { userId: \"1\" })\n       */\n      extendMarkRange: (\n        /**\n         * The type or name of the mark.\n         */\n        typeOrName: string | MarkType,\n\n        /**\n         * The attributes of the mark.\n         */\n        attributes?: Record<string, any>,\n      ) => ReturnType\n    }\n  }\n}\n\nexport const extendMarkRange: RawCommands['extendMarkRange'] = (typeOrName, attributes = {}) => ({ tr, state, dispatch }) => {\n  const type = getMarkType(typeOrName, state.schema)\n  const { doc, selection } = tr\n  const { $from, from, to } = selection\n\n  if (dispatch) {\n    const range = getMarkRange($from, type, attributes)\n\n    if (range && range.from <= from && range.to >= to) {\n      const newSelection = TextSelection.create(doc, range.from, range.to)\n\n      tr.setSelection(newSelection)\n    }\n  }\n\n  return true\n}\n", "import { Command, CommandProps, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    first: {\n      /**\n       * Runs one command after the other and stops at the first which returns true.\n       * @param commands The commands to run.\n       * @example editor.commands.first([command1, command2])\n       */\n      first: (commands: Command[] | ((props: CommandProps) => Command[])) => ReturnType,\n    }\n  }\n}\n\nexport const first: RawCommands['first'] = commands => props => {\n  const items = typeof commands === 'function'\n    ? commands(props)\n    : commands\n\n  for (let i = 0; i < items.length; i += 1) {\n    if (items[i](props)) {\n      return true\n    }\n  }\n\n  return false\n}\n", "import { TextSelection } from '@tiptap/pm/state'\n\nexport function isTextSelection(value: unknown): value is TextSelection {\n  return value instanceof TextSelection\n}\n", "export function minMax(value = 0, min = 0, max = 0): number {\n  return Math.min(Math.max(value, min), max)\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { Selection, TextSelection } from '@tiptap/pm/state'\n\nimport { FocusPosition } from '../types.js'\nimport { minMax } from '../utilities/minMax.js'\n\nexport function resolveFocusPosition(\n  doc: ProseMirrorNode,\n  position: FocusPosition = null,\n): Selection | null {\n  if (!position) {\n    return null\n  }\n\n  const selectionAtStart = Selection.atStart(doc)\n  const selectionAtEnd = Selection.atEnd(doc)\n\n  if (position === 'start' || position === true) {\n    return selectionAtStart\n  }\n\n  if (position === 'end') {\n    return selectionAtEnd\n  }\n\n  const minPos = selectionAtStart.from\n  const maxPos = selectionAtEnd.to\n\n  if (position === 'all') {\n    return TextSelection.create(\n      doc,\n      minMax(0, minPos, maxPos),\n      minMax(doc.content.size, minPos, maxPos),\n    )\n  }\n\n  return TextSelection.create(\n    doc,\n    minMax(position, minPos, maxPos),\n    minMax(position, minPos, maxPos),\n  )\n}\n", "export function isAndroid(): boolean {\n  return navigator.platform === 'Android' || /android/i.test(navigator.userAgent)\n}\n", "export function isiOS(): boolean {\n  return [\n    'iPad Simulator',\n    'iPhone Simulator',\n    'iPod Simulator',\n    'iPad',\n    'iPhone',\n    'iPod',\n  ].includes(navigator.platform)\n  // iPad on iOS 13 detection\n  || (navigator.userAgent.includes('Mac') && 'ontouchend' in document)\n}\n", "import { isTextSelection } from '../helpers/isTextSelection.js'\nimport { resolveFocusPosition } from '../helpers/resolveFocusPosition.js'\nimport { FocusPosition, RawCommands } from '../types.js'\nimport { isAndroid } from '../utilities/isAndroid.js'\nimport { isiOS } from '../utilities/isiOS.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    focus: {\n      /**\n       * Focus the editor at the given position.\n       * @param position The position to focus at.\n       * @param options.scrollIntoView Scroll the focused position into view after focusing\n       * @example editor.commands.focus()\n       * @example editor.commands.focus(32, { scrollIntoView: false })\n       */\n      focus: (\n        /**\n         * The position to focus at.\n         */\n        position?: FocusPosition,\n\n        /**\n         * Optional options\n         * @default { scrollIntoView: true }\n         */\n        options?: {\n          scrollIntoView?: boolean,\n        },\n      ) => ReturnType,\n    }\n  }\n}\n\nexport const focus: RawCommands['focus'] = (position = null, options = {}) => ({\n  editor,\n  view,\n  tr,\n  dispatch,\n}) => {\n  options = {\n    scrollIntoView: true,\n    ...options,\n  }\n\n  const delayedFocus = () => {\n    // focus within `requestAnimationFrame` breaks focus on iOS and Android\n    // so we have to call this\n    if (isiOS() || isAndroid()) {\n      (view.dom as HTMLElement).focus()\n    }\n\n    // For React we have to focus asynchronously. Otherwise wild things happen.\n    // see: https://github.com/ueberdosis/tiptap/issues/1520\n    requestAnimationFrame(() => {\n      if (!editor.isDestroyed) {\n        view.focus()\n\n        if (options?.scrollIntoView) {\n          editor.commands.scrollIntoView()\n        }\n      }\n    })\n  }\n\n  if ((view.hasFocus() && position === null) || position === false) {\n    return true\n  }\n\n  // we don’t try to resolve a NodeSelection or CellSelection\n  if (dispatch && position === null && !isTextSelection(editor.state.selection)) {\n    delayedFocus()\n    return true\n  }\n\n  // pass through tr.doc instead of editor.state.doc\n  // since transactions could change the editors state before this command has been run\n  const selection = resolveFocusPosition(tr.doc, position) || editor.state.selection\n  const isSameSelection = editor.state.selection.eq(selection)\n\n  if (dispatch) {\n    if (!isSameSelection) {\n      tr.setSelection(selection)\n    }\n\n    // `tr.setSelection` resets the stored marks\n    // so we’ll restore them if the selection is the same as before\n    if (isSameSelection && tr.storedMarks) {\n      tr.setStoredMarks(tr.storedMarks)\n    }\n\n    delayedFocus()\n  }\n\n  return true\n}\n", "import { CommandProps, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    forEach: {\n      /**\n       * Loop through an array of items.\n       */\n      forEach: <T>(\n        items: T[],\n        fn: (\n          item: T,\n          props: CommandProps & {\n            index: number,\n          },\n        ) => boolean,\n      ) => ReturnType,\n    }\n  }\n}\n\nexport const forEach: RawCommands['forEach'] = (items, fn) => props => {\n  return items.every((item, index) => fn(item, { ...props, index }))\n}\n", "import { Fragment, Node as ProseMirrorNode, ParseOptions } from '@tiptap/pm/model'\n\nimport { Content, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    insertContent: {\n      /**\n       * Insert a node or string of HTML at the current position.\n       * @example editor.commands.insertContent('<h1>Example</h1>')\n       * @example editor.commands.insertContent('<h1>Example</h1>', { updateSelection: false })\n       */\n      insertContent: (\n        /**\n         * The ProseMirror content to insert.\n         */\n        value: Content | ProseMirrorNode | Fragment,\n\n        /**\n         * Optional options\n         */\n        options?: {\n          /**\n           * Options for parsing the content.\n           */\n          parseOptions?: ParseOptions;\n\n          /**\n           * Whether to update the selection after inserting the content.\n           */\n          updateSelection?: boolean;\n          applyInputRules?: boolean;\n          applyPasteRules?: boolean;\n        }\n      ) => ReturnType;\n    };\n  }\n}\n\nexport const insertContent: RawCommands['insertContent'] = (value, options) => ({ tr, commands }) => {\n  return commands.insertContentAt(\n    { from: tr.selection.from, to: tr.selection.to },\n    value,\n    options,\n  )\n}\n", "const removeWhitespaces = (node: HTMLElement) => {\n  const children = node.childNodes\n\n  for (let i = children.length - 1; i >= 0; i -= 1) {\n    const child = children[i]\n\n    if (child.nodeType === 3 && child.nodeValue && /^(\\n\\s\\s|\\n)$/.test(child.nodeValue)) {\n      node.removeChild(child)\n    } else if (child.nodeType === 1) {\n      removeWhitespaces(child as HTMLElement)\n    }\n  }\n\n  return node\n}\n\nexport function elementFromString(value: string): HTMLElement {\n  // add a wrapper to preserve leading and trailing whitespace\n  const wrappedValue = `<body>${value}</body>`\n\n  const html = new window.DOMParser().parseFromString(wrappedValue, 'text/html').body\n\n  return removeWhitespaces(html)\n}\n", "import {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  Frag<PERSON>,\n  Node as ProseMirrorNode,\n  ParseOptions,\n  Schema,\n} from '@tiptap/pm/model'\n\nimport { Content } from '../types.js'\nimport { elementFromString } from '../utilities/elementFromString.js'\n\nexport type CreateNodeFromContentOptions = {\n  slice?: boolean\n  parseOptions?: ParseOptions\n  errorOnInvalidContent?: boolean\n}\n\n/**\n * Takes a JSON or HTML content and creates a Prosemirror node or fragment from it.\n * @param content The JSON or HTML content to create the node from\n * @param schema The Prosemirror schema to use for the node\n * @param options Options for the parser\n * @returns The created Prosemirror node or fragment\n */\nexport function createNodeFromContent(\n  content: Content | ProseMirrorNode | Fragment,\n  schema: Schema,\n  options?: CreateNodeFromContentOptions,\n): ProseMirrorNode | Fragment {\n  if (content instanceof ProseMirrorNode || content instanceof Fragment) {\n    return content\n  }\n  options = {\n    slice: true,\n    parseOptions: {},\n    ...options,\n  }\n\n  const isJSONContent = typeof content === 'object' && content !== null\n  const isTextContent = typeof content === 'string'\n\n  if (isJSONContent) {\n    try {\n      const isArrayContent = Array.isArray(content) && content.length > 0\n\n      // if the JSON Content is an array of nodes, create a fragment for each node\n      if (isArrayContent) {\n        return Fragment.fromArray(content.map(item => schema.nodeFromJSON(item)))\n      }\n\n      const node = schema.nodeFromJSON(content)\n\n      if (options.errorOnInvalidContent) {\n        node.check()\n      }\n\n      return node\n    } catch (error) {\n      if (options.errorOnInvalidContent) {\n        throw new Error('[tiptap error]: Invalid JSON content', { cause: error as Error })\n      }\n\n      console.warn('[tiptap warn]: Invalid content.', 'Passed value:', content, 'Error:', error)\n\n      return createNodeFromContent('', schema, options)\n    }\n  }\n\n  if (isTextContent) {\n\n    // Check for invalid content\n    if (options.errorOnInvalidContent) {\n      let hasInvalidContent = false\n      let invalidContent = ''\n\n      // A copy of the current schema with a catch-all node at the end\n      const contentCheckSchema = new Schema({\n        topNode: schema.spec.topNode,\n        marks: schema.spec.marks,\n        // Prosemirror's schemas are executed such that: the last to execute, matches last\n        // This means that we can add a catch-all node at the end of the schema to catch any content that we don't know how to handle\n        nodes: schema.spec.nodes.append({\n          __tiptap__private__unknown__catch__all__node: {\n            content: 'inline*',\n            group: 'block',\n            parseDOM: [\n              {\n                tag: '*',\n                getAttrs: e => {\n                  // If this is ever called, we know that the content has something that we don't know how to handle in the schema\n                  hasInvalidContent = true\n                  // Try to stringify the element for a more helpful error message\n                  invalidContent = typeof e === 'string' ? e : e.outerHTML\n                  return null\n                },\n              },\n            ],\n          },\n        }),\n      })\n\n      if (options.slice) {\n        DOMParser.fromSchema(contentCheckSchema).parseSlice(elementFromString(content), options.parseOptions)\n      } else {\n        DOMParser.fromSchema(contentCheckSchema).parse(elementFromString(content), options.parseOptions)\n      }\n\n      if (options.errorOnInvalidContent && hasInvalidContent) {\n        throw new Error('[tiptap error]: Invalid HTML content', { cause: new Error(`Invalid element found: ${invalidContent}`) })\n      }\n    }\n\n    const parser = DOMParser.fromSchema(schema)\n\n    if (options.slice) {\n      return parser.parseSlice(elementFromString(content), options.parseOptions).content\n    }\n\n    return parser.parse(elementFromString(content), options.parseOptions)\n\n  }\n\n  return createNodeFromContent('', schema, options)\n}\n", "import { Selection, Transaction } from '@tiptap/pm/state'\nimport { ReplaceAroundStep, ReplaceStep } from '@tiptap/pm/transform'\n\n// source: https://github.com/ProseMirror/prosemirror-state/blob/master/src/selection.js#L466\nexport function selectionToInsertionEnd(tr: Transaction, startLen: number, bias: number) {\n  const last = tr.steps.length - 1\n\n  if (last < startLen) {\n    return\n  }\n\n  const step = tr.steps[last]\n\n  if (!(step instanceof ReplaceStep || step instanceof ReplaceAroundStep)) {\n    return\n  }\n\n  const map = tr.mapping.maps[last]\n  let end = 0\n\n  map.forEach((_from, _to, _newFrom, newTo) => {\n    if (end === 0) {\n      end = newTo\n    }\n  })\n\n  tr.setSelection(Selection.near(tr.doc.resolve(end), bias))\n}\n", "import { Fragment, Node as ProseMirrorNode, ParseOptions } from '@tiptap/pm/model'\n\nimport { createNodeFromContent } from '../helpers/createNodeFromContent.js'\nimport { selectionToInsertionEnd } from '../helpers/selectionToInsertionEnd.js'\nimport { Content, Range, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    insertContentAt: {\n      /**\n       * Insert a node or string of HTML at a specific position.\n       * @example editor.commands.insertContentAt(0, '<h1>Example</h1>')\n       */\n      insertContentAt: (\n        /**\n         * The position to insert the content at.\n         */\n        position: number | Range,\n\n        /**\n         * The ProseMirror content to insert.\n         */\n        value: Content | ProseMirrorNode | Fragment,\n\n        /**\n         * Optional options\n         */\n        options?: {\n          /**\n           * Options for parsing the content.\n           */\n          parseOptions?: ParseOptions\n\n          /**\n           * Whether to update the selection after inserting the content.\n           */\n          updateSelection?: boolean\n\n          /**\n           * Whether to apply input rules after inserting the content.\n           */\n          applyInputRules?: boolean\n\n          /**\n           * Whether to apply paste rules after inserting the content.\n           */\n          applyPasteRules?: boolean\n\n          /**\n           * Whether to throw an error if the content is invalid.\n           */\n          errorOnInvalidContent?: boolean\n        },\n      ) => ReturnType\n    }\n  }\n}\n\nconst isFragment = (nodeOrFragment: ProseMirrorNode | Fragment): nodeOrFragment is Fragment => {\n  return !('type' in nodeOrFragment)\n}\n\nexport const insertContentAt: RawCommands['insertContentAt'] = (position, value, options) => ({ tr, dispatch, editor }) => {\n  if (dispatch) {\n    options = {\n      parseOptions: editor.options.parseOptions,\n      updateSelection: true,\n      applyInputRules: false,\n      applyPasteRules: false,\n      ...options,\n    }\n\n    let content: Fragment | ProseMirrorNode\n\n    try {\n      content = createNodeFromContent(value, editor.schema, {\n        parseOptions: {\n          preserveWhitespace: 'full',\n          ...options.parseOptions,\n        },\n        errorOnInvalidContent: options.errorOnInvalidContent ?? editor.options.enableContentCheck,\n      })\n    } catch (e) {\n      editor.emit('contentError', {\n        editor,\n        error: e as Error,\n        disableCollaboration: () => {\n          if (editor.storage.collaboration) {\n            editor.storage.collaboration.isDisabled = true\n          }\n        },\n      })\n      return false\n    }\n\n    let { from, to } = typeof position === 'number' ? { from: position, to: position } : { from: position.from, to: position.to }\n\n    let isOnlyTextContent = true\n    let isOnlyBlockContent = true\n    const nodes = isFragment(content) ? content : [content]\n\n    nodes.forEach(node => {\n      // check if added node is valid\n      node.check()\n\n      isOnlyTextContent = isOnlyTextContent ? node.isText && node.marks.length === 0 : false\n\n      isOnlyBlockContent = isOnlyBlockContent ? node.isBlock : false\n    })\n\n    // check if we can replace the wrapping node by\n    // the newly inserted content\n    // example:\n    // replace an empty paragraph by an inserted image\n    // instead of inserting the image below the paragraph\n    if (from === to && isOnlyBlockContent) {\n      const { parent } = tr.doc.resolve(from)\n      const isEmptyTextBlock = parent.isTextblock && !parent.type.spec.code && !parent.childCount\n\n      if (isEmptyTextBlock) {\n        from -= 1\n        to += 1\n      }\n    }\n\n    let newContent\n\n    // if there is only plain text we have to use `insertText`\n    // because this will keep the current marks\n    if (isOnlyTextContent) {\n      // if value is string, we can use it directly\n      // otherwise if it is an array, we have to join it\n      if (Array.isArray(value)) {\n        newContent = value.map(v => v.text || '').join('')\n      } else if (value instanceof Fragment) {\n        let text = ''\n\n        value.forEach(node => {\n          if (node.text) {\n            text += node.text\n          }\n        })\n\n        newContent = text\n      } else if (typeof value === 'object' && !!value && !!value.text) {\n        newContent = value.text\n      } else {\n        newContent = value as string\n      }\n\n      tr.insertText(newContent, from, to)\n    } else {\n      newContent = content\n\n      tr.replaceWith(from, to, newContent)\n    }\n\n    // set cursor at end of inserted content\n    if (options.updateSelection) {\n      selectionToInsertionEnd(tr, tr.steps.length - 1, -1)\n    }\n\n    if (options.applyInputRules) {\n      tr.setMeta('applyInputRules', { from, text: newContent })\n    }\n\n    if (options.applyPasteRules) {\n      tr.setMeta('applyPasteRules', { from, text: newContent })\n    }\n  }\n\n  return true\n}\n", "import {\n  joinBackward as original<PERSON>oi<PERSON><PERSON><PERSON><PERSON>,\n  joinDown as original<PERSON>oinDown,\n  joinForward as original<PERSON>oin<PERSON><PERSON><PERSON>,\n  joinUp as original<PERSON>oinUp,\n} from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinUp: {\n      /**\n       * Join the selected block or, if there is a text selection, the closest ancestor block of the selection that can be joined, with the sibling above it.\n       * @example editor.commands.joinUp()\n       */\n      joinUp: () => ReturnType\n    }\n    joinDown: {\n      /**\n       * Join the selected block, or the closest ancestor of the selection that can be joined, with the sibling after it.\n       * @example editor.commands.joinDown()\n       */\n      joinDown: () => ReturnType\n    }\n    joinBackward: {\n      /**\n       * If the selection is empty and at the start of a textblock, try to reduce the distance between that block and the one before it—if there's a block directly before it that can be joined, join them.\n       * If not, try to move the selected block closer to the next one in the document structure by lifting it out of its\n       * parent or moving it into a parent of the previous block. Will use the view for accurate (bidi-aware) start-of-textblock detection if given.\n       * @example editor.commands.joinBackward()\n       */\n      joinBackward: () => ReturnType\n    }\n    joinForward: {\n      /**\n       * If the selection is empty and the cursor is at the end of a textblock, try to reduce or remove the boundary between that block and the one after it,\n       * either by joining them or by moving the other block closer to this one in the tree structure.\n       * Will use the view for accurate start-of-textblock detection if given.\n       * @example editor.commands.joinForward()\n       */\n      joinForward: () => ReturnType\n    }\n  }\n}\n\nexport const joinUp: RawCommands['joinUp'] = () => ({ state, dispatch }) => {\n  return originalJoinUp(state, dispatch)\n}\n\nexport const joinDown: RawCommands['joinDown'] = () => ({ state, dispatch }) => {\n  return originalJoinDown(state, dispatch)\n}\n\nexport const joinBackward: RawCommands['joinBackward'] = () => ({ state, dispatch }) => {\n  return originalJoinBackward(state, dispatch)\n}\n\nexport const joinForward: RawCommands['joinForward'] = () => ({ state, dispatch }) => {\n  return originalJoinForward(state, dispatch)\n}\n", "import { joinPoint } from '@tiptap/pm/transform'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinItemBackward: {\n      /**\n       * Join two items backward.\n       * @example editor.commands.joinItemBackward()\n       */\n      joinItemBackward: () => ReturnType\n    }\n  }\n}\n\nexport const joinItemBackward: RawCommands['joinItemBackward'] = () => ({\n  state,\n  dispatch,\n  tr,\n}) => {\n  try {\n    const point = joinPoint(state.doc, state.selection.$from.pos, -1)\n\n    if (point === null || point === undefined) {\n      return false\n    }\n\n    tr.join(point, 2)\n\n    if (dispatch) {\n      dispatch(tr)\n    }\n\n    return true\n  } catch {\n    return false\n  }\n}\n", "import { joinPoint } from '@tiptap/pm/transform'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinItemForward: {\n      /**\n       * Join two items Forwards.\n       * @example editor.commands.joinItemForward()\n       */\n      joinItemForward: () => ReturnType\n    }\n  }\n}\n\nexport const joinItemForward: RawCommands['joinItemForward'] = () => ({\n  state,\n  dispatch,\n  tr,\n}) => {\n  try {\n    const point = joinPoint(state.doc, state.selection.$from.pos, +1)\n\n    if (point === null || point === undefined) {\n      return false\n    }\n\n    tr.join(point, 2)\n\n    if (dispatch) {\n      dispatch(tr)\n    }\n\n    return true\n  } catch {\n    return false\n  }\n}\n", "import { joinTextblockBackward as originalCommand } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinTextblockBackward: {\n      /**\n       * A more limited form of joinBackward that only tries to join the current textblock to the one before it, if the cursor is at the start of a textblock.\n       */\n      joinTextblockBackward: () => ReturnType\n    }\n  }\n}\n\nexport const joinTextblockBackward: RawCommands['joinTextblockBackward'] = () => ({ state, dispatch }) => {\n  return originalCommand(state, dispatch)\n}\n", "import { joinTextblockForward as originalCommand } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinTextblockForward: {\n      /**\n       * A more limited form of joinForward that only tries to join the current textblock to the one after it, if the cursor is at the end of a textblock.\n       */\n      joinTextblockForward: () => ReturnType\n    }\n  }\n}\n\nexport const joinTextblockForward: RawCommands['joinTextblockForward'] = () => ({ state, dispatch }) => {\n  return originalCommand(state, dispatch)\n}\n", "export function isMacOS(): boolean {\n  return typeof navigator !== 'undefined'\n    ? /Mac/.test(navigator.platform)\n    : false\n}\n", "import { RawCommands } from '../types.js'\nimport { isiOS } from '../utilities/isiOS.js'\nimport { isMacOS } from '../utilities/isMacOS.js'\n\nfunction normalizeKeyName(name: string) {\n  const parts = name.split(/-(?!$)/)\n  let result = parts[parts.length - 1]\n\n  if (result === 'Space') {\n    result = ' '\n  }\n\n  let alt\n  let ctrl\n  let shift\n  let meta\n\n  for (let i = 0; i < parts.length - 1; i += 1) {\n    const mod = parts[i]\n\n    if (/^(cmd|meta|m)$/i.test(mod)) {\n      meta = true\n    } else if (/^a(lt)?$/i.test(mod)) {\n      alt = true\n    } else if (/^(c|ctrl|control)$/i.test(mod)) {\n      ctrl = true\n    } else if (/^s(hift)?$/i.test(mod)) {\n      shift = true\n    } else if (/^mod$/i.test(mod)) {\n      if (isiOS() || isMacOS()) {\n        meta = true\n      } else {\n        ctrl = true\n      }\n    } else {\n      throw new Error(`Unrecognized modifier name: ${mod}`)\n    }\n  }\n\n  if (alt) {\n    result = `Alt-${result}`\n  }\n\n  if (ctrl) {\n    result = `Ctrl-${result}`\n  }\n\n  if (meta) {\n    result = `Meta-${result}`\n  }\n\n  if (shift) {\n    result = `Shift-${result}`\n  }\n\n  return result\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    keyboardShortcut: {\n      /**\n       * Trigger a keyboard shortcut.\n       * @param name The name of the keyboard shortcut.\n       * @example editor.commands.keyboardShortcut('Mod-b')\n       */\n      keyboardShortcut: (name: string) => ReturnType,\n    }\n  }\n}\n\nexport const keyboardShortcut: RawCommands['keyboardShortcut'] = name => ({\n  editor,\n  view,\n  tr,\n  dispatch,\n}) => {\n  const keys = normalizeKeyName(name).split(/-(?!$)/)\n  const key = keys.find(item => !['Alt', 'Ctrl', 'Meta', 'Shift'].includes(item))\n  const event = new KeyboardEvent('keydown', {\n    key: key === 'Space'\n      ? ' '\n      : key,\n    altKey: keys.includes('Alt'),\n    ctrlKey: keys.includes('Ctrl'),\n    metaKey: keys.includes('Meta'),\n    shiftKey: keys.includes('Shift'),\n    bubbles: true,\n    cancelable: true,\n  })\n\n  const capturedTransaction = editor.captureTransaction(() => {\n    view.someProp('handleKeyDown', f => f(view, event))\n  })\n\n  capturedTransaction?.steps.forEach(step => {\n    const newStep = step.map(tr.mapping)\n\n    if (newStep && dispatch) {\n      tr.maybeStep(newStep)\n    }\n  })\n\n  return true\n}\n", "import { NodeType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\nimport { NodeRange } from '../types.js'\nimport { objectIncludes } from '../utilities/objectIncludes.js'\nimport { getNodeType } from './getNodeType.js'\n\nexport function isNodeActive(\n  state: EditorState,\n  typeOrName: NodeType | string | null,\n  attributes: Record<string, any> = {},\n): boolean {\n  const { from, to, empty } = state.selection\n  const type = typeOrName ? getNodeType(typeOrName, state.schema) : null\n\n  const nodeRanges: NodeRange[] = []\n\n  state.doc.nodesBetween(from, to, (node, pos) => {\n    if (node.isText) {\n      return\n    }\n\n    const relativeFrom = Math.max(from, pos)\n    const relativeTo = Math.min(to, pos + node.nodeSize)\n\n    nodeRanges.push({\n      node,\n      from: relativeFrom,\n      to: relativeTo,\n    })\n  })\n\n  const selectionRange = to - from\n  const matchedNodeRanges = nodeRanges\n    .filter(nodeRange => {\n      if (!type) {\n        return true\n      }\n\n      return type.name === nodeRange.node.type.name\n    })\n    .filter(nodeRange => objectIncludes(nodeRange.node.attrs, attributes, { strict: false }))\n\n  if (empty) {\n    return !!matchedNodeRanges.length\n  }\n\n  const range = matchedNodeRanges.reduce((sum, nodeRange) => sum + nodeRange.to - nodeRange.from, 0)\n\n  return range >= selectionRange\n}\n", "import { lift as originalLift } from '@tiptap/pm/commands'\nimport { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { isNodeActive } from '../helpers/isNodeActive.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    lift: {\n      /**\n       * Removes an existing wrap if possible lifting the node out of it\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.lift('paragraph')\n       * @example editor.commands.lift('heading', { level: 1 })\n       */\n      lift: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const lift: RawCommands['lift'] = (typeOrName, attributes = {}) => ({ state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n  const isActive = isNodeActive(state, type, attributes)\n\n  if (!isActive) {\n    return false\n  }\n\n  return originalLift(state, dispatch)\n}\n", "import { liftEmptyBlock as originalLiftEmptyBlock } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    liftEmptyBlock: {\n      /**\n       * If the cursor is in an empty textblock that can be lifted, lift the block.\n       * @example editor.commands.liftEmptyBlock()\n       */\n      liftEmptyBlock: () => ReturnType,\n    }\n  }\n}\n\nexport const liftEmptyBlock: RawCommands['liftEmptyBlock'] = () => ({ state, dispatch }) => {\n  return originalLiftEmptyBlock(state, dispatch)\n}\n", "import { NodeType } from '@tiptap/pm/model'\nimport { liftListItem as originalLiftListItem } from '@tiptap/pm/schema-list'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    liftListItem: {\n      /**\n       * Create a command to lift the list item around the selection up into a wrapping list.\n       * @param typeOrName The type or name of the node.\n       * @example editor.commands.liftListItem('listItem')\n       */\n      liftListItem: (typeOrName: string | NodeType) => ReturnType\n    }\n  }\n}\n\nexport const liftListItem: RawCommands['liftListItem'] = typeOrName => ({ state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n\n  return originalLiftListItem(type)(state, dispatch)\n}\n", "import { newlineInCode as originalNewlineInCode } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    newlineInCode: {\n      /**\n       * Add a newline character in code.\n       * @example editor.commands.newlineInCode()\n       */\n      newlineInCode: () => ReturnType\n    }\n  }\n}\n\nexport const newlineInCode: RawCommands['newlineInCode'] = () => ({ state, dispatch }) => {\n  return originalNewlineInCode(state, dispatch)\n}\n", "import { Schema } from '@tiptap/pm/model'\n\n/**\n * Get the type of a schema item by its name.\n * @param name The name of the schema item\n * @param schema The Prosemiror schema to search in\n * @returns The type of the schema item (`node` or `mark`), or null if it doesn't exist\n */\nexport function getSchemaTypeNameByName(name: string, schema: Schema): 'node' | 'mark' | null {\n  if (schema.nodes[name]) {\n    return 'node'\n  }\n\n  if (schema.marks[name]) {\n    return 'mark'\n  }\n\n  return null\n}\n", "/**\n * Remove a property or an array of properties from an object\n * @param obj Object\n * @param key Key to remove\n */\nexport function deleteProps(obj: Record<string, any>, propOrProps: string | string[]): Record<string, any> {\n  const props = typeof propOrProps === 'string'\n    ? [propOrProps]\n    : propOrProps\n\n  return Object\n    .keys(obj)\n    .reduce((newObj: Record<string, any>, prop) => {\n      if (!props.includes(prop)) {\n        newObj[prop] = obj[prop]\n      }\n\n      return newObj\n    }, {})\n}\n", "import { MarkType, NodeType } from '@tiptap/pm/model'\n\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { getSchemaTypeNameByName } from '../helpers/getSchemaTypeNameByName.js'\nimport { RawCommands } from '../types.js'\nimport { deleteProps } from '../utilities/deleteProps.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    resetAttributes: {\n      /**\n       * Resets some node attributes to the default value.\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node to reset.\n       * @example editor.commands.resetAttributes('heading', 'level')\n       */\n      resetAttributes: (\n        typeOrName: string | NodeType | MarkType,\n        attributes: string | string[],\n      ) => ReturnType\n    }\n  }\n}\n\nexport const resetAttributes: RawCommands['resetAttributes'] = (typeOrName, attributes) => ({ tr, state, dispatch }) => {\n  let nodeType: NodeType | null = null\n  let markType: MarkType | null = null\n\n  const schemaType = getSchemaTypeNameByName(\n    typeof typeOrName === 'string' ? typeOrName : typeOrName.name,\n    state.schema,\n  )\n\n  if (!schemaType) {\n    return false\n  }\n\n  if (schemaType === 'node') {\n    nodeType = getNodeType(typeOrName as NodeType, state.schema)\n  }\n\n  if (schemaType === 'mark') {\n    markType = getMarkType(typeOrName as MarkType, state.schema)\n  }\n\n  if (dispatch) {\n    tr.selection.ranges.forEach(range => {\n      state.doc.nodesBetween(range.$from.pos, range.$to.pos, (node, pos) => {\n        if (nodeType && nodeType === node.type) {\n          tr.setNodeMarkup(pos, undefined, deleteProps(node.attrs, attributes))\n        }\n\n        if (markType && node.marks.length) {\n          node.marks.forEach(mark => {\n            if (markType === mark.type) {\n              tr.addMark(\n                pos,\n                pos + node.nodeSize,\n                markType.create(deleteProps(mark.attrs, attributes)),\n              )\n            }\n          })\n        }\n      })\n    })\n  }\n\n  return true\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    scrollIntoView: {\n      /**\n       * Scroll the selection into view.\n       * @example editor.commands.scrollIntoView()\n       */\n      scrollIntoView: () => ReturnType,\n    }\n  }\n}\n\nexport const scrollIntoView: RawCommands['scrollIntoView'] = () => ({ tr, dispatch }) => {\n  if (dispatch) {\n    tr.scrollIntoView()\n  }\n\n  return true\n}\n", "import { AllSelection } from '@tiptap/pm/state'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectAll: {\n      /**\n       * Select the whole document.\n       * @example editor.commands.selectAll()\n       */\n      selectAll: () => ReturnType,\n    }\n  }\n}\n\nexport const selectAll: RawCommands['selectAll'] = () => ({ tr, dispatch }) => {\n  if (dispatch) {\n    const selection = new AllSelection(tr.doc)\n\n    tr.setSelection(selection)\n  }\n\n  return true\n}\n", "import { selectNodeBackward as originalSelectNodeBackward } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectNodeBackward: {\n      /**\n       * Select a node backward.\n       * @example editor.commands.selectNodeBackward()\n       */\n      selectNodeBackward: () => ReturnType\n    }\n  }\n}\n\nexport const selectNodeBackward: RawCommands['selectNodeBackward'] = () => ({ state, dispatch }) => {\n  return originalSelectNodeBackward(state, dispatch)\n}\n", "import { selectNodeForward as originalSelectNodeForward } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectNodeForward: {\n      /**\n       * Select a node forward.\n       * @example editor.commands.selectNodeForward()\n       */\n      selectNodeForward: () => ReturnType\n    }\n  }\n}\n\nexport const selectNodeForward: RawCommands['selectNodeForward'] = () => ({ state, dispatch }) => {\n  return originalSelectNodeForward(state, dispatch)\n}\n", "import { selectParentNode as originalSelectParentNode } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectParentNode: {\n      /**\n       * Select the parent node.\n       * @example editor.commands.selectParentNode()\n       */\n      selectParentNode: () => ReturnType\n    }\n  }\n}\n\nexport const selectParentNode: RawCommands['selectParentNode'] = () => ({ state, dispatch }) => {\n  return originalSelectParentNode(state, dispatch)\n}\n", "// @ts-ignore\n// TODO: add types to @types/prosemirror-commands\nimport { selectTextblockEnd as originalSelectTextblockEnd } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectTextblockEnd: {\n      /**\n       * Moves the cursor to the end of current text block.\n       * @example editor.commands.selectTextblockEnd()\n       */\n      selectTextblockEnd: () => ReturnType\n    }\n  }\n}\n\nexport const selectTextblockEnd: RawCommands['selectTextblockEnd'] = () => ({ state, dispatch }) => {\n  return originalSelectTextblockEnd(state, dispatch)\n}\n", "// @ts-ignore\n// TODO: add types to @types/prosemirror-commands\nimport { selectTextblockStart as originalSelectTextblockStart } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectTextblockStart: {\n      /**\n       * Moves the cursor to the start of current text block.\n       * @example editor.commands.selectTextblockStart()\n       */\n      selectTextblockStart: () => ReturnType\n    }\n  }\n}\n\nexport const selectTextblockStart: RawCommands['selectTextblockStart'] = () => ({ state, dispatch }) => {\n  return originalSelectTextblockStart(state, dispatch)\n}\n", "import {\n  Fragment, Node as ProseMirrorNode, ParseOptions, Schema,\n} from '@tiptap/pm/model'\n\nimport { Content } from '../types.js'\nimport { createNodeFromContent } from './createNodeFromContent.js'\n\n/**\n * Create a new Prosemirror document node from content.\n * @param content The JSON or HTML content to create the document from\n * @param schema The Prosemirror schema to use for the document\n * @param parseOptions Options for the parser\n * @returns The created Prosemirror document node\n */\nexport function createDocument(\n  content: Content | ProseMirrorNode | Fragment,\n  schema: Schema,\n  parseOptions: ParseOptions = {},\n  options: { errorOnInvalidContent?: boolean } = {},\n): ProseMirrorNode {\n  return createNodeFromContent(content, schema, {\n    slice: false,\n    parseOptions,\n    errorOnInvalidContent: options.errorOnInvalidContent,\n  }) as ProseMirrorNode\n}\n", "import { Fragment, Node as ProseMirrorNode, ParseOptions } from '@tiptap/pm/model'\n\nimport { createDocument } from '../helpers/createDocument.js'\nimport { Content, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setContent: {\n      /**\n       * Replace the whole document with new content.\n       * @param content The new content.\n       * @param emitUpdate Whether to emit an update event.\n       * @param parseOptions Options for parsing the content.\n       * @example editor.commands.setContent('<p>Example text</p>')\n       */\n      setContent: (\n        /**\n         * The new content.\n         */\n        content: Content | Fragment | ProseMirrorNode,\n\n        /**\n         * Whether to emit an update event.\n         * @default false\n         */\n        emitUpdate?: boolean,\n\n        /**\n         * Options for parsing the content.\n         * @default {}\n         */\n        parseOptions?: ParseOptions,\n        /**\n         * Options for `setContent`.\n         */\n        options?: {\n          /**\n           * Whether to throw an error if the content is invalid.\n           */\n          errorOnInvalidContent?: boolean;\n        }\n      ) => ReturnType;\n    };\n  }\n}\n\nexport const setContent: RawCommands['setContent'] = (content, emitUpdate = false, parseOptions = {}, options = {}) => ({\n  editor, tr, dispatch, commands,\n}) => {\n  const { doc } = tr\n\n  // This is to keep backward compatibility with the previous behavior\n  // TODO remove this in the next major version\n  if (parseOptions.preserveWhitespace !== 'full') {\n    const document = createDocument(content, editor.schema, parseOptions, {\n      errorOnInvalidContent: options.errorOnInvalidContent ?? editor.options.enableContentCheck,\n    })\n\n    if (dispatch) {\n      tr.replaceWith(0, doc.content.size, document).setMeta('preventUpdate', !emitUpdate)\n    }\n    return true\n  }\n\n  if (dispatch) {\n    tr.setMeta('preventUpdate', !emitUpdate)\n  }\n\n  return commands.insertContentAt({ from: 0, to: doc.content.size }, content, {\n    parseOptions,\n    errorOnInvalidContent: options.errorOnInvalidContent ?? editor.options.enableContentCheck,\n  })\n}\n", "import { Mark, MarkType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\nimport { getMarkType } from './getMarkType.js'\n\nexport function getMarkAttributes(\n  state: EditorState,\n  typeOrName: string | MarkType,\n): Record<string, any> {\n  const type = getMarkType(typeOrName, state.schema)\n  const { from, to, empty } = state.selection\n  const marks: Mark[] = []\n\n  if (empty) {\n    if (state.storedMarks) {\n      marks.push(...state.storedMarks)\n    }\n\n    marks.push(...state.selection.$head.marks())\n  } else {\n    state.doc.nodesBetween(from, to, node => {\n      marks.push(...node.marks)\n    })\n  }\n\n  const mark = marks.find(markItem => markItem.type.name === type.name)\n\n  if (!mark) {\n    return {}\n  }\n\n  return { ...mark.attrs }\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { Transaction } from '@tiptap/pm/state'\nimport { Transform } from '@tiptap/pm/transform'\n\n/**\n * Returns a new `Transform` based on all steps of the passed transactions.\n * @param oldDoc The Prosemirror node to start from\n * @param transactions The transactions to combine\n * @returns A new `Transform` with all steps of the passed transactions\n */\nexport function combineTransactionSteps(\n  oldDoc: ProseMirrorNode,\n  transactions: Transaction[],\n): Transform {\n  const transform = new Transform(oldDoc)\n\n  transactions.forEach(transaction => {\n    transaction.steps.forEach(step => {\n      transform.step(step)\n    })\n  })\n\n  return transform\n}\n", "import { ContentMatch, NodeType } from '@tiptap/pm/model'\n\n/**\n * Gets the default block type at a given match\n * @param match The content match to get the default block type from\n * @returns The default block type or null\n */\nexport function defaultBlockAt(match: ContentMatch): NodeType | null {\n  for (let i = 0; i < match.edgeCount; i += 1) {\n    const { type } = match.edge(i)\n\n    if (type.isTextblock && !type.hasRequiredAttrs()) {\n      return type\n    }\n  }\n\n  return null\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { NodeWithPos, Predicate } from '../types.js'\n\n/**\n * Find children inside a Prosemirror node that match a predicate.\n * @param node The Prosemirror node to search in\n * @param predicate The predicate to match\n * @returns An array of nodes with their positions\n */\nexport function findChildren(node: ProseMirrorNode, predicate: Predicate): NodeWithPos[] {\n  const nodesWithPos: NodeWithPos[] = []\n\n  node.descendants((child, pos) => {\n    if (predicate(child)) {\n      nodesWithPos.push({\n        node: child,\n        pos,\n      })\n    }\n  })\n\n  return nodesWithPos\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { NodeWithPos, Predicate, Range } from '../types.js'\n\n/**\n * Same as `find<PERSON><PERSON>dren` but searches only within a `range`.\n * @param node The Prosemirror node to search in\n * @param range The range to search in\n * @param predicate The predicate to match\n * @returns An array of nodes with their positions\n */\nexport function findChildrenInRange(\n  node: ProseMirrorNode,\n  range: Range,\n  predicate: Predicate,\n): NodeWithPos[] {\n  const nodesWithPos: NodeWithPos[] = []\n\n  // if (range.from === range.to) {\n  //   const nodeAt = node.nodeAt(range.from)\n\n  //   if (nodeAt) {\n  //     nodesWithPos.push({\n  //       node: nodeAt,\n  //       pos: range.from,\n  //     })\n  //   }\n  // }\n\n  node.nodesBetween(range.from, range.to, (child, pos) => {\n    if (predicate(child)) {\n      nodesWithPos.push({\n        node: child,\n        pos,\n      })\n    }\n  })\n\n  return nodesWithPos\n}\n", "import { Node as ProseMirrorNode, ResolvedPos } from '@tiptap/pm/model'\n\nimport { Predicate } from '../types.js'\n\n/**\n * Finds the closest parent node to a resolved position that matches a predicate.\n * @param $pos The resolved position to search from\n * @param predicate The predicate to match\n * @returns The closest parent node to the resolved position that matches the predicate\n * @example ```js\n * findParentNodeClosestToPos($from, node => node.type.name === 'paragraph')\n * ```\n */\nexport function findParentNodeClosestToPos(\n  $pos: ResolvedPos,\n  predicate: Predicate,\n):\n  | {\n      pos: number\n      start: number\n      depth: number\n      node: ProseMirrorNode\n    }\n  | undefined {\n  for (let i = $pos.depth; i > 0; i -= 1) {\n    const node = $pos.node(i)\n\n    if (predicate(node)) {\n      return {\n        pos: i > 0 ? $pos.before(i) : 0,\n        start: $pos.start(i),\n        depth: i,\n        node,\n      }\n    }\n  }\n}\n", "import { Selection } from '@tiptap/pm/state'\n\nimport { Predicate } from '../types.js'\nimport { findParentNodeClosestToPos } from './findParentNodeClosestToPos.js'\n\n/**\n * Finds the closest parent node to the current selection that matches a predicate.\n * @param predicate The predicate to match\n * @returns A command that finds the closest parent node to the current selection that matches the predicate\n * @example ```js\n * findParentNode(node => node.type.name === 'paragraph')\n * ```\n */\nexport function findParentNode(predicate: Predicate) {\n  return (selection: Selection) => findParentNodeClosestToPos(selection.$from, predicate)\n}\n", "import { Schema } from '@tiptap/pm/model'\n\nimport { Editor } from '../Editor.js'\nimport { ExtensionManager } from '../ExtensionManager.js'\nimport { Extensions } from '../types.js'\nimport { getSchemaByResolvedExtensions } from './getSchemaByResolvedExtensions.js'\n\nexport function getSchema(extensions: Extensions, editor?: Editor): Schema {\n  const resolvedExtensions = ExtensionManager.resolve(extensions)\n\n  return getSchemaByResolvedExtensions(resolvedExtensions, editor)\n}\n", "import { Node } from '@tiptap/pm/model'\n\nimport { Extensions, JSONContent } from '../types.js'\nimport { getHTMLFromFragment } from './getHTMLFromFragment.js'\nimport { getSchema } from './getSchema.js'\n\n/**\n * Generate HTML from a JSONContent\n * @param doc The JSONContent to generate HTML from\n * @param extensions The extensions to use for the schema\n * @returns The generated HTML\n */\nexport function generateHTML(doc: JSONContent, extensions: Extensions): string {\n  const schema = getSchema(extensions)\n  const contentNode = Node.fromJSON(schema, doc)\n\n  return getHTMLFromFragment(contentNode.content, schema)\n}\n", "import { DOMParser } from '@tiptap/pm/model'\n\nimport { Extensions } from '../types.js'\nimport { elementFromString } from '../utilities/elementFromString.js'\nimport { getSchema } from './getSchema.js'\n\n/**\n * Generate JSONContent from HTML\n * @param html The HTML to generate J<PERSON><PERSON>ontent from\n * @param extensions The extensions to use for the schema\n * @returns The generated JSONContent\n */\nexport function generateJSON(html: string, extensions: Extensions): Record<string, any> {\n  const schema = getSchema(extensions)\n  const dom = elementFromString(html)\n\n  return DOMParser.fromSchema(schema).parse(dom).toJSON()\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { TextSerializer } from '../types.js'\nimport { getTextBetween } from './getTextBetween.js'\n\n/**\n * Gets the text of a Prosemirror node\n * @param node The Prosemirror node\n * @param options Options for the text serializer & block separator\n * @returns The text of the node\n * @example ```js\n * const text = getText(node, { blockSeparator: '\\n' })\n * ```\n */\nexport function getText(\n  node: ProseMirrorNode,\n  options?: {\n    blockSeparator?: string\n    textSerializers?: Record<string, TextSerializer>\n  },\n) {\n  const range = {\n    from: 0,\n    to: node.content.size,\n  }\n\n  return getTextBetween(node, range, options)\n}\n", "import { Node } from '@tiptap/pm/model'\n\nimport { Extensions, JSONContent, TextSerializer } from '../types.js'\nimport { getSchema } from './getSchema.js'\nimport { getText } from './getText.js'\nimport { getTextSerializersFromSchema } from './getTextSerializersFromSchema.js'\n\n/**\n * Generate raw text from a JSONContent\n * @param doc The JSONContent to generate text from\n * @param extensions The extensions to use for the schema\n * @param options Options for the text generation f.e. blockSeparator or textSerializers\n * @returns The generated text\n */\nexport function generateText(\n  doc: JSONContent,\n  extensions: Extensions,\n  options?: {\n    blockSeparator?: string\n    textSerializers?: Record<string, TextSerializer>\n  },\n): string {\n  const { blockSeparator = '\\n\\n', textSerializers = {} } = options || {}\n  const schema = getSchema(extensions)\n  const contentNode = Node.fromJSON(schema, doc)\n\n  return getText(contentNode, {\n    blockSeparator,\n    textSerializers: {\n      ...getTextSerializersFromSchema(schema),\n      ...textSerializers,\n    },\n  })\n}\n", "import { Node, NodeType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\nimport { getNodeType } from './getNodeType.js'\n\nexport function getNodeAttributes(\n  state: EditorState,\n  typeOrName: string | NodeType,\n): Record<string, any> {\n  const type = getNodeType(typeOrName, state.schema)\n  const { from, to } = state.selection\n  const nodes: Node[] = []\n\n  state.doc.nodesBetween(from, to, node => {\n    nodes.push(node)\n  })\n\n  const node = nodes.reverse().find(nodeItem => nodeItem.type.name === type.name)\n\n  if (!node) {\n    return {}\n  }\n\n  return { ...node.attrs }\n}\n", "import { MarkType, NodeType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\nimport { getMarkAttributes } from './getMarkAttributes.js'\nimport { getNodeAttributes } from './getNodeAttributes.js'\nimport { getSchemaTypeNameByName } from './getSchemaTypeNameByName.js'\n\n/**\n * Get node or mark attributes by type or name on the current editor state\n * @param state The current editor state\n * @param typeOrName The node or mark type or name\n * @returns The attributes of the node or mark or an empty object\n */\nexport function getAttributes(\n  state: EditorState,\n  typeOrName: string | NodeType | MarkType,\n): Record<string, any> {\n  const schemaType = getSchemaTypeNameByName(\n    typeof typeOrName === 'string' ? typeOrName : typeOrName.name,\n    state.schema,\n  )\n\n  if (schemaType === 'node') {\n    return getNodeAttributes(state, typeOrName as NodeType)\n  }\n\n  if (schemaType === 'mark') {\n    return getMarkAttributes(state, typeOrName as MarkType)\n  }\n\n  return {}\n}\n", "/**\n * Removes duplicated values within an array.\n * Supports numbers, strings and objects.\n */\nexport function removeDuplicates<T>(array: T[], by = JSON.stringify): T[] {\n  const seen: Record<any, any> = {}\n\n  return array.filter(item => {\n    const key = by(item)\n\n    return Object.prototype.hasOwnProperty.call(seen, key)\n      ? false\n      : (seen[key] = true)\n  })\n}\n", "import { Step, Transform } from '@tiptap/pm/transform'\n\nimport { Range } from '../types.js'\nimport { removeDuplicates } from '../utilities/removeDuplicates.js'\n\nexport type ChangedRange = {\n  oldRange: Range,\n  newRange: Range,\n}\n\n/**\n * Removes duplicated ranges and ranges that are\n * fully captured by other ranges.\n */\nfunction simplifyChangedRanges(changes: ChangedRange[]): ChangedRange[] {\n  const uniqueChanges = removeDuplicates(changes)\n\n  return uniqueChanges.length === 1\n    ? uniqueChanges\n    : uniqueChanges.filter((change, index) => {\n      const rest = uniqueChanges.filter((_, i) => i !== index)\n\n      return !rest.some(otherChange => {\n        return change.oldRange.from >= otherChange.oldRange.from\n          && change.oldRange.to <= otherChange.oldRange.to\n          && change.newRange.from >= otherChange.newRange.from\n          && change.newRange.to <= otherChange.newRange.to\n      })\n    })\n}\n\n/**\n * Returns a list of changed ranges\n * based on the first and last state of all steps.\n */\nexport function getChangedRanges(transform: Transform): ChangedRange[] {\n  const { mapping, steps } = transform\n  const changes: ChangedRange[] = []\n\n  mapping.maps.forEach((stepMap, index) => {\n    const ranges: Range[] = []\n\n    // This accounts for step changes where no range was actually altered\n    // e.g. when setting a mark, node attribute, etc.\n    // @ts-ignore\n    if (!stepMap.ranges.length) {\n      const { from, to } = steps[index] as Step & {\n        from?: number,\n        to?: number,\n      }\n\n      if (from === undefined || to === undefined) {\n        return\n      }\n\n      ranges.push({ from, to })\n    } else {\n      stepMap.forEach((from, to) => {\n        ranges.push({ from, to })\n      })\n    }\n\n    ranges.forEach(({ from, to }) => {\n      const newStart = mapping.slice(index).map(from, -1)\n      const newEnd = mapping.slice(index).map(to)\n      const oldStart = mapping.invert().map(newStart, -1)\n      const oldEnd = mapping.invert().map(newEnd)\n\n      changes.push({\n        oldRange: {\n          from: oldStart,\n          to: oldEnd,\n        },\n        newRange: {\n          from: newStart,\n          to: newEnd,\n        },\n      })\n    })\n  })\n\n  return simplifyChangedRanges(changes)\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { JSONContent } from '../types.js'\n\ninterface DebugJSONContent extends JSO<PERSON>ontent {\n  from: number\n  to: number\n}\n\nexport function getDebugJSON(node: ProseMirrorNode, startOffset = 0): DebugJSONContent {\n  const isTopNode = node.type === node.type.schema.topNodeType\n  const increment = isTopNode ? 0 : 1\n  const from = startOffset\n  const to = from + node.nodeSize\n  const marks = node.marks.map(mark => {\n    const output: { type: string; attrs?: Record<string, any> } = {\n      type: mark.type.name,\n    }\n\n    if (Object.keys(mark.attrs).length) {\n      output.attrs = { ...mark.attrs }\n    }\n\n    return output\n  })\n  const attrs = { ...node.attrs }\n  const output: DebugJSONContent = {\n    type: node.type.name,\n    from,\n    to,\n  }\n\n  if (Object.keys(attrs).length) {\n    output.attrs = attrs\n  }\n\n  if (marks.length) {\n    output.marks = marks\n  }\n\n  if (node.content.childCount) {\n    output.content = []\n\n    node.forEach((child, offset) => {\n      output.content?.push(getDebugJSON(child, startOffset + offset + increment))\n    })\n  }\n\n  if (node.text) {\n    output.text = node.text\n  }\n\n  return output\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { MarkRange } from '../types.js'\nimport { getMarkRange } from './getMarkRange.js'\n\nexport function getMarksBetween(from: number, to: number, doc: ProseMirrorNode): MarkRange[] {\n  const marks: MarkRange[] = []\n\n  // get all inclusive marks on empty selection\n  if (from === to) {\n    doc\n      .resolve(from)\n      .marks()\n      .forEach(mark => {\n        const $pos = doc.resolve(from)\n        const range = getMarkRange($pos, mark.type)\n\n        if (!range) {\n          return\n        }\n\n        marks.push({\n          mark,\n          ...range,\n        })\n      })\n  } else {\n    doc.nodesBetween(from, to, (node, pos) => {\n      if (!node || node?.nodeSize === undefined) {\n        return\n      }\n\n      marks.push(\n        ...node.marks.map(mark => ({\n          from: pos,\n          to: pos + node.nodeSize,\n          mark,\n        })),\n      )\n    })\n  }\n\n  return marks\n}\n", "import { Node, NodeType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\n/**\n * Finds the first node of a given type or name in the current selection.\n * @param state The editor state.\n * @param typeOrName The node type or name.\n * @param pos The position to start searching from.\n * @param maxDepth The maximum depth to search.\n * @returns The node and the depth as an array.\n */\nexport const getNodeAtPosition = (state: EditorState, typeOrName: string | NodeType, pos: number, maxDepth = 20) => {\n  const $pos = state.doc.resolve(pos)\n\n  let currentDepth = maxDepth\n  let node: Node | null = null\n\n  while (currentDepth > 0 && node === null) {\n    const currentNode = $pos.node(currentDepth)\n\n    if (currentNode?.type.name === typeOrName) {\n      node = currentNode\n    } else {\n      currentDepth -= 1\n    }\n  }\n\n  return [node, currentDepth] as [Node | null, number]\n}\n", "import { ExtensionAttribute } from '../types.js'\n\n/**\n * Return attributes of an extension that should be splitted by keepOnSplit flag\n * @param extensionAttributes Array of extension attributes\n * @param typeName The type of the extension\n * @param attributes The attributes of the extension\n * @returns The splitted attributes\n */\nexport function getSplittedAttributes(\n  extensionAttributes: ExtensionAttribute[],\n  typeName: string,\n  attributes: Record<string, any>,\n): Record<string, any> {\n  return Object.fromEntries(Object\n    .entries(attributes)\n    .filter(([name]) => {\n      const extensionAttribute = extensionAttributes.find(item => {\n        return item.type === typeName && item.name === name\n      })\n\n      if (!extensionAttribute) {\n        return false\n      }\n\n      return extensionAttribute.attribute.keepOnSplit\n    }))\n}\n", "import { MarkType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\nimport { MarkRange } from '../types.js'\nimport { objectIncludes } from '../utilities/objectIncludes.js'\nimport { getMarkType } from './getMarkType.js'\n\nexport function isMarkActive(\n  state: EditorState,\n  typeOrName: MarkType | string | null,\n  attributes: Record<string, any> = {},\n): boolean {\n  const { empty, ranges } = state.selection\n  const type = typeOrName ? getMarkType(typeOrName, state.schema) : null\n\n  if (empty) {\n    return !!(state.storedMarks || state.selection.$from.marks())\n      .filter(mark => {\n        if (!type) {\n          return true\n        }\n\n        return type.name === mark.type.name\n      })\n      .find(mark => objectIncludes(mark.attrs, attributes, { strict: false }))\n  }\n\n  let selectionRange = 0\n  const markRanges: MarkRange[] = []\n\n  ranges.forEach(({ $from, $to }) => {\n    const from = $from.pos\n    const to = $to.pos\n\n    state.doc.nodesBetween(from, to, (node, pos) => {\n      if (!node.isText && !node.marks.length) {\n        return\n      }\n\n      const relativeFrom = Math.max(from, pos)\n      const relativeTo = Math.min(to, pos + node.nodeSize)\n      const range = relativeTo - relativeFrom\n\n      selectionRange += range\n\n      markRanges.push(\n        ...node.marks.map(mark => ({\n          mark,\n          from: relativeFrom,\n          to: relativeTo,\n        })),\n      )\n    })\n  })\n\n  if (selectionRange === 0) {\n    return false\n  }\n\n  // calculate range of matched mark\n  const matchedRange = markRanges\n    .filter(markRange => {\n      if (!type) {\n        return true\n      }\n\n      return type.name === markRange.mark.type.name\n    })\n    .filter(markRange => objectIncludes(markRange.mark.attrs, attributes, { strict: false }))\n    .reduce((sum, markRange) => sum + markRange.to - markRange.from, 0)\n\n  // calculate range of marks that excludes the searched mark\n  // for example `code` doesn’t allow any other marks\n  const excludedRange = markRanges\n    .filter(markRange => {\n      if (!type) {\n        return true\n      }\n\n      return markRange.mark.type !== type && markRange.mark.type.excludes(type)\n    })\n    .reduce((sum, markRange) => sum + markRange.to - markRange.from, 0)\n\n  // we only include the result of `excludedRange`\n  // if there is a match at all\n  const range = matchedRange > 0 ? matchedRange + excludedRange : matchedRange\n\n  return range >= selectionRange\n}\n", "import { EditorState } from '@tiptap/pm/state'\n\nimport { getSchemaTypeNameByName } from './getSchemaTypeNameByName.js'\nimport { isMarkActive } from './isMarkActive.js'\nimport { isNodeActive } from './isNodeActive.js'\n\nexport function isActive(\n  state: EditorState,\n  name: string | null,\n  attributes: Record<string, any> = {},\n): boolean {\n  if (!name) {\n    return isNodeActive(state, null, attributes) || isMarkActive(state, null, attributes)\n  }\n\n  const schemaType = getSchemaTypeNameByName(name, state.schema)\n\n  if (schemaType === 'node') {\n    return isNodeActive(state, name, attributes)\n  }\n\n  if (schemaType === 'mark') {\n    return isMarkActive(state, name, attributes)\n  }\n\n  return false\n}\n", "import { EditorState } from '@tiptap/pm/state'\n\nimport { findParentNode } from './findParentNode.js'\n\nexport const isAtEndOfNode = (state: EditorState, nodeType?: string) => {\n  const { $from, $to, $anchor } = state.selection\n\n  if (nodeType) {\n    const parentNode = findParentNode(node => node.type.name === nodeType)(state.selection)\n\n    if (!parentNode) {\n      return false\n    }\n\n    const $parentPos = state.doc.resolve(parentNode.pos + 1)\n\n    if ($anchor.pos + 1 === $parentPos.end()) {\n      return true\n    }\n\n    return false\n  }\n\n  if ($to.parentOffset < $to.parent.nodeSize - 2 || $from.pos !== $to.pos) {\n    return false\n  }\n\n  return true\n}\n", "import { EditorState } from '@tiptap/pm/state'\n\nexport const isAtStartOfNode = (state: EditorState) => {\n  const { $from, $to } = state.selection\n\n  if ($from.parentOffset > 0 || $from.pos !== $to.pos) {\n    return false\n  }\n\n  return true\n}\n", "import { getExtensionField } from '../helpers/getExtensionField.js'\nimport { NodeConfig } from '../index.js'\nimport { Extensions } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\nimport { splitExtensions } from './splitExtensions.js'\n\nexport function isList(name: string, extensions: Extensions): boolean {\n  const { nodeExtensions } = splitExtensions(extensions)\n  const extension = nodeExtensions.find(item => item.name === name)\n\n  if (!extension) {\n    return false\n  }\n\n  const context = {\n    name: extension.name,\n    options: extension.options,\n    storage: extension.storage,\n  }\n  const group = callOrReturn(getExtensionField<NodeConfig['group']>(extension, 'group', context))\n\n  if (typeof group !== 'string') {\n    return false\n  }\n\n  return group.split(' ').includes('list')\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\n/**\n * Returns true if the given prosemirror node is empty.\n */\nexport function isNodeEmpty(\n  node: ProseMirrorNode,\n  {\n    checkChildren = true,\n    ignoreWhitespace = false,\n  }: {\n    /**\n     * When true (default), it will also check if all children are empty.\n     */\n    checkChildren?: boolean;\n    /**\n     * When true, it will ignore whitespace when checking for emptiness.\n     */\n    ignoreWhitespace?: boolean;\n  } = {},\n): boolean {\n  if (ignoreWhitespace) {\n    if (node.type.name === 'hardBreak') {\n      // Hard breaks are considered empty\n      return true\n    }\n    if (node.isText) {\n      return /^\\s*$/m.test(node.text ?? '')\n    }\n  }\n\n  if (node.isText) {\n    return !node.text\n  }\n\n  if (node.isAtom || node.isLeaf) {\n    return false\n  }\n\n  if (node.content.childCount === 0) {\n    return true\n  }\n\n  if (checkChildren) {\n    let isContentEmpty = true\n\n    node.content.forEach(childNode => {\n      if (isContentEmpty === false) {\n        // Exit early for perf\n        return\n      }\n\n      if (!isNodeEmpty(childNode, { ignoreWhitespace, checkChildren })) {\n        isContentEmpty = false\n      }\n    })\n\n    return isContentEmpty\n  }\n\n  return false\n}\n", "import { NodeSelection } from '@tiptap/pm/state'\n\nexport function isNodeSelection(value: unknown): value is NodeSelection {\n  return value instanceof NodeSelection\n}\n", "import { EditorView } from '@tiptap/pm/view'\n\nimport { minMax } from '../utilities/minMax.js'\n\nexport function posToDOMRect(view: EditorView, from: number, to: number): DOMRect {\n  const minPos = 0\n  const maxPos = view.state.doc.content.size\n  const resolvedFrom = minMax(from, minPos, maxPos)\n  const resolvedEnd = minMax(to, minPos, maxPos)\n  const start = view.coordsAtPos(resolvedFrom)\n  const end = view.coordsAtPos(resolvedEnd, -1)\n  const top = Math.min(start.top, end.top)\n  const bottom = Math.max(start.bottom, end.bottom)\n  const left = Math.min(start.left, end.left)\n  const right = Math.max(start.right, end.right)\n  const width = right - left\n  const height = bottom - top\n  const x = left\n  const y = top\n  const data = {\n    top,\n    bottom,\n    left,\n    right,\n    width,\n    height,\n    x,\n    y,\n  }\n\n  return {\n    ...data,\n    toJSON: () => data,\n  }\n}\n", "import type { Schema } from '@tiptap/pm/model'\n\nimport type { JSONContent } from '../types.js'\n\ntype RewriteUnknownContentOptions = {\n  /**\n   * If true, unknown nodes will be treated as paragraphs\n   * @default true\n   */\n  fallbackToParagraph?: boolean;\n};\n\ntype RewrittenContent = {\n  /**\n   * The original JSON content that was rewritten\n   */\n  original: JSONContent;\n  /**\n   * The name of the node or mark that was unsupported\n   */\n  unsupported: string;\n}[];\n\n/**\n * The actual implementation of the rewriteUnknownContent function\n */\nfunction rewriteUnknownContentInner({\n  json,\n  validMarks,\n  validNodes,\n  options,\n  rewrittenContent = [],\n}: {\n  json: JSONContent;\n  validMarks: Set<string>;\n  validNodes: Set<string>;\n  options?: RewriteUnknownContentOptions;\n  rewrittenContent?: RewrittenContent;\n}): {\n  /**\n   * The cleaned JSON content\n   */\n  json: JSONContent | null;\n  /**\n   * The array of nodes and marks that were rewritten\n   */\n  rewrittenContent: RewrittenContent;\n} {\n  if (json.marks && Array.isArray(json.marks)) {\n    json.marks = json.marks.filter(mark => {\n      const name = typeof mark === 'string' ? mark : mark.type\n\n      if (validMarks.has(name)) {\n        return true\n      }\n\n      rewrittenContent.push({\n        original: JSON.parse(JSON.stringify(mark)),\n        unsupported: name,\n      })\n      // Just ignore any unknown marks\n      return false\n    })\n  }\n\n  if (json.content && Array.isArray(json.content)) {\n    json.content = json.content\n      .map(\n        value => rewriteUnknownContentInner({\n          json: value,\n          validMarks,\n          validNodes,\n          options,\n          rewrittenContent,\n        }).json,\n      )\n      .filter(a => a !== null && a !== undefined)\n  }\n\n  if (json.type && !validNodes.has(json.type)) {\n    rewrittenContent.push({\n      original: JSON.parse(JSON.stringify(json)),\n      unsupported: json.type,\n    })\n\n    if (json.content && Array.isArray(json.content) && (options?.fallbackToParagraph !== false)) {\n      // Just treat it like a paragraph and hope for the best\n      json.type = 'paragraph'\n\n      return {\n        json,\n        rewrittenContent,\n      }\n    }\n\n    // or just omit it entirely\n    return {\n      json: null,\n      rewrittenContent,\n    }\n  }\n\n  return { json, rewrittenContent }\n}\n\n/**\n * Rewrite unknown nodes and marks within JSON content\n * Allowing for user within the editor\n */\nexport function rewriteUnknownContent(\n  /**\n   * The JSON content to clean of unknown nodes and marks\n   */\n  json: JSONContent,\n  /**\n   * The schema to use for validation\n   */\n  schema: Schema,\n  /**\n   * Options for the cleaning process\n   */\n  options?: RewriteUnknownContentOptions,\n): {\n  /**\n   * The cleaned JSON content\n   */\n  json: JSONContent | null;\n  /**\n   * The array of nodes and marks that were rewritten\n   */\n  rewrittenContent: {\n    /**\n     * The original JSON content that was rewritten\n     */\n    original: JSONContent;\n    /**\n     * The name of the node or mark that was unsupported\n     */\n    unsupported: string;\n  }[];\n} {\n  return rewriteUnknownContentInner({\n    json,\n    validNodes: new Set(Object.keys(schema.nodes)),\n    validMarks: new Set(Object.keys(schema.marks)),\n    options,\n  })\n}\n", "import { MarkType, ResolvedPos } from '@tiptap/pm/model'\nimport { EditorState, Transaction } from '@tiptap/pm/state'\n\nimport { getMarkAttributes } from '../helpers/getMarkAttributes.js'\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { isTextSelection } from '../helpers/index.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setMark: {\n      /**\n       * Add a mark with new attributes.\n       * @param typeOrName The mark type or name.\n       * @example editor.commands.setMark('bold', { level: 1 })\n       */\n      setMark: (typeOrName: string | MarkType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nfunction canSetMark(state: EditorState, tr: Transaction, newMarkType: MarkType) {\n  const { selection } = tr\n  let cursor: ResolvedPos | null = null\n\n  if (isTextSelection(selection)) {\n    cursor = selection.$cursor\n  }\n\n  if (cursor) {\n    const currentMarks = state.storedMarks ?? cursor.marks()\n\n    // There can be no current marks that exclude the new mark\n    return (\n      !!newMarkType.isInSet(currentMarks)\n      || !currentMarks.some(mark => mark.type.excludes(newMarkType))\n    )\n  }\n\n  const { ranges } = selection\n\n  return ranges.some(({ $from, $to }) => {\n    let someNodeSupportsMark = $from.depth === 0\n      ? state.doc.inlineContent && state.doc.type.allowsMarkType(newMarkType)\n      : false\n\n    state.doc.nodesBetween($from.pos, $to.pos, (node, _pos, parent) => {\n      // If we already found a mark that we can enable, return false to bypass the remaining search\n      if (someNodeSupportsMark) {\n        return false\n      }\n\n      if (node.isInline) {\n        const parentAllowsMarkType = !parent || parent.type.allowsMarkType(newMarkType)\n        const currentMarksAllowMarkType = !!newMarkType.isInSet(node.marks)\n          || !node.marks.some(otherMark => otherMark.type.excludes(newMarkType))\n\n        someNodeSupportsMark = parentAllowsMarkType && currentMarksAllowMarkType\n      }\n      return !someNodeSupportsMark\n    })\n\n    return someNodeSupportsMark\n  })\n}\nexport const setMark: RawCommands['setMark'] = (typeOrName, attributes = {}) => ({ tr, state, dispatch }) => {\n  const { selection } = tr\n  const { empty, ranges } = selection\n  const type = getMarkType(typeOrName, state.schema)\n\n  if (dispatch) {\n    if (empty) {\n      const oldAttributes = getMarkAttributes(state, type)\n\n      tr.addStoredMark(\n        type.create({\n          ...oldAttributes,\n          ...attributes,\n        }),\n      )\n    } else {\n      ranges.forEach(range => {\n        const from = range.$from.pos\n        const to = range.$to.pos\n\n        state.doc.nodesBetween(from, to, (node, pos) => {\n          const trimmedFrom = Math.max(pos, from)\n          const trimmedTo = Math.min(pos + node.nodeSize, to)\n          const someHasMark = node.marks.find(mark => mark.type === type)\n\n          // if there is already a mark of this type\n          // we know that we have to merge its attributes\n          // otherwise we add a fresh new mark\n          if (someHasMark) {\n            node.marks.forEach(mark => {\n              if (type === mark.type) {\n                tr.addMark(\n                  trimmedFrom,\n                  trimmedTo,\n                  type.create({\n                    ...mark.attrs,\n                    ...attributes,\n                  }),\n                )\n              }\n            })\n          } else {\n            tr.addMark(trimmedFrom, trimmedTo, type.create(attributes))\n          }\n        })\n      })\n    }\n  }\n\n  return canSetMark(state, tr, type)\n}\n", "import type { Plug<PERSON>, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setMeta: {\n      /**\n       * Store a metadata property in the current transaction.\n       * @param key The key of the metadata property.\n       * @param value The value to store.\n       * @example editor.commands.setMeta('foo', 'bar')\n       */\n      setMeta: (key: string | Plugin | PluginKey, value: any) => ReturnType,\n    }\n  }\n}\n\nexport const setMeta: RawCommands['setMeta'] = (key, value) => ({ tr }) => {\n  tr.setMeta(key, value)\n\n  return true\n}\n", "import { setBlockType } from '@tiptap/pm/commands'\nimport { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setNode: {\n      /**\n       * Replace a given range with a node.\n       * @param typeOrName The type or name of the node\n       * @param attributes The attributes of the node\n       * @example editor.commands.setNode('paragraph')\n       */\n      setNode: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const setNode: RawCommands['setNode'] = (typeOrName, attributes = {}) => ({ state, dispatch, chain }) => {\n  const type = getNodeType(typeOrName, state.schema)\n\n  let attributesToCopy: Record<string, any> | undefined\n\n  if (state.selection.$anchor.sameParent(state.selection.$head)) {\n    // only copy attributes if the selection is pointing to a node of the same type\n    attributesToCopy = state.selection.$anchor.parent.attrs\n  }\n\n  // TODO: use a fallback like insertContent?\n  if (!type.isTextblock) {\n    console.warn('[tiptap warn]: Currently \"setNode()\" only supports text block nodes.')\n\n    return false\n  }\n\n  return (\n    chain()\n    // try to convert node to default node if needed\n      .command(({ commands }) => {\n        const canSetBlock = setBlockType(type, { ...attributesToCopy, ...attributes })(state)\n\n        if (canSetBlock) {\n          return true\n        }\n\n        return commands.clearNodes()\n      })\n      .command(({ state: updatedState }) => {\n        return setBlockType(type, { ...attributesToCopy, ...attributes })(updatedState, dispatch)\n      })\n      .run()\n  )\n}\n", "import { NodeSelection } from '@tiptap/pm/state'\n\nimport { RawCommands } from '../types.js'\nimport { minMax } from '../utilities/minMax.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setNodeSelection: {\n      /**\n       * Creates a NodeSelection.\n       * @param position - Position of the node.\n       * @example editor.commands.setNodeSelection(10)\n       */\n      setNodeSelection: (position: number) => ReturnType\n    }\n  }\n}\n\nexport const setNodeSelection: RawCommands['setNodeSelection'] = position => ({ tr, dispatch }) => {\n  if (dispatch) {\n    const { doc } = tr\n    const from = minMax(position, 0, doc.content.size)\n    const selection = NodeSelection.create(doc, from)\n\n    tr.setSelection(selection)\n  }\n\n  return true\n}\n", "import { TextSelection } from '@tiptap/pm/state'\n\nimport { Range, RawCommands } from '../types.js'\nimport { minMax } from '../utilities/minMax.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setTextSelection: {\n      /**\n       * Creates a TextSelection.\n       * @param position The position of the selection.\n       * @example editor.commands.setTextSelection(10)\n       */\n      setTextSelection: (position: number | Range) => ReturnType\n    }\n  }\n}\n\nexport const setTextSelection: RawCommands['setTextSelection'] = position => ({ tr, dispatch }) => {\n  if (dispatch) {\n    const { doc } = tr\n    const { from, to } = typeof position === 'number' ? { from: position, to: position } : position\n    const minPos = TextSelection.atStart(doc).from\n    const maxPos = TextSelection.atEnd(doc).to\n    const resolvedFrom = minMax(from, minPos, maxPos)\n    const resolvedEnd = minMax(to, minPos, maxPos)\n    const selection = TextSelection.create(doc, resolvedFrom, resolvedEnd)\n\n    tr.setSelection(selection)\n  }\n\n  return true\n}\n", "import { NodeType } from '@tiptap/pm/model'\nimport { sinkListItem as originalSinkListItem } from '@tiptap/pm/schema-list'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    sinkListItem: {\n      /**\n       * Sink the list item down into an inner list.\n       * @param typeOrName The type or name of the node.\n       * @example editor.commands.sinkListItem('listItem')\n       */\n      sinkListItem: (typeOrName: string | NodeType) => ReturnType\n    }\n  }\n}\n\nexport const sinkListItem: RawCommands['sinkListItem'] = typeOrName => ({ state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n\n  return originalSinkListItem(type)(state, dispatch)\n}\n", "import { EditorState, NodeSelection, TextSelection } from '@tiptap/pm/state'\nimport { canSplit } from '@tiptap/pm/transform'\n\nimport { defaultBlockAt } from '../helpers/defaultBlockAt.js'\nimport { getSplittedAttributes } from '../helpers/getSplittedAttributes.js'\nimport { RawCommands } from '../types.js'\n\nfunction ensureMarks(state: EditorState, splittableMarks?: string[]) {\n  const marks = state.storedMarks || (state.selection.$to.parentOffset && state.selection.$from.marks())\n\n  if (marks) {\n    const filteredMarks = marks.filter(mark => splittableMarks?.includes(mark.type.name))\n\n    state.tr.ensureMarks(filteredMarks)\n  }\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    splitBlock: {\n      /**\n       * Forks a new node from an existing node.\n       * @param options.keepMarks Keep marks from the previous node.\n       * @example editor.commands.splitBlock()\n       * @example editor.commands.splitBlock({ keepMarks: true })\n       */\n      splitBlock: (options?: { keepMarks?: boolean }) => ReturnType\n    }\n  }\n}\n\nexport const splitBlock: RawCommands['splitBlock'] = ({ keepMarks = true } = {}) => ({\n  tr, state, dispatch, editor,\n}) => {\n  const { selection, doc } = tr\n  const { $from, $to } = selection\n  const extensionAttributes = editor.extensionManager.attributes\n  const newAttributes = getSplittedAttributes(\n    extensionAttributes,\n    $from.node().type.name,\n    $from.node().attrs,\n  )\n\n  if (selection instanceof NodeSelection && selection.node.isBlock) {\n    if (!$from.parentOffset || !canSplit(doc, $from.pos)) {\n      return false\n    }\n\n    if (dispatch) {\n      if (keepMarks) {\n        ensureMarks(state, editor.extensionManager.splittableMarks)\n      }\n\n      tr.split($from.pos).scrollIntoView()\n    }\n\n    return true\n  }\n\n  if (!$from.parent.isBlock) {\n    return false\n  }\n\n  const atEnd = $to.parentOffset === $to.parent.content.size\n\n  const deflt = $from.depth === 0\n    ? undefined\n    : defaultBlockAt($from.node(-1).contentMatchAt($from.indexAfter(-1)))\n\n  let types = atEnd && deflt\n    ? [\n      {\n        type: deflt,\n        attrs: newAttributes,\n      },\n    ]\n    : undefined\n\n  let can = canSplit(tr.doc, tr.mapping.map($from.pos), 1, types)\n\n  if (\n    !types\n      && !can\n      && canSplit(tr.doc, tr.mapping.map($from.pos), 1, deflt ? [{ type: deflt }] : undefined)\n  ) {\n    can = true\n    types = deflt\n      ? [\n        {\n          type: deflt,\n          attrs: newAttributes,\n        },\n      ]\n      : undefined\n  }\n\n  if (dispatch) {\n    if (can) {\n      if (selection instanceof TextSelection) {\n        tr.deleteSelection()\n      }\n\n      tr.split(tr.mapping.map($from.pos), 1, types)\n\n      if (deflt && !atEnd && !$from.parentOffset && $from.parent.type !== deflt) {\n        const first = tr.mapping.map($from.before())\n        const $first = tr.doc.resolve(first)\n\n        if ($from.node(-1).canReplaceWith($first.index(), $first.index() + 1, deflt)) {\n          tr.setNodeMarkup(tr.mapping.map($from.before()), deflt)\n        }\n      }\n    }\n\n    if (keepMarks) {\n      ensureMarks(state, editor.extensionManager.splittableMarks)\n    }\n\n    tr.scrollIntoView()\n  }\n\n  return can\n}\n", "import {\n  Fragment, Node as ProseMirrorNode, NodeType, Slice,\n} from '@tiptap/pm/model'\nimport { TextSelection } from '@tiptap/pm/state'\nimport { canSplit } from '@tiptap/pm/transform'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { getSplittedAttributes } from '../helpers/getSplittedAttributes.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    splitListItem: {\n      /**\n       * Splits one list item into two list items.\n       * @param typeOrName The type or name of the node.\n       * @param overrideAttrs The attributes to ensure on the new node.\n       * @example editor.commands.splitListItem('listItem')\n       */\n      splitListItem: (typeOrName: string | NodeType, overrideAttrs?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const splitListItem: RawCommands['splitListItem'] = (typeOrName, overrideAttrs = {}) => ({\n  tr, state, dispatch, editor,\n}) => {\n  const type = getNodeType(typeOrName, state.schema)\n  const { $from, $to } = state.selection\n\n  // @ts-ignore\n  // eslint-disable-next-line\n    const node: ProseMirrorNode = state.selection.node\n\n  if ((node && node.isBlock) || $from.depth < 2 || !$from.sameParent($to)) {\n    return false\n  }\n\n  const grandParent = $from.node(-1)\n\n  if (grandParent.type !== type) {\n    return false\n  }\n\n  const extensionAttributes = editor.extensionManager.attributes\n\n  if ($from.parent.content.size === 0 && $from.node(-1).childCount === $from.indexAfter(-1)) {\n    // In an empty block. If this is a nested list, the wrapping\n    // list item should be split. Otherwise, bail out and let next\n    // command handle lifting.\n    if (\n      $from.depth === 2\n        || $from.node(-3).type !== type\n        || $from.index(-2) !== $from.node(-2).childCount - 1\n    ) {\n      return false\n    }\n\n    if (dispatch) {\n      let wrap = Fragment.empty\n      // eslint-disable-next-line\n        const depthBefore = $from.index(-1) ? 1 : $from.index(-2) ? 2 : 3\n\n      // Build a fragment containing empty versions of the structure\n      // from the outer list item to the parent node of the cursor\n      for (let d = $from.depth - depthBefore; d >= $from.depth - 3; d -= 1) {\n        wrap = Fragment.from($from.node(d).copy(wrap))\n      }\n\n      // eslint-disable-next-line\n        const depthAfter = $from.indexAfter(-1) < $from.node(-2).childCount ? 1 : $from.indexAfter(-2) < $from.node(-3).childCount ? 2 : 3\n\n      // Add a second list item with an empty default start node\n      const newNextTypeAttributes = {\n        ...getSplittedAttributes(\n          extensionAttributes,\n          $from.node().type.name,\n          $from.node().attrs,\n        ),\n        ...overrideAttrs,\n      }\n      const nextType = type.contentMatch.defaultType?.createAndFill(newNextTypeAttributes) || undefined\n\n      wrap = wrap.append(Fragment.from(type.createAndFill(null, nextType) || undefined))\n\n      const start = $from.before($from.depth - (depthBefore - 1))\n\n      tr.replace(start, $from.after(-depthAfter), new Slice(wrap, 4 - depthBefore, 0))\n\n      let sel = -1\n\n      tr.doc.nodesBetween(start, tr.doc.content.size, (n, pos) => {\n        if (sel > -1) {\n          return false\n        }\n\n        if (n.isTextblock && n.content.size === 0) {\n          sel = pos + 1\n        }\n      })\n\n      if (sel > -1) {\n        tr.setSelection(TextSelection.near(tr.doc.resolve(sel)))\n      }\n\n      tr.scrollIntoView()\n    }\n\n    return true\n  }\n\n  const nextType = $to.pos === $from.end() ? grandParent.contentMatchAt(0).defaultType : null\n\n  const newTypeAttributes = {\n    ...getSplittedAttributes(\n      extensionAttributes,\n      grandParent.type.name,\n      grandParent.attrs,\n    ),\n    ...overrideAttrs,\n  }\n  const newNextTypeAttributes = {\n    ...getSplittedAttributes(\n      extensionAttributes,\n      $from.node().type.name,\n      $from.node().attrs,\n    ),\n    ...overrideAttrs,\n  }\n\n  tr.delete($from.pos, $to.pos)\n\n  const types = nextType\n    ? [\n      { type, attrs: newTypeAttributes },\n      { type: nextType, attrs: newNextTypeAttributes },\n    ]\n    : [{ type, attrs: newTypeAttributes }]\n\n  if (!canSplit(tr.doc, $from.pos, 2)) {\n    return false\n  }\n\n  if (dispatch) {\n    const { selection, storedMarks } = state\n    const { splittableMarks } = editor.extensionManager\n    const marks = storedMarks || (selection.$to.parentOffset && selection.$from.marks())\n\n    tr.split($from.pos, 2, types).scrollIntoView()\n\n    if (!marks || !dispatch) {\n      return true\n    }\n\n    const filteredMarks = marks.filter(mark => splittableMarks.includes(mark.type.name))\n\n    tr.ensureMarks(filteredMarks)\n  }\n\n  return true\n}\n", "import { NodeType } from '@tiptap/pm/model'\nimport { Transaction } from '@tiptap/pm/state'\nimport { canJoin } from '@tiptap/pm/transform'\n\nimport { findParentNode } from '../helpers/findParentNode.js'\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { isList } from '../helpers/isList.js'\nimport { RawCommands } from '../types.js'\n\nconst joinListBackwards = (tr: Transaction, listType: NodeType): boolean => {\n  const list = findParentNode(node => node.type === listType)(tr.selection)\n\n  if (!list) {\n    return true\n  }\n\n  const before = tr.doc.resolve(Math.max(0, list.pos - 1)).before(list.depth)\n\n  if (before === undefined) {\n    return true\n  }\n\n  const nodeBefore = tr.doc.nodeAt(before)\n  const canJoinBackwards = list.node.type === nodeBefore?.type && canJoin(tr.doc, list.pos)\n\n  if (!canJoinBackwards) {\n    return true\n  }\n\n  tr.join(list.pos)\n\n  return true\n}\n\nconst joinListForwards = (tr: Transaction, listType: NodeType): boolean => {\n  const list = findParentNode(node => node.type === listType)(tr.selection)\n\n  if (!list) {\n    return true\n  }\n\n  const after = tr.doc.resolve(list.start).after(list.depth)\n\n  if (after === undefined) {\n    return true\n  }\n\n  const nodeAfter = tr.doc.nodeAt(after)\n  const canJoinForwards = list.node.type === nodeAfter?.type && canJoin(tr.doc, after)\n\n  if (!canJoinForwards) {\n    return true\n  }\n\n  tr.join(after)\n\n  return true\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    toggleList: {\n      /**\n       * Toggle between different list types.\n       * @param listTypeOrName The type or name of the list.\n       * @param itemTypeOrName The type or name of the list item.\n       * @param keepMarks Keep marks when toggling.\n       * @param attributes Attributes for the new list.\n       * @example editor.commands.toggleList('bulletList', 'listItem')\n       */\n      toggleList: (listTypeOrName: string | NodeType, itemTypeOrName: string | NodeType, keepMarks?: boolean, attributes?: Record<string, any>) => ReturnType;\n    }\n  }\n}\n\nexport const toggleList: RawCommands['toggleList'] = (listTypeOrName, itemTypeOrName, keepMarks, attributes = {}) => ({\n  editor, tr, state, dispatch, chain, commands, can,\n}) => {\n  const { extensions, splittableMarks } = editor.extensionManager\n  const listType = getNodeType(listTypeOrName, state.schema)\n  const itemType = getNodeType(itemTypeOrName, state.schema)\n  const { selection, storedMarks } = state\n  const { $from, $to } = selection\n  const range = $from.blockRange($to)\n\n  const marks = storedMarks || (selection.$to.parentOffset && selection.$from.marks())\n\n  if (!range) {\n    return false\n  }\n\n  const parentList = findParentNode(node => isList(node.type.name, extensions))(selection)\n\n  if (range.depth >= 1 && parentList && range.depth - parentList.depth <= 1) {\n    // remove list\n    if (parentList.node.type === listType) {\n      return commands.liftListItem(itemType)\n    }\n\n    // change list type\n    if (\n      isList(parentList.node.type.name, extensions)\n        && listType.validContent(parentList.node.content)\n        && dispatch\n    ) {\n      return chain()\n        .command(() => {\n          tr.setNodeMarkup(parentList.pos, listType)\n\n          return true\n        })\n        .command(() => joinListBackwards(tr, listType))\n        .command(() => joinListForwards(tr, listType))\n        .run()\n    }\n  }\n  if (!keepMarks || !marks || !dispatch) {\n\n    return chain()\n      // try to convert node to default node if needed\n      .command(() => {\n        const canWrapInList = can().wrapInList(listType, attributes)\n\n        if (canWrapInList) {\n          return true\n        }\n\n        return commands.clearNodes()\n      })\n      .wrapInList(listType, attributes)\n      .command(() => joinListBackwards(tr, listType))\n      .command(() => joinListForwards(tr, listType))\n      .run()\n  }\n\n  return (\n    chain()\n    // try to convert node to default node if needed\n      .command(() => {\n        const canWrapInList = can().wrapInList(listType, attributes)\n\n        const filteredMarks = marks.filter(mark => splittableMarks.includes(mark.type.name))\n\n        tr.ensureMarks(filteredMarks)\n\n        if (canWrapInList) {\n          return true\n        }\n\n        return commands.clearNodes()\n      })\n      .wrapInList(listType, attributes)\n      .command(() => joinListBackwards(tr, listType))\n      .command(() => joinListForwards(tr, listType))\n      .run()\n  )\n}\n", "import { MarkType } from '@tiptap/pm/model'\n\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { isMarkActive } from '../helpers/isMarkActive.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    toggleMark: {\n      /**\n       * Toggle a mark on and off.\n       * @param typeOrName The mark type or name.\n       * @param attributes The attributes of the mark.\n       * @param options.extendEmptyMarkRange Removes the mark even across the current selection. Defaults to `false`.\n       * @example editor.commands.toggleMark('bold')\n       */\n      toggleMark: (\n        /**\n         * The mark type or name.\n         */\n        typeOrName: string | MarkType,\n\n        /**\n         * The attributes of the mark.\n         */\n        attributes?: Record<string, any>,\n\n        options?: {\n          /**\n           * Removes the mark even across the current selection. Defaults to `false`.\n           */\n          extendEmptyMarkRange?: boolean\n        },\n      ) => ReturnType\n    }\n  }\n}\n\nexport const toggleMark: RawCommands['toggleMark'] = (typeOrName, attributes = {}, options = {}) => ({ state, commands }) => {\n  const { extendEmptyMarkRange = false } = options\n  const type = getMarkType(typeOrName, state.schema)\n  const isActive = isMarkActive(state, type, attributes)\n\n  if (isActive) {\n    return commands.unsetMark(type, { extendEmptyMarkRange })\n  }\n\n  return commands.setMark(type, attributes)\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { isNodeActive } from '../helpers/isNodeActive.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    toggleNode: {\n      /**\n       * Toggle a node with another node.\n       * @param typeOrName The type or name of the node.\n       * @param toggleTypeOrName The type or name of the node to toggle.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.toggleNode('heading', 'paragraph')\n       */\n      toggleNode: (\n        typeOrName: string | NodeType,\n        toggleTypeOrName: string | NodeType,\n        attributes?: Record<string, any>,\n      ) => ReturnType\n    }\n  }\n}\n\nexport const toggleNode: RawCommands['toggleNode'] = (typeOrName, toggleTypeOrName, attributes = {}) => ({ state, commands }) => {\n  const type = getNodeType(typeOrName, state.schema)\n  const toggleType = getNodeType(toggleTypeOrName, state.schema)\n  const isActive = isNodeActive(state, type, attributes)\n\n  let attributesToCopy: Record<string, any> | undefined\n\n  if (state.selection.$anchor.sameParent(state.selection.$head)) {\n    // only copy attributes if the selection is pointing to a node of the same type\n    attributesToCopy = state.selection.$anchor.parent.attrs\n  }\n\n  if (isActive) {\n    return commands.setNode(toggleType, attributesToCopy)\n  }\n\n  // If the node is not active, we want to set the new node type with the given attributes\n  // Copying over the attributes from the current node if the selection is pointing to a node of the same type\n  return commands.setNode(type, { ...attributesToCopy, ...attributes })\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { isNodeActive } from '../helpers/isNodeActive.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    toggleWrap: {\n      /**\n       * Wraps nodes in another node, or removes an existing wrap.\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.toggleWrap('blockquote')\n       */\n      toggleWrap: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const toggleWrap: RawCommands['toggleWrap'] = (typeOrName, attributes = {}) => ({ state, commands }) => {\n  const type = getNodeType(typeOrName, state.schema)\n  const isActive = isNodeActive(state, type, attributes)\n\n  if (isActive) {\n    return commands.lift(type)\n  }\n\n  return commands.wrapIn(type, attributes)\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    undoInputRule: {\n      /**\n       * Undo an input rule.\n       * @example editor.commands.undoInputRule()\n       */\n      undoInputRule: () => ReturnType,\n    }\n  }\n}\n\nexport const undoInputRule: RawCommands['undoInputRule'] = () => ({ state, dispatch }) => {\n  const plugins = state.plugins\n\n  for (let i = 0; i < plugins.length; i += 1) {\n    const plugin = plugins[i]\n    let undoable\n\n    // @ts-ignore\n    // eslint-disable-next-line\n    if (plugin.spec.isInputRules && (undoable = plugin.getState(state))) {\n      if (dispatch) {\n        const tr = state.tr\n        const toUndo = undoable.transform\n\n        for (let j = toUndo.steps.length - 1; j >= 0; j -= 1) {\n          tr.step(toUndo.steps[j].invert(toUndo.docs[j]))\n        }\n\n        if (undoable.text) {\n          const marks = tr.doc.resolve(undoable.from).marks()\n\n          tr.replaceWith(undoable.from, undoable.to, state.schema.text(undoable.text, marks))\n        } else {\n          tr.delete(undoable.from, undoable.to)\n        }\n      }\n\n      return true\n    }\n  }\n\n  return false\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    unsetAllMarks: {\n      /**\n       * Remove all marks in the current selection.\n       * @example editor.commands.unsetAllMarks()\n       */\n      unsetAllMarks: () => ReturnType,\n    }\n  }\n}\n\nexport const unsetAllMarks: RawCommands['unsetAllMarks'] = () => ({ tr, dispatch }) => {\n  const { selection } = tr\n  const { empty, ranges } = selection\n\n  if (empty) {\n    return true\n  }\n\n  if (dispatch) {\n    ranges.forEach(range => {\n      tr.removeMark(range.$from.pos, range.$to.pos)\n    })\n  }\n\n  return true\n}\n", "import { MarkType } from '@tiptap/pm/model'\n\nimport { getMarkRange } from '../helpers/getMarkRange.js'\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    unsetMark: {\n      /**\n       * Remove all marks in the current selection.\n       * @param typeOrName The mark type or name.\n       * @param options.extendEmptyMarkRange Removes the mark even across the current selection. Defaults to `false`.\n       * @example editor.commands.unsetMark('bold')\n       */\n      unsetMark: (\n        /**\n         * The mark type or name.\n         */\n        typeOrName: string | MarkType,\n\n        options?: {\n          /**\n           * Removes the mark even across the current selection. Defaults to `false`.\n           */\n          extendEmptyMarkRange?: boolean\n        },\n      ) => ReturnType\n    }\n  }\n}\n\nexport const unsetMark: RawCommands['unsetMark'] = (typeOrName, options = {}) => ({ tr, state, dispatch }) => {\n  const { extendEmptyMarkRange = false } = options\n  const { selection } = tr\n  const type = getMarkType(typeOrName, state.schema)\n  const { $from, empty, ranges } = selection\n\n  if (!dispatch) {\n    return true\n  }\n\n  if (empty && extendEmptyMarkRange) {\n    let { from, to } = selection\n    const attrs = $from.marks().find(mark => mark.type === type)?.attrs\n    const range = getMarkRange($from, type, attrs)\n\n    if (range) {\n      from = range.from\n      to = range.to\n    }\n\n    tr.removeMark(from, to, type)\n  } else {\n    ranges.forEach(range => {\n      tr.removeMark(range.$from.pos, range.$to.pos, type)\n    })\n  }\n\n  tr.removeStoredMark(type)\n\n  return true\n}\n", "import {\n  Mark, MarkType, Node, NodeType,\n} from '@tiptap/pm/model'\nimport { SelectionRange } from '@tiptap/pm/state'\n\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { getSchemaTypeNameByName } from '../helpers/getSchemaTypeNameByName.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    updateAttributes: {\n      /**\n       * Update attributes of a node or mark.\n       * @param typeOrName The type or name of the node or mark.\n       * @param attributes The attributes of the node or mark.\n       * @example editor.commands.updateAttributes('mention', { userId: \"2\" })\n       */\n      updateAttributes: (\n        /**\n         * The type or name of the node or mark.\n         */\n        typeOrName: string | NodeType | MarkType,\n\n        /**\n         * The attributes of the node or mark.\n         */\n        attributes: Record<string, any>,\n      ) => ReturnType\n    }\n  }\n}\n\nexport const updateAttributes: RawCommands['updateAttributes'] = (typeOrName, attributes = {}) => ({ tr, state, dispatch }) => {\n\n  let nodeType: NodeType | null = null\n  let markType: MarkType | null = null\n\n  const schemaType = getSchemaTypeNameByName(\n    typeof typeOrName === 'string' ? typeOrName : typeOrName.name,\n    state.schema,\n  )\n\n  if (!schemaType) {\n    return false\n  }\n\n  if (schemaType === 'node') {\n    nodeType = getNodeType(typeOrName as NodeType, state.schema)\n  }\n\n  if (schemaType === 'mark') {\n    markType = getMarkType(typeOrName as MarkType, state.schema)\n  }\n\n  if (dispatch) {\n    tr.selection.ranges.forEach((range: SelectionRange) => {\n\n      const from = range.$from.pos\n      const to = range.$to.pos\n\n      let lastPos: number | undefined\n      let lastNode: Node | undefined\n      let trimmedFrom: number\n      let trimmedTo: number\n\n      if (tr.selection.empty) {\n        state.doc.nodesBetween(from, to, (node: Node, pos: number) => {\n\n          if (nodeType && nodeType === node.type) {\n            trimmedFrom = Math.max(pos, from)\n            trimmedTo = Math.min(pos + node.nodeSize, to)\n            lastPos = pos\n            lastNode = node\n          }\n        })\n      } else {\n        state.doc.nodesBetween(from, to, (node: Node, pos: number) => {\n\n          if (pos < from && nodeType && nodeType === node.type) {\n            trimmedFrom = Math.max(pos, from)\n            trimmedTo = Math.min(pos + node.nodeSize, to)\n            lastPos = pos\n            lastNode = node\n          }\n\n          if (pos >= from && pos <= to) {\n\n            if (nodeType && nodeType === node.type) {\n              tr.setNodeMarkup(pos, undefined, {\n                ...node.attrs,\n                ...attributes,\n              })\n            }\n\n            if (markType && node.marks.length) {\n              node.marks.forEach((mark: Mark) => {\n\n                if (markType === mark.type) {\n                  const trimmedFrom2 = Math.max(pos, from)\n                  const trimmedTo2 = Math.min(pos + node.nodeSize, to)\n\n                  tr.addMark(\n                    trimmedFrom2,\n                    trimmedTo2,\n                    markType.create({\n                      ...mark.attrs,\n                      ...attributes,\n                    }),\n                  )\n                }\n              })\n            }\n          }\n        })\n      }\n\n      if (lastNode) {\n\n        if (lastPos !== undefined) {\n          tr.setNodeMarkup(lastPos, undefined, {\n            ...lastNode.attrs,\n            ...attributes,\n          })\n        }\n\n        if (markType && lastNode.marks.length) {\n          lastNode.marks.forEach((mark: Mark) => {\n\n            if (markType === mark.type) {\n              tr.addMark(\n                trimmedFrom,\n                trimmedTo,\n                markType.create({\n                  ...mark.attrs,\n                  ...attributes,\n                }),\n              )\n            }\n          })\n        }\n      }\n    })\n  }\n\n  return true\n}\n", "import { wrapIn as originalWrapIn } from '@tiptap/pm/commands'\nimport { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    wrapIn: {\n      /**\n       * Wraps nodes in another node.\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.wrapIn('blockquote')\n       */\n      wrapIn: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const wrapIn: RawCommands['wrapIn'] = (typeOrName, attributes = {}) => ({ state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n\n  return originalWrapIn(type, attributes)(state, dispatch)\n}\n", "import { NodeType } from '@tiptap/pm/model'\nimport { wrapInList as originalWrapInList } from '@tiptap/pm/schema-list'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    wrapInList: {\n      /**\n       * Wrap a node in a list.\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.wrapInList('bulletList')\n       */\n      wrapInList: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const wrapInList: RawCommands['wrapInList'] = (typeOrName, attributes = {}) => ({ state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n\n  return originalWrapInList(type, attributes)(state, dispatch)\n}\n", "import * as commands from '../commands/index.js'\nimport { Extension } from '../Extension.js'\n\nexport * from '../commands/index.js'\n\nexport const Commands = Extension.create({\n  name: 'commands',\n\n  addCommands() {\n    return {\n      ...commands,\n    }\n  },\n})\n", "import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const Drop = Extension.create({\n  name: 'drop',\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('tiptapDrop'),\n\n        props: {\n          handleDrop: (_, e, slice, moved) => {\n            this.editor.emit('drop', {\n              editor: this.editor,\n              event: e,\n              slice,\n              moved,\n            })\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const Editable = Extension.create({\n  name: 'editable',\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('editable'),\n        props: {\n          editable: () => this.editor.options.editable,\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const focusEventsPluginKey = new PluginKey('focusEvents')\n\nexport const FocusEvents = Extension.create({\n  name: 'focusEvents',\n\n  addProseMirrorPlugins() {\n    const { editor } = this\n\n    return [\n      new Plugin({\n        key: focusEventsPluginKey,\n        props: {\n          handleDOMEvents: {\n            focus: (view, event: Event) => {\n              editor.isFocused = true\n\n              const transaction = editor.state.tr\n                .setMeta('focus', { event })\n                .setMeta('addToHistory', false)\n\n              view.dispatch(transaction)\n\n              return false\n            },\n            blur: (view, event: Event) => {\n              editor.isFocused = false\n\n              const transaction = editor.state.tr\n                .setMeta('blur', { event })\n                .setMeta('addToHistory', false)\n\n              view.dispatch(transaction)\n\n              return false\n            },\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON><PERSON>, <PERSON> } from '@tiptap/pm/state'\n\nimport { CommandManager } from '../CommandManager.js'\nimport { Extension } from '../Extension.js'\nimport { createChainableState } from '../helpers/createChainableState.js'\nimport { isNodeEmpty } from '../helpers/isNodeEmpty.js'\nimport { isiOS } from '../utilities/isiOS.js'\nimport { isMacOS } from '../utilities/isMacOS.js'\n\nexport const Keymap = Extension.create({\n  name: 'keymap',\n\n  addKeyboardShortcuts() {\n    const handleBackspace = () => this.editor.commands.first(({ commands }) => [\n      () => commands.undoInputRule(),\n\n      // maybe convert first text block node to default node\n      () => commands.command(({ tr }) => {\n        const { selection, doc } = tr\n        const { empty, $anchor } = selection\n        const { pos, parent } = $anchor\n        const $parentPos = $anchor.parent.isTextblock && pos > 0 ? tr.doc.resolve(pos - 1) : $anchor\n        const parentIsIsolating = $parentPos.parent.type.spec.isolating\n\n        const parentPos = $anchor.pos - $anchor.parentOffset\n\n        const isAtStart = (parentIsIsolating && $parentPos.parent.childCount === 1)\n          ? parentPos === $anchor.pos\n          : Selection.atStart(doc).from === pos\n\n        if (\n          !empty\n          || !parent.type.isTextblock\n          || parent.textContent.length\n          || !isAtStart\n          || (isAtStart && $anchor.parent.type.name === 'paragraph') // prevent clearNodes when no nodes to clear, otherwise history stack is appended\n        ) {\n          return false\n        }\n\n        return commands.clearNodes()\n      }),\n\n      () => commands.deleteSelection(),\n      () => commands.joinBackward(),\n      () => commands.selectNodeBackward(),\n    ])\n\n    const handleDelete = () => this.editor.commands.first(({ commands }) => [\n      () => commands.deleteSelection(),\n      () => commands.deleteCurrentNode(),\n      () => commands.joinForward(),\n      () => commands.selectNodeForward(),\n    ])\n\n    const handleEnter = () => this.editor.commands.first(({ commands }) => [\n      () => commands.newlineInCode(),\n      () => commands.createParagraphNear(),\n      () => commands.liftEmptyBlock(),\n      () => commands.splitBlock(),\n    ])\n\n    const baseKeymap = {\n      Enter: handleEnter,\n      'Mod-Enter': () => this.editor.commands.exitCode(),\n      Backspace: handleBackspace,\n      'Mod-Backspace': handleBackspace,\n      'Shift-Backspace': handleBackspace,\n      Delete: handleDelete,\n      'Mod-Delete': handleDelete,\n      'Mod-a': () => this.editor.commands.selectAll(),\n    }\n\n    const pcKeymap = {\n      ...baseKeymap,\n    }\n\n    const macKeymap = {\n      ...baseKeymap,\n      'Ctrl-h': handleBackspace,\n      'Alt-Backspace': handleBackspace,\n      'Ctrl-d': handleDelete,\n      'Ctrl-Alt-Backspace': handleDelete,\n      'Alt-Delete': handleDelete,\n      'Alt-d': handleDelete,\n      'Ctrl-a': () => this.editor.commands.selectTextblockStart(),\n      'Ctrl-e': () => this.editor.commands.selectTextblockEnd(),\n    }\n\n    if (isiOS() || isMacOS()) {\n      return macKeymap\n    }\n\n    return pcKeymap\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      // With this plugin we check if the whole document was selected and deleted.\n      // In this case we will additionally call `clearNodes()` to convert e.g. a heading\n      // to a paragraph if necessary.\n      // This is an alternative to ProseMirror's `AllSelection`, which doesn’t work well\n      // with many other commands.\n      new Plugin({\n        key: new PluginKey('clearDocument'),\n        appendTransaction: (transactions, oldState, newState) => {\n          if (transactions.some(tr => tr.getMeta('composition'))) {\n            return\n          }\n\n          const docChanges = transactions.some(transaction => transaction.docChanged)\n            && !oldState.doc.eq(newState.doc)\n\n          const ignoreTr = transactions.some(transaction => transaction.getMeta('preventClearDocument'))\n\n          if (!docChanges || ignoreTr) {\n            return\n          }\n\n          const { empty, from, to } = oldState.selection\n          const allFrom = Selection.atStart(oldState.doc).from\n          const allEnd = Selection.atEnd(oldState.doc).to\n          const allWasSelected = from === allFrom && to === allEnd\n\n          if (empty || !allWasSelected) {\n            return\n          }\n\n          const isEmpty = isNodeEmpty(newState.doc)\n\n          if (!isEmpty) {\n            return\n          }\n\n          const tr = newState.tr\n          const state = createChainableState({\n            state: newState,\n            transaction: tr,\n          })\n          const { commands } = new CommandManager({\n            editor: this.editor,\n            state,\n          })\n\n          commands.clearNodes()\n\n          if (!tr.steps.length) {\n            return\n          }\n\n          return tr\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const Paste = Extension.create({\n  name: 'paste',\n\n  addProseMirrorPlugins() {\n\n    return [\n      new Plugin({\n        key: new PluginKey('tiptapPaste'),\n\n        props: {\n          handlePaste: (_view, e, slice) => {\n            this.editor.emit('paste', {\n              editor: this.editor,\n              event: e,\n              slice,\n            })\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const Tabindex = Extension.create({\n  name: 'tabindex',\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('tabindex'),\n        props: {\n          attributes: (): { [name: string]: string; } => (this.editor.isEditable ? { tabindex: '0' } : {}),\n        },\n      }),\n    ]\n  },\n})\n", "import {\n  Fragment, Node, ResolvedPos,\n} from '@tiptap/pm/model'\n\nimport { Editor } from './Editor.js'\nimport { Content, Range } from './types.js'\n\nexport class NodePos {\n  private resolvedPos: ResolvedPos\n\n  private isBlock: boolean\n\n  private editor: Editor\n\n  private get name(): string {\n    return this.node.type.name\n  }\n\n  constructor(pos: ResolvedPos, editor: Editor, isBlock = false, node: Node | null = null) {\n    this.isBlock = isBlock\n    this.resolvedPos = pos\n    this.editor = editor\n    this.currentNode = node\n  }\n\n  private currentNode: Node | null = null\n\n  get node(): Node {\n    return this.currentNode || this.resolvedPos.node()\n  }\n\n  get element(): HTMLElement {\n    return this.editor.view.domAtPos(this.pos).node as HTMLElement\n  }\n\n  public actualDepth: number | null = null\n\n  get depth(): number {\n    return this.actualDepth ?? this.resolvedPos.depth\n  }\n\n  get pos(): number {\n    return this.resolvedPos.pos\n  }\n\n  get content(): Fragment {\n    return this.node.content\n  }\n\n  set content(content: Content) {\n    let from = this.from\n    let to = this.to\n\n    if (this.isBlock) {\n      if (this.content.size === 0) {\n        console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`)\n        return\n      }\n\n      from = this.from + 1\n      to = this.to - 1\n    }\n\n    this.editor.commands.insertContentAt({ from, to }, content)\n  }\n\n  get attributes(): { [key: string]: any } {\n    return this.node.attrs\n  }\n\n  get textContent(): string {\n    return this.node.textContent\n  }\n\n  get size(): number {\n    return this.node.nodeSize\n  }\n\n  get from(): number {\n    if (this.isBlock) {\n      return this.pos\n    }\n\n    return this.resolvedPos.start(this.resolvedPos.depth)\n  }\n\n  get range(): Range {\n    return {\n      from: this.from,\n      to: this.to,\n    }\n  }\n\n  get to(): number {\n    if (this.isBlock) {\n      return this.pos + this.size\n    }\n\n    return this.resolvedPos.end(this.resolvedPos.depth) + (this.node.isText ? 0 : 1)\n  }\n\n  get parent(): NodePos | null {\n    if (this.depth === 0) {\n      return null\n    }\n\n    const parentPos = this.resolvedPos.start(this.resolvedPos.depth - 1)\n    const $pos = this.resolvedPos.doc.resolve(parentPos)\n\n    return new NodePos($pos, this.editor)\n  }\n\n  get before(): NodePos | null {\n    let $pos = this.resolvedPos.doc.resolve(this.from - (this.isBlock ? 1 : 2))\n\n    if ($pos.depth !== this.depth) {\n      $pos = this.resolvedPos.doc.resolve(this.from - 3)\n    }\n\n    return new NodePos($pos, this.editor)\n  }\n\n  get after(): NodePos | null {\n    let $pos = this.resolvedPos.doc.resolve(this.to + (this.isBlock ? 2 : 1))\n\n    if ($pos.depth !== this.depth) {\n      $pos = this.resolvedPos.doc.resolve(this.to + 3)\n    }\n\n    return new NodePos($pos, this.editor)\n  }\n\n  get children(): NodePos[] {\n    const children: NodePos[] = []\n\n    this.node.content.forEach((node, offset) => {\n      const isBlock = node.isBlock && !node.isTextblock\n      const isNonTextAtom = node.isAtom && !node.isText\n\n      const targetPos = this.pos + offset + (isNonTextAtom ? 0 : 1)\n      const $pos = this.resolvedPos.doc.resolve(targetPos)\n\n      if (!isBlock && $pos.depth <= this.depth) {\n        return\n      }\n\n      const childNodePos = new NodePos($pos, this.editor, isBlock, isBlock ? node : null)\n\n      if (isBlock) {\n        childNodePos.actualDepth = this.depth + 1\n      }\n\n      children.push(new NodePos($pos, this.editor, isBlock, isBlock ? node : null))\n    })\n\n    return children\n  }\n\n  get firstChild(): NodePos | null {\n    return this.children[0] || null\n  }\n\n  get lastChild(): NodePos | null {\n    const children = this.children\n\n    return children[children.length - 1] || null\n  }\n\n  closest(selector: string, attributes: { [key: string]: any } = {}): NodePos | null {\n    let node: NodePos | null = null\n    let currentNode = this.parent\n\n    while (currentNode && !node) {\n      if (currentNode.node.type.name === selector) {\n        if (Object.keys(attributes).length > 0) {\n          const nodeAttributes = currentNode.node.attrs\n          const attrKeys = Object.keys(attributes)\n\n          for (let index = 0; index < attrKeys.length; index += 1) {\n            const key = attrKeys[index]\n\n            if (nodeAttributes[key] !== attributes[key]) {\n              break\n            }\n          }\n        } else {\n          node = currentNode\n        }\n      }\n\n      currentNode = currentNode.parent\n    }\n\n    return node\n  }\n\n  querySelector(selector: string, attributes: { [key: string]: any } = {}): NodePos | null {\n    return this.querySelectorAll(selector, attributes, true)[0] || null\n  }\n\n  querySelectorAll(selector: string, attributes: { [key: string]: any } = {}, firstItemOnly = false): NodePos[] {\n    let nodes: NodePos[] = []\n\n    if (!this.children || this.children.length === 0) {\n      return nodes\n    }\n    const attrKeys = Object.keys(attributes)\n\n    /**\n     * Finds all children recursively that match the selector and attributes\n     * If firstItemOnly is true, it will return the first item found\n     */\n    this.children.forEach(childPos => {\n      // If we already found a node and we only want the first item, we dont need to keep going\n      if (firstItemOnly && nodes.length > 0) {\n        return\n      }\n\n      if (childPos.node.type.name === selector) {\n        const doesAllAttributesMatch = attrKeys.every(key => attributes[key] === childPos.node.attrs[key])\n\n        if (doesAllAttributesMatch) {\n          nodes.push(childPos)\n        }\n      }\n\n      // If we already found a node and we only want the first item, we can stop here and skip the recursion\n      if (firstItemOnly && nodes.length > 0) {\n        return\n      }\n\n      nodes = nodes.concat(childPos.querySelectorAll(selector, attributes, firstItemOnly))\n    })\n\n    return nodes\n  }\n\n  setAttribute(attributes: { [key: string]: any }) {\n    const { tr } = this.editor.state\n\n    tr.setNodeMarkup(this.from, undefined, {\n      ...this.node.attrs,\n      ...attributes,\n    })\n\n    this.editor.view.dispatch(tr)\n  }\n}\n", "export const style = `.ProseMirror {\n  position: relative;\n}\n\n.ProseMirror {\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  white-space: break-spaces;\n  -webkit-font-variant-ligatures: none;\n  font-variant-ligatures: none;\n  font-feature-settings: \"liga\" 0; /* the above doesn't seem to work in Edge */\n}\n\n.ProseMirror [contenteditable=\"false\"] {\n  white-space: normal;\n}\n\n.ProseMirror [contenteditable=\"false\"] [contenteditable=\"true\"] {\n  white-space: pre-wrap;\n}\n\n.ProseMirror pre {\n  white-space: pre-wrap;\n}\n\nimg.ProseMirror-separator {\n  display: inline !important;\n  border: none !important;\n  margin: 0 !important;\n  width: 0 !important;\n  height: 0 !important;\n}\n\n.ProseMirror-gapcursor {\n  display: none;\n  pointer-events: none;\n  position: absolute;\n  margin: 0;\n}\n\n.ProseMirror-gapcursor:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: -2px;\n  width: 20px;\n  border-top: 1px solid black;\n  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;\n}\n\n@keyframes ProseMirror-cursor-blink {\n  to {\n    visibility: hidden;\n  }\n}\n\n.ProseMirror-hideselection *::selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection *::-moz-selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection * {\n  caret-color: transparent;\n}\n\n.ProseMirror-focused .ProseMirror-gapcursor {\n  display: block;\n}\n\n.tippy-box[data-animation=fade][data-state=hidden] {\n  opacity: 0\n}`\n", "export function createStyleTag(style: string, nonce?: string, suffix?: string): HTMLStyleElement {\n  const tiptapStyleTag = (<HTMLStyleElement>document.querySelector(`style[data-tiptap-style${suffix ? `-${suffix}` : ''}]`))\n\n  if (tiptapStyleTag !== null) {\n    return tiptapStyleTag\n  }\n\n  const styleNode = document.createElement('style')\n\n  if (nonce) {\n    styleNode.setAttribute('nonce', nonce)\n  }\n\n  styleNode.setAttribute(`data-tiptap-style${suffix ? `-${suffix}` : ''}`, '')\n  styleNode.innerHTML = style\n  document.getElementsByTagName('head')[0].appendChild(styleNode)\n\n  return styleNode\n}\n", "/* eslint-disable @typescript-eslint/no-empty-object-type */\nimport {\n  MarkType,\n  Node as ProseMirrorNode,\n  NodeType,\n  Schema,\n} from '@tiptap/pm/model'\nimport {\n  EditorState, Plugin, PluginKey, Transaction,\n} from '@tiptap/pm/state'\nimport { EditorView } from '@tiptap/pm/view'\n\nimport { CommandManager } from './CommandManager.js'\nimport { EventEmitter } from './EventEmitter.js'\nimport { ExtensionManager } from './ExtensionManager.js'\nimport {\n  ClipboardTextSerializer, Commands, Drop, Editable, FocusEvents, Keymap, Paste,\n  Tabindex,\n} from './extensions/index.js'\nimport { createDocument } from './helpers/createDocument.js'\nimport { getAttributes } from './helpers/getAttributes.js'\nimport { getHTMLFromFragment } from './helpers/getHTMLFromFragment.js'\nimport { getText } from './helpers/getText.js'\nimport { getTextSerializersFromSchema } from './helpers/getTextSerializersFromSchema.js'\nimport { isActive } from './helpers/isActive.js'\nimport { isNodeEmpty } from './helpers/isNodeEmpty.js'\nimport { resolveFocusPosition } from './helpers/resolveFocusPosition.js'\nimport { NodePos } from './NodePos.js'\nimport { style } from './style.js'\nimport {\n  CanCommands,\n  ChainedCommands,\n  EditorEvents,\n  EditorOptions,\n  JSONContent,\n  SingleCommands,\n  TextSerializer,\n} from './types.js'\nimport { createStyleTag } from './utilities/createStyleTag.js'\nimport { isFunction } from './utilities/isFunction.js'\n\nexport * as extensions from './extensions/index.js'\n\n// @ts-ignore\nexport interface TiptapEditorHTMLElement extends HTMLElement {\n  editor?: Editor\n}\n\nexport class Editor extends EventEmitter<EditorEvents> {\n  private commandManager!: CommandManager\n\n  public extensionManager!: ExtensionManager\n\n  private css!: HTMLStyleElement\n\n  public schema!: Schema\n\n  public view!: EditorView\n\n  public isFocused = false\n\n  /**\n   * The editor is considered initialized after the `create` event has been emitted.\n   */\n  public isInitialized = false\n\n  public extensionStorage: Record<string, any> = {}\n\n  public options: EditorOptions = {\n    element: document.createElement('div'),\n    content: '',\n    injectCSS: true,\n    injectNonce: undefined,\n    extensions: [],\n    autofocus: false,\n    editable: true,\n    editorProps: {},\n    parseOptions: {},\n    coreExtensionOptions: {},\n    enableInputRules: true,\n    enablePasteRules: true,\n    enableCoreExtensions: true,\n    enableContentCheck: false,\n    onBeforeCreate: () => null,\n    onCreate: () => null,\n    onUpdate: () => null,\n    onSelectionUpdate: () => null,\n    onTransaction: () => null,\n    onFocus: () => null,\n    onBlur: () => null,\n    onDestroy: () => null,\n    onContentError: ({ error }) => { throw error },\n    onPaste: () => null,\n    onDrop: () => null,\n  }\n\n  constructor(options: Partial<EditorOptions> = {}) {\n    super()\n    this.setOptions(options)\n    this.createExtensionManager()\n    this.createCommandManager()\n    this.createSchema()\n    this.on('beforeCreate', this.options.onBeforeCreate)\n    this.emit('beforeCreate', { editor: this })\n    this.on('contentError', this.options.onContentError)\n    this.createView()\n    this.injectCSS()\n    this.on('create', this.options.onCreate)\n    this.on('update', this.options.onUpdate)\n    this.on('selectionUpdate', this.options.onSelectionUpdate)\n    this.on('transaction', this.options.onTransaction)\n    this.on('focus', this.options.onFocus)\n    this.on('blur', this.options.onBlur)\n    this.on('destroy', this.options.onDestroy)\n    this.on('drop', ({ event, slice, moved }) => this.options.onDrop(event, slice, moved))\n    this.on('paste', ({ event, slice }) => this.options.onPaste(event, slice))\n\n    window.setTimeout(() => {\n      if (this.isDestroyed) {\n        return\n      }\n\n      this.commands.focus(this.options.autofocus)\n      this.emit('create', { editor: this })\n      this.isInitialized = true\n    }, 0)\n  }\n\n  /**\n   * Returns the editor storage.\n   */\n  public get storage(): Record<string, any> {\n    return this.extensionStorage\n  }\n\n  /**\n   * An object of all registered commands.\n   */\n  public get commands(): SingleCommands {\n    return this.commandManager.commands\n  }\n\n  /**\n   * Create a command chain to call multiple commands at once.\n   */\n  public chain(): ChainedCommands {\n    return this.commandManager.chain()\n  }\n\n  /**\n   * Check if a command or a command chain can be executed. Without executing it.\n   */\n  public can(): CanCommands {\n    return this.commandManager.can()\n  }\n\n  /**\n   * Inject CSS styles.\n   */\n  private injectCSS(): void {\n    if (this.options.injectCSS && document) {\n      this.css = createStyleTag(style, this.options.injectNonce)\n    }\n  }\n\n  /**\n   * Update editor options.\n   *\n   * @param options A list of options\n   */\n  public setOptions(options: Partial<EditorOptions> = {}): void {\n    this.options = {\n      ...this.options,\n      ...options,\n    }\n\n    if (!this.view || !this.state || this.isDestroyed) {\n      return\n    }\n\n    if (this.options.editorProps) {\n      this.view.setProps(this.options.editorProps)\n    }\n\n    this.view.updateState(this.state)\n  }\n\n  /**\n   * Update editable state of the editor.\n   */\n  public setEditable(editable: boolean, emitUpdate = true): void {\n    this.setOptions({ editable })\n\n    if (emitUpdate) {\n      this.emit('update', { editor: this, transaction: this.state.tr })\n    }\n  }\n\n  /**\n   * Returns whether the editor is editable.\n   */\n  public get isEditable(): boolean {\n    // since plugins are applied after creating the view\n    // `editable` is always `true` for one tick.\n    // that’s why we also have to check for `options.editable`\n    return this.options.editable && this.view && this.view.editable\n  }\n\n  /**\n   * Returns the editor state.\n   */\n  public get state(): EditorState {\n    return this.view.state\n  }\n\n  /**\n   * Register a ProseMirror plugin.\n   *\n   * @param plugin A ProseMirror plugin\n   * @param handlePlugins Control how to merge the plugin into the existing plugins.\n   * @returns The new editor state\n   */\n  public registerPlugin(\n    plugin: Plugin,\n    handlePlugins?: (newPlugin: Plugin, plugins: Plugin[]) => Plugin[],\n  ): EditorState {\n    const plugins = isFunction(handlePlugins)\n      ? handlePlugins(plugin, [...this.state.plugins])\n      : [...this.state.plugins, plugin]\n\n    const state = this.state.reconfigure({ plugins })\n\n    this.view.updateState(state)\n\n    return state\n  }\n\n  /**\n   * Unregister a ProseMirror plugin.\n   *\n   * @param nameOrPluginKeyToRemove The plugins name\n   * @returns The new editor state or undefined if the editor is destroyed\n   */\n  public unregisterPlugin(nameOrPluginKeyToRemove: string | PluginKey | (string | PluginKey)[]): EditorState | undefined {\n    if (this.isDestroyed) {\n      return undefined\n    }\n\n    const prevPlugins = this.state.plugins\n    let plugins = prevPlugins;\n\n    ([] as (string | PluginKey)[]).concat(nameOrPluginKeyToRemove).forEach(nameOrPluginKey => {\n      // @ts-ignore\n      const name = typeof nameOrPluginKey === 'string' ? `${nameOrPluginKey}$` : nameOrPluginKey.key\n\n      // @ts-ignore\n      plugins = prevPlugins.filter(plugin => !plugin.key.startsWith(name))\n    })\n\n    if (prevPlugins.length === plugins.length) {\n      // No plugin was removed, so we don’t need to update the state\n      return undefined\n    }\n\n    const state = this.state.reconfigure({\n      plugins,\n    })\n\n    this.view.updateState(state)\n\n    return state\n  }\n\n  /**\n   * Creates an extension manager.\n   */\n  private createExtensionManager(): void {\n\n    const coreExtensions = this.options.enableCoreExtensions ? [\n      Editable,\n      ClipboardTextSerializer.configure({\n        blockSeparator: this.options.coreExtensionOptions?.clipboardTextSerializer?.blockSeparator,\n      }),\n      Commands,\n      FocusEvents,\n      Keymap,\n      Tabindex,\n      Drop,\n      Paste,\n    ].filter(ext => {\n      if (typeof this.options.enableCoreExtensions === 'object') {\n        return this.options.enableCoreExtensions[ext.name as keyof typeof this.options.enableCoreExtensions] !== false\n      }\n      return true\n    }) : []\n    const allExtensions = [...coreExtensions, ...this.options.extensions].filter(extension => {\n      return ['extension', 'node', 'mark'].includes(extension?.type)\n    })\n\n    this.extensionManager = new ExtensionManager(allExtensions, this)\n  }\n\n  /**\n   * Creates an command manager.\n   */\n  private createCommandManager(): void {\n    this.commandManager = new CommandManager({\n      editor: this,\n    })\n  }\n\n  /**\n   * Creates a ProseMirror schema.\n   */\n  private createSchema(): void {\n    this.schema = this.extensionManager.schema\n  }\n\n  /**\n   * Creates a ProseMirror view.\n   */\n  private createView(): void {\n    let doc: ProseMirrorNode\n\n    try {\n      doc = createDocument(\n        this.options.content,\n        this.schema,\n        this.options.parseOptions,\n        { errorOnInvalidContent: this.options.enableContentCheck },\n      )\n    } catch (e) {\n      if (!(e instanceof Error) || !['[tiptap error]: Invalid JSON content', '[tiptap error]: Invalid HTML content'].includes(e.message)) {\n        // Not the content error we were expecting\n        throw e\n      }\n      this.emit('contentError', {\n        editor: this,\n        error: e as Error,\n        disableCollaboration: () => {\n          if (this.storage.collaboration) {\n            this.storage.collaboration.isDisabled = true\n          }\n          // To avoid syncing back invalid content, reinitialize the extensions without the collaboration extension\n          this.options.extensions = this.options.extensions.filter(extension => extension.name !== 'collaboration')\n\n          // Restart the initialization process by recreating the extension manager with the new set of extensions\n          this.createExtensionManager()\n        },\n      })\n\n      // Content is invalid, but attempt to create it anyway, stripping out the invalid parts\n      doc = createDocument(\n        this.options.content,\n        this.schema,\n        this.options.parseOptions,\n        { errorOnInvalidContent: false },\n      )\n    }\n    const selection = resolveFocusPosition(doc, this.options.autofocus)\n\n    this.view = new EditorView(this.options.element, {\n      ...this.options.editorProps,\n      attributes: {\n        // add `role=\"textbox\"` to the editor element\n        role: 'textbox',\n        ...this.options.editorProps?.attributes,\n      },\n      dispatchTransaction: this.dispatchTransaction.bind(this),\n      state: EditorState.create({\n        doc,\n        selection: selection || undefined,\n      }),\n    })\n\n    // `editor.view` is not yet available at this time.\n    // Therefore we will add all plugins and node views directly afterwards.\n    const newState = this.state.reconfigure({\n      plugins: this.extensionManager.plugins,\n    })\n\n    this.view.updateState(newState)\n\n    this.createNodeViews()\n    this.prependClass()\n\n    // Let’s store the editor instance in the DOM element.\n    // So we’ll have access to it for tests.\n    // @ts-ignore\n    const dom = this.view.dom as TiptapEditorHTMLElement\n\n    dom.editor = this\n  }\n\n  /**\n   * Creates all node views.\n   */\n  public createNodeViews(): void {\n    if (this.view.isDestroyed) {\n      return\n    }\n\n    this.view.setProps({\n      nodeViews: this.extensionManager.nodeViews,\n    })\n  }\n\n  /**\n   * Prepend class name to element.\n   */\n  public prependClass(): void {\n    this.view.dom.className = `tiptap ${this.view.dom.className}`\n  }\n\n  public isCapturingTransaction = false\n\n  private capturedTransaction: Transaction | null = null\n\n  public captureTransaction(fn: () => void) {\n    this.isCapturingTransaction = true\n    fn()\n    this.isCapturingTransaction = false\n\n    const tr = this.capturedTransaction\n\n    this.capturedTransaction = null\n\n    return tr\n  }\n\n  /**\n   * The callback over which to send transactions (state updates) produced by the view.\n   *\n   * @param transaction An editor state transaction\n   */\n  private dispatchTransaction(transaction: Transaction): void {\n    // if the editor / the view of the editor was destroyed\n    // the transaction should not be dispatched as there is no view anymore.\n    if (this.view.isDestroyed) {\n      return\n    }\n\n    if (this.isCapturingTransaction) {\n      if (!this.capturedTransaction) {\n        this.capturedTransaction = transaction\n\n        return\n      }\n\n      transaction.steps.forEach(step => this.capturedTransaction?.step(step))\n\n      return\n    }\n\n    const state = this.state.apply(transaction)\n    const selectionHasChanged = !this.state.selection.eq(state.selection)\n\n    this.emit('beforeTransaction', {\n      editor: this,\n      transaction,\n      nextState: state,\n    })\n    this.view.updateState(state)\n    this.emit('transaction', {\n      editor: this,\n      transaction,\n    })\n\n    if (selectionHasChanged) {\n      this.emit('selectionUpdate', {\n        editor: this,\n        transaction,\n      })\n    }\n\n    const focus = transaction.getMeta('focus')\n    const blur = transaction.getMeta('blur')\n\n    if (focus) {\n      this.emit('focus', {\n        editor: this,\n        event: focus.event,\n        transaction,\n      })\n    }\n\n    if (blur) {\n      this.emit('blur', {\n        editor: this,\n        event: blur.event,\n        transaction,\n      })\n    }\n\n    if (!transaction.docChanged || transaction.getMeta('preventUpdate')) {\n      return\n    }\n\n    this.emit('update', {\n      editor: this,\n      transaction,\n    })\n  }\n\n  /**\n   * Get attributes of the currently selected node or mark.\n   */\n  public getAttributes(nameOrType: string | NodeType | MarkType): Record<string, any> {\n    return getAttributes(this.state, nameOrType)\n  }\n\n  /**\n   * Returns if the currently selected node or mark is active.\n   *\n   * @param name Name of the node or mark\n   * @param attributes Attributes of the node or mark\n   */\n  public isActive(name: string, attributes?: {}): boolean\n  public isActive(attributes: {}): boolean\n  public isActive(nameOrAttributes: string, attributesOrUndefined?: {}): boolean {\n    const name = typeof nameOrAttributes === 'string' ? nameOrAttributes : null\n\n    const attributes = typeof nameOrAttributes === 'string' ? attributesOrUndefined : nameOrAttributes\n\n    return isActive(this.state, name, attributes)\n  }\n\n  /**\n   * Get the document as JSON.\n   */\n  public getJSON(): JSONContent {\n    return this.state.doc.toJSON()\n  }\n\n  /**\n   * Get the document as HTML.\n   */\n  public getHTML(): string {\n    return getHTMLFromFragment(this.state.doc.content, this.schema)\n  }\n\n  /**\n   * Get the document as text.\n   */\n  public getText(options?: {\n    blockSeparator?: string\n    textSerializers?: Record<string, TextSerializer>\n  }): string {\n    const { blockSeparator = '\\n\\n', textSerializers = {} } = options || {}\n\n    return getText(this.state.doc, {\n      blockSeparator,\n      textSerializers: {\n        ...getTextSerializersFromSchema(this.schema),\n        ...textSerializers,\n      },\n    })\n  }\n\n  /**\n   * Check if there is no content.\n   */\n  public get isEmpty(): boolean {\n    return isNodeEmpty(this.state.doc)\n  }\n\n  /**\n   * Get the number of characters for the current document.\n   *\n   * @deprecated\n   */\n  public getCharacterCount(): number {\n    console.warn(\n      '[tiptap warn]: \"editor.getCharacterCount()\" is deprecated. Please use \"editor.storage.characterCount.characters()\" instead.',\n    )\n\n    return this.state.doc.content.size - 2\n  }\n\n  /**\n   * Destroy the editor.\n   */\n  public destroy(): void {\n    this.emit('destroy')\n\n    if (this.view) {\n      // Cleanup our reference to prevent circular references which caused memory leaks\n      // @ts-ignore\n      const dom = this.view.dom as TiptapEditorHTMLElement\n\n      if (dom && dom.editor) {\n        delete dom.editor\n      }\n      this.view.destroy()\n    }\n\n    this.removeAllListeners()\n  }\n\n  /**\n   * Check if the editor is already destroyed.\n   */\n  public get isDestroyed(): boolean {\n    // @ts-ignore\n    return !this.view?.docView\n  }\n\n  public $node(selector: string, attributes?: { [key: string]: any }): NodePos | null {\n    return this.$doc?.querySelector(selector, attributes) || null\n  }\n\n  public $nodes(selector: string, attributes?: { [key: string]: any }): NodePos[] | null {\n    return this.$doc?.querySelectorAll(selector, attributes) || null\n  }\n\n  public $pos(pos: number) {\n    const $pos = this.state.doc.resolve(pos)\n\n    return new NodePos($pos, this)\n  }\n\n  get $doc() {\n    return this.$pos(0)\n  }\n}\n", "import { MarkType } from '@tiptap/pm/model'\n\nimport { getMarksBetween } from '../helpers/getMarksBetween.js'\nimport { InputRule, InputRuleFinder } from '../InputRule.js'\nimport { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an input rule that adds a mark when the\n * matched text is typed into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function markInputRule(config: {\n  find: InputRuleFinder\n  type: MarkType\n  getAttributes?:\n    | Record<string, any>\n    | ((match: ExtendedRegExpMatchArray) => Record<string, any>)\n    | false\n    | null\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      const attributes = callOrReturn(config.getAttributes, undefined, match)\n\n      if (attributes === false || attributes === null) {\n        return null\n      }\n\n      const { tr } = state\n      const captureGroup = match[match.length - 1]\n      const fullMatch = match[0]\n\n      if (captureGroup) {\n        const startSpaces = fullMatch.search(/\\S/)\n        const textStart = range.from + fullMatch.indexOf(captureGroup)\n        const textEnd = textStart + captureGroup.length\n\n        const excludedMarks = getMarksBetween(range.from, range.to, state.doc)\n          .filter(item => {\n            // @ts-ignore\n            const excluded = item.mark.type.excluded as MarkType[]\n\n            return excluded.find(type => type === config.type && type !== item.mark.type)\n          })\n          .filter(item => item.to > textStart)\n\n        if (excludedMarks.length) {\n          return null\n        }\n\n        if (textEnd < range.to) {\n          tr.delete(textEnd, range.to)\n        }\n\n        if (textStart > range.from) {\n          tr.delete(range.from + startSpaces, textStart)\n        }\n\n        const markEnd = range.from + startSpaces + captureGroup.length\n\n        tr.addMark(range.from + startSpaces, markEnd, config.type.create(attributes || {}))\n\n        tr.removeStoredMark(config.type)\n      }\n    },\n  })\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { InputRule, InputRuleFinder } from '../InputRule.js'\nimport { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an input rule that adds a node when the\n * matched text is typed into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function nodeInputRule(config: {\n  /**\n   * The regex to match.\n   */\n  find: InputRuleFinder\n\n  /**\n   * The node type to add.\n   */\n  type: NodeType\n\n  /**\n   * A function that returns the attributes for the node\n   * can also be an object of attributes\n   */\n  getAttributes?:\n    | Record<string, any>\n    | ((match: ExtendedRegExpMatchArray) => Record<string, any>)\n    | false\n    | null\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      const attributes = callOrReturn(config.getAttributes, undefined, match) || {}\n      const { tr } = state\n      const start = range.from\n      let end = range.to\n\n      const newNode = config.type.create(attributes)\n\n      if (match[1]) {\n        const offset = match[0].lastIndexOf(match[1])\n        let matchStart = start + offset\n\n        if (matchStart > end) {\n          matchStart = end\n        } else {\n          end = matchStart + match[1].length\n        }\n\n        // insert last typed character\n        const lastChar = match[0][match[0].length - 1]\n\n        tr.insertText(lastChar, start + match[0].length - 1)\n\n        // insert node from input rule\n        tr.replaceWith(matchStart, end, newNode)\n      } else if (match[0]) {\n        const insertionStart = config.type.isInline ? start : start - 1\n\n        tr.insert(insertionStart, config.type.create(attributes)).delete(\n          tr.mapping.map(start),\n          tr.mapping.map(end),\n        )\n      }\n\n      tr.scrollIntoView()\n    },\n  })\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { InputRule, InputRuleFinder } from '../InputRule.js'\nimport { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an input rule that changes the type of a textblock when the\n * matched text is typed into it. When using a regular expresion you’ll\n * probably want the regexp to start with `^`, so that the pattern can\n * only occur at the start of a textblock.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function textblockTypeInputRule(config: {\n  find: InputRuleFinder\n  type: NodeType\n  getAttributes?:\n    | Record<string, any>\n    | ((match: ExtendedRegExpMatchArray) => Record<string, any>)\n    | false\n    | null\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      const $start = state.doc.resolve(range.from)\n      const attributes = callOrReturn(config.getAttributes, undefined, match) || {}\n\n      if (!$start.node(-1).canReplaceWith($start.index(-1), $start.indexAfter(-1), config.type)) {\n        return null\n      }\n\n      state.tr\n        .delete(range.from, range.to)\n        .setBlockType(range.from, range.from, config.type, attributes)\n    },\n  })\n}\n", "import { InputRule, InputRuleFinder } from '../InputRule.js'\n\n/**\n * Build an input rule that replaces text when the\n * matched text is typed into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function textInputRule(config: {\n  find: InputRuleFinder,\n  replace: string,\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      let insert = config.replace\n      let start = range.from\n      const end = range.to\n\n      if (match[1]) {\n        const offset = match[0].lastIndexOf(match[1])\n\n        insert += match[0].slice(offset + match[1].length)\n        start += offset\n\n        const cutOff = start - end\n\n        if (cutOff > 0) {\n          insert = match[0].slice(offset - cutOff, offset) + insert\n          start = end\n        }\n      }\n\n      state.tr.insertText(insert, start, end)\n    },\n  })\n}\n", "import { Node as ProseMir<PERSON>rNode, NodeType } from '@tiptap/pm/model'\nimport { canJoin, findWrapping } from '@tiptap/pm/transform'\n\nimport { Editor } from '../Editor.js'\nimport { InputRule, InputRuleFinder } from '../InputRule.js'\nimport { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an input rule for automatically wrapping a textblock when a\n * given string is typed. When using a regular expresion you’ll\n * probably want the regexp to start with `^`, so that the pattern can\n * only occur at the start of a textblock.\n *\n * `type` is the type of node to wrap in.\n *\n * By default, if there’s a node with the same type above the newly\n * wrapped node, the rule will try to join those\n * two nodes. You can pass a join predicate, which takes a regular\n * expression match and the node before the wrapped node, and can\n * return a boolean to indicate whether a join should happen.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function wrappingInputRule(config: {\n  find: InputRuleFinder,\n  type: NodeType,\n  keepMarks?: boolean,\n  keepAttributes?: boolean,\n  editor?: Editor\n  getAttributes?:\n  | Record<string, any>\n  | ((match: ExtendedRegExpMatchArray) => Record<string, any>)\n  | false\n  | null\n  ,\n  joinPredicate?: (match: ExtendedRegExpMatchArray, node: ProseMirrorNode) => boolean,\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({\n      state, range, match, chain,\n    }) => {\n      const attributes = callOrReturn(config.getAttributes, undefined, match) || {}\n      const tr = state.tr.delete(range.from, range.to)\n      const $start = tr.doc.resolve(range.from)\n      const blockRange = $start.blockRange()\n      const wrapping = blockRange && findWrapping(blockRange, config.type, attributes)\n\n      if (!wrapping) {\n        return null\n      }\n\n      tr.wrap(blockRange, wrapping)\n\n      if (config.keepMarks && config.editor) {\n        const { selection, storedMarks } = state\n        const { splittableMarks } = config.editor.extensionManager\n        const marks = storedMarks || (selection.$to.parentOffset && selection.$from.marks())\n\n        if (marks) {\n          const filteredMarks = marks.filter(mark => splittableMarks.includes(mark.type.name))\n\n          tr.ensureMarks(filteredMarks)\n        }\n      }\n      if (config.keepAttributes) {\n        /** If the nodeType is `bulletList` or `orderedList` set the `nodeType` as `listItem` */\n        const nodeType = config.type.name === 'bulletList' || config.type.name === 'orderedList' ? 'listItem' : 'taskList'\n\n        chain().updateAttributes(nodeType, attributes).run()\n      }\n\n      const before = tr.doc.resolve(range.from - 1).nodeBefore\n\n      if (\n        before\n        && before.type === config.type\n        && canJoin(tr.doc, range.from - 1)\n        && (!config.joinPredicate || config.joinPredicate(match, before))\n      ) {\n        tr.join(range.from - 1)\n      }\n    },\n  })\n}\n", "import {\n  DOMOutputSpec, Node as ProseMirror<PERSON>ode, NodeSpec, NodeType,\n} from '@tiptap/pm/model'\nimport { Plugin, Transaction } from '@tiptap/pm/state'\n\nimport { Editor } from './Editor.js'\nimport { getExtensionField } from './helpers/getExtensionField.js'\nimport { NodeConfig } from './index.js'\nimport { InputRule } from './InputRule.js'\nimport { Mark } from './Mark.js'\nimport { PasteRule } from './PasteRule.js'\nimport {\n  AnyConfig,\n  Attributes,\n  Extensions,\n  GlobalAttributes,\n  KeyboardShortcutCommand,\n  NodeViewRenderer,\n  ParentConfig,\n  RawCommands,\n} from './types.js'\nimport { callOrReturn } from './utilities/callOrReturn.js'\nimport { mergeDeep } from './utilities/mergeDeep.js'\n\ndeclare module '@tiptap/core' {\n  interface NodeConfig<Options = any, Storage = any> {\n    // @ts-ignore - this is a dynamic key\n    [key: string]: any\n\n    /**\n     * The extension name - this must be unique.\n     * It will be used to identify the extension.\n     *\n     * @example 'myExtension'\n     */\n    name: string\n\n    /**\n     * The priority of your extension. The higher, the earlier it will be called\n     * and will take precedence over other extensions with a lower priority.\n     * @default 100\n     * @example 101\n     */\n    priority?: number\n\n    /**\n     * The default options for this extension.\n     * @example\n     * defaultOptions: {\n     *   myOption: 'foo',\n     *   myOtherOption: 10,\n     * }\n     */\n    defaultOptions?: Options\n\n    /**\n     * This method will add options to this extension\n     * @see https://tiptap.dev/guide/custom-extensions#settings\n     * @example\n     * addOptions() {\n     *  return {\n     *    myOption: 'foo',\n     *    myOtherOption: 10,\n     * }\n     */\n    addOptions?: (this: {\n      name: string\n      parent: Exclude<ParentConfig<NodeConfig<Options, Storage>>['addOptions'], undefined>\n    }) => Options\n\n    /**\n     * The default storage this extension can save data to.\n     * @see https://tiptap.dev/guide/custom-extensions#storage\n     * @example\n     * defaultStorage: {\n     *   prefetchedUsers: [],\n     *   loading: false,\n     * }\n     */\n    addStorage?: (this: {\n      name: string\n      options: Options\n      parent: Exclude<ParentConfig<NodeConfig<Options, Storage>>['addStorage'], undefined>\n    }) => Storage\n\n    /**\n     * This function adds globalAttributes to specific nodes.\n     * @see https://tiptap.dev/guide/custom-extensions#global-attributes\n     * @example\n     * addGlobalAttributes() {\n     *   return [\n     *     {\n             // Extend the following extensions\n     *       types: [\n     *         'heading',\n     *         'paragraph',\n     *       ],\n     *       // … with those attributes\n     *       attributes: {\n     *         textAlign: {\n     *           default: 'left',\n     *           renderHTML: attributes => ({\n     *             style: `text-align: ${attributes.textAlign}`,\n     *           }),\n     *           parseHTML: element => element.style.textAlign || 'left',\n     *         },\n     *       },\n     *     },\n     *   ]\n     * }\n     */\n    addGlobalAttributes?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      extensions: (Node | Mark)[]\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addGlobalAttributes']\n    }) => GlobalAttributes\n\n    /**\n     * This function adds commands to the editor\n     * @see https://tiptap.dev/guide/custom-extensions#keyboard-shortcuts\n     * @example\n     * addCommands() {\n     *   return {\n     *     myCommand: () => ({ chain }) => chain().setMark('type', 'foo').run(),\n     *   }\n     * }\n     */\n    addCommands?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: NodeType\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addCommands']\n    }) => Partial<RawCommands>\n\n    /**\n     * This function registers keyboard shortcuts.\n     * @see https://tiptap.dev/guide/custom-extensions#keyboard-shortcuts\n     * @example\n     * addKeyboardShortcuts() {\n     *   return {\n     *     'Mod-l': () => this.editor.commands.toggleBulletList(),\n     *   }\n     * },\n     */\n    addKeyboardShortcuts?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: NodeType\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addKeyboardShortcuts']\n    }) => {\n      [key: string]: KeyboardShortcutCommand\n    }\n\n    /**\n     * This function adds input rules to the editor.\n     * @see https://tiptap.dev/guide/custom-extensions#input-rules\n     * @example\n     * addInputRules() {\n     *   return [\n     *     markInputRule({\n     *       find: inputRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addInputRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: NodeType\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addInputRules']\n    }) => InputRule[]\n\n    /**\n     * This function adds paste rules to the editor.\n     * @see https://tiptap.dev/guide/custom-extensions#paste-rules\n     * @example\n     * addPasteRules() {\n     *   return [\n     *     markPasteRule({\n     *       find: pasteRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addPasteRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: NodeType\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addPasteRules']\n    }) => PasteRule[]\n\n    /**\n     * This function adds Prosemirror plugins to the editor\n     * @see https://tiptap.dev/guide/custom-extensions#prosemirror-plugins\n     * @example\n     * addProseMirrorPlugins() {\n     *   return [\n     *     customPlugin(),\n     *   ]\n     * }\n     */\n    addProseMirrorPlugins?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: NodeType\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addProseMirrorPlugins']\n    }) => Plugin[]\n\n    /**\n     * This function adds additional extensions to the editor. This is useful for\n     * building extension kits.\n     * @example\n     * addExtensions() {\n     *   return [\n     *     BulletList,\n     *     OrderedList,\n     *     ListItem\n     *   ]\n     * }\n     */\n    addExtensions?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addExtensions']\n    }) => Extensions\n\n    /**\n     * This function extends the schema of the node.\n     * @example\n     * extendNodeSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendNodeSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<NodeConfig<Options, Storage>>['extendNodeSchema']\n          },\n          extension: Node,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * This function extends the schema of the mark.\n     * @example\n     * extendMarkSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendMarkSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<NodeConfig<Options, Storage>>['extendMarkSchema']\n            editor?: Editor\n          },\n          extension: Node,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * The editor is not ready yet.\n     */\n    onBeforeCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['onBeforeCreate']\n        }) => void)\n      | null\n\n    /**\n     * The editor is ready.\n     */\n    onCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['onCreate']\n        }) => void)\n      | null\n\n    /**\n     * The content has changed.\n     */\n    onUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['onUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The selection has changed.\n     */\n    onSelectionUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['onSelectionUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The editor state has changed.\n     */\n    onTransaction?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: NodeType\n            parent: ParentConfig<NodeConfig<Options, Storage>>['onTransaction']\n          },\n          props: {\n            editor: Editor\n            transaction: Transaction\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is focused.\n     */\n    onFocus?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: NodeType\n            parent: ParentConfig<NodeConfig<Options, Storage>>['onFocus']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor isn’t focused anymore.\n     */\n    onBlur?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: NodeType\n            parent: ParentConfig<NodeConfig<Options, Storage>>['onBlur']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is destroyed.\n     */\n    onDestroy?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['onDestroy']\n        }) => void)\n      | null\n\n    /**\n     * Node View\n     */\n    addNodeView?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['addNodeView']\n        }) => NodeViewRenderer)\n      | null\n\n    /**\n     * Defines if this node should be a top level node (doc)\n     * @default false\n     * @example true\n     */\n    topNode?: boolean\n\n    /**\n     * The content expression for this node, as described in the [schema\n     * guide](/docs/guide/#schema.content_expressions). When not given,\n     * the node does not allow any content.\n     *\n     * You can read more about it on the Prosemirror documentation here\n     * @see https://prosemirror.net/docs/guide/#schema.content_expressions\n     * @default undefined\n     * @example content: 'block+'\n     * @example content: 'headline paragraph block*'\n     */\n    content?:\n      | NodeSpec['content']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['content']\n          editor?: Editor\n        }) => NodeSpec['content'])\n\n    /**\n     * The marks that are allowed inside of this node. May be a\n     * space-separated string referring to mark names or groups, `\"_\"`\n     * to explicitly allow all marks, or `\"\"` to disallow marks. When\n     * not given, nodes with inline content default to allowing all\n     * marks, other nodes default to not allowing marks.\n     *\n     * @example marks: 'strong em'\n     */\n    marks?:\n      | NodeSpec['marks']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['marks']\n          editor?: Editor\n        }) => NodeSpec['marks'])\n\n    /**\n     * The group or space-separated groups to which this node belongs,\n     * which can be referred to in the content expressions for the\n     * schema.\n     *\n     * By default Tiptap uses the groups 'block' and 'inline' for nodes. You\n     * can also use custom groups if you want to group specific nodes together\n     * and handle them in your schema.\n     * @example group: 'block'\n     * @example group: 'inline'\n     * @example group: 'customBlock' // this uses a custom group\n     */\n    group?:\n      | NodeSpec['group']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['group']\n          editor?: Editor\n        }) => NodeSpec['group'])\n\n    /**\n     * Should be set to true for inline nodes. (Implied for text nodes.)\n     */\n    inline?:\n      | NodeSpec['inline']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['inline']\n          editor?: Editor\n        }) => NodeSpec['inline'])\n\n    /**\n     * Can be set to true to indicate that, though this isn't a [leaf\n     * node](https://prosemirror.net/docs/ref/#model.NodeType.isLeaf), it doesn't have directly editable\n     * content and should be treated as a single unit in the view.\n     *\n     * @example atom: true\n     */\n    atom?:\n      | NodeSpec['atom']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['atom']\n          editor?: Editor\n        }) => NodeSpec['atom'])\n\n    /**\n     * Controls whether nodes of this type can be selected as a [node\n     * selection](https://prosemirror.net/docs/ref/#state.NodeSelection). Defaults to true for non-text\n     * nodes.\n     *\n     * @default true\n     * @example selectable: false\n     */\n    selectable?:\n      | NodeSpec['selectable']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['selectable']\n          editor?: Editor\n        }) => NodeSpec['selectable'])\n\n    /**\n     * Determines whether nodes of this type can be dragged without\n     * being selected. Defaults to false.\n     *\n     * @default: false\n     * @example: draggable: true\n     */\n    draggable?:\n      | NodeSpec['draggable']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['draggable']\n          editor?: Editor\n        }) => NodeSpec['draggable'])\n\n    /**\n     * Can be used to indicate that this node contains code, which\n     * causes some commands to behave differently.\n     */\n    code?:\n      | NodeSpec['code']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['code']\n          editor?: Editor\n        }) => NodeSpec['code'])\n\n    /**\n     * Controls way whitespace in this a node is parsed. The default is\n     * `\"normal\"`, which causes the [DOM parser](https://prosemirror.net/docs/ref/#model.DOMParser) to\n     * collapse whitespace in normal mode, and normalize it (replacing\n     * newlines and such with spaces) otherwise. `\"pre\"` causes the\n     * parser to preserve spaces inside the node. When this option isn't\n     * given, but [`code`](https://prosemirror.net/docs/ref/#model.NodeSpec.code) is true, `whitespace`\n     * will default to `\"pre\"`. Note that this option doesn't influence\n     * the way the node is rendered—that should be handled by `toDOM`\n     * and/or styling.\n     */\n    whitespace?:\n      | NodeSpec['whitespace']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['whitespace']\n          editor?: Editor\n        }) => NodeSpec['whitespace'])\n\n    /**\n     * Allows a **single** node to be set as linebreak equivalent (e.g. hardBreak).\n     * When converting between block types that have whitespace set to \"pre\"\n     * and don't support the linebreak node (e.g. codeBlock) and other block types\n     * that do support the linebreak node (e.g. paragraphs) - this node will be used\n     * as the linebreak instead of stripping the newline.\n     *\n     * See [linebreakReplacement](https://prosemirror.net/docs/ref/#model.NodeSpec.linebreakReplacement).\n     */\n    linebreakReplacement?:\n      | NodeSpec['linebreakReplacement']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['linebreakReplacement']\n          editor?: Editor\n        }) => NodeSpec['linebreakReplacement'])\n\n    /**\n     * When enabled, enables both\n     * [`definingAsContext`](https://prosemirror.net/docs/ref/#model.NodeSpec.definingAsContext) and\n     * [`definingForContent`](https://prosemirror.net/docs/ref/#model.NodeSpec.definingForContent).\n     *\n     * @default false\n     * @example isolating: true\n     */\n    defining?:\n      | NodeSpec['defining']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['defining']\n          editor?: Editor\n        }) => NodeSpec['defining'])\n\n    /**\n     * When enabled (default is false), the sides of nodes of this type\n     * count as boundaries that regular editing operations, like\n     * backspacing or lifting, won't cross. An example of a node that\n     * should probably have this enabled is a table cell.\n     */\n    isolating?:\n      | NodeSpec['isolating']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['isolating']\n          editor?: Editor\n        }) => NodeSpec['isolating'])\n\n    /**\n     * Associates DOM parser information with this node, which can be\n     * used by [`DOMParser.fromSchema`](https://prosemirror.net/docs/ref/#model.DOMParser^fromSchema) to\n     * automatically derive a parser. The `node` field in the rules is\n     * implied (the name of this node will be filled in automatically).\n     * If you supply your own parser, you do not need to also specify\n     * parsing rules in your schema.\n     *\n     * @example parseHTML: [{ tag: 'div', attrs: { 'data-id': 'my-block' } }]\n     */\n    parseHTML?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<NodeConfig<Options, Storage>>['parseHTML']\n      editor?: Editor\n    }) => NodeSpec['parseDOM']\n\n    /**\n     * A description of a DOM structure. Can be either a string, which is\n     * interpreted as a text node, a DOM node, which is interpreted as\n     * itself, a `{dom, contentDOM}` object, or an array.\n     *\n     * An array describes a DOM element. The first value in the array\n     * should be a string—the name of the DOM element, optionally prefixed\n     * by a namespace URL and a space. If the second element is plain\n     * object, it is interpreted as a set of attributes for the element.\n     * Any elements after that (including the 2nd if it's not an attribute\n     * object) are interpreted as children of the DOM elements, and must\n     * either be valid `DOMOutputSpec` values, or the number zero.\n     *\n     * The number zero (pronounced “hole”) is used to indicate the place\n     * where a node's child nodes should be inserted. If it occurs in an\n     * output spec, it should be the only child element in its parent\n     * node.\n     *\n     * @example toDOM: ['div[data-id=\"my-block\"]', { class: 'my-block' }, 0]\n     */\n    renderHTML?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<NodeConfig<Options, Storage>>['renderHTML']\n            editor?: Editor\n          },\n          props: {\n            node: ProseMirrorNode\n            HTMLAttributes: Record<string, any>\n          },\n        ) => DOMOutputSpec)\n      | null\n\n    /**\n     * renders the node as text\n     * @example renderText: () => 'foo\n     */\n    renderText?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<NodeConfig<Options, Storage>>['renderText']\n            editor?: Editor\n          },\n          props: {\n            node: ProseMirrorNode\n            pos: number\n            parent: ProseMirrorNode\n            index: number\n          },\n        ) => string)\n      | null\n\n    /**\n     * Add attributes to the node\n     * @example addAttributes: () => ({ class: 'foo' })\n     */\n    addAttributes?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addAttributes']\n      editor?: Editor\n    // eslint-disable-next-line @typescript-eslint/no-empty-object-type\n    }) => Attributes | {}\n  }\n}\n\n/**\n * The Node class is used to create custom node extensions.\n * @see https://tiptap.dev/api/extensions#create-a-new-extension\n */\nexport class Node<Options = any, Storage = any> {\n  type = 'node'\n\n  name = 'node'\n\n  parent: Node | null = null\n\n  child: Node | null = null\n\n  options: Options\n\n  storage: Storage\n\n  config: NodeConfig = {\n    name: this.name,\n    defaultOptions: {},\n  }\n\n  constructor(config: Partial<NodeConfig<Options, Storage>> = {}) {\n    this.config = {\n      ...this.config,\n      ...config,\n    }\n\n    this.name = this.config.name\n\n    if (config.defaultOptions && Object.keys(config.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${this.name}\".`,\n      )\n    }\n\n    // TODO: remove `addOptions` fallback\n    this.options = this.config.defaultOptions\n\n    if (this.config.addOptions) {\n      this.options = callOrReturn(\n        getExtensionField<AnyConfig['addOptions']>(this, 'addOptions', {\n          name: this.name,\n        }),\n      )\n    }\n\n    this.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(this, 'addStorage', {\n        name: this.name,\n        options: this.options,\n      }),\n    ) || {}\n  }\n\n  static create<O = any, S = any>(config: Partial<NodeConfig<O, S>> = {}) {\n    return new Node<O, S>(config)\n  }\n\n  configure(options: Partial<Options> = {}) {\n    // return a new instance so we can use the same extension\n    // with different calls of `configure`\n    const extension = this.extend<Options, Storage>({\n      ...this.config,\n      addOptions: () => {\n        return mergeDeep(this.options as Record<string, any>, options) as Options\n      },\n    })\n\n    // Always preserve the current name\n    extension.name = this.name\n    // Set the parent to be our parent\n    extension.parent = this.parent\n\n    return extension\n  }\n\n  extend<ExtendedOptions = Options, ExtendedStorage = Storage>(\n    extendedConfig: Partial<NodeConfig<ExtendedOptions, ExtendedStorage>> = {},\n  ) {\n    const extension = new Node<ExtendedOptions, ExtendedStorage>(extendedConfig)\n\n    extension.parent = this\n\n    this.child = extension\n\n    extension.name = extendedConfig.name ? extendedConfig.name : extension.parent.name\n\n    if (extendedConfig.defaultOptions && Object.keys(extendedConfig.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${extension.name}\".`,\n      )\n    }\n\n    extension.options = callOrReturn(\n      getExtensionField<AnyConfig['addOptions']>(extension, 'addOptions', {\n        name: extension.name,\n      }),\n    )\n\n    extension.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(extension, 'addStorage', {\n        name: extension.name,\n        options: extension.options,\n      }),\n    )\n\n    return extension\n  }\n}\n", "import { NodeSelection } from '@tiptap/pm/state'\nimport { NodeView as ProseMirrorNodeView, ViewMutationRecord } from '@tiptap/pm/view'\n\nimport { Editor as CoreEditor } from './Editor.js'\nimport { DecorationWithType, NodeViewRendererOptions, NodeViewRendererProps } from './types.js'\nimport { isAndroid } from './utilities/isAndroid.js'\nimport { isiOS } from './utilities/isiOS.js'\n\n/**\n * Node views are used to customize the rendered DOM structure of a node.\n * @see https://tiptap.dev/guide/node-views\n */\nexport class NodeView<\n  Component,\n  NodeEditor extends CoreEditor = CoreEditor,\n  Options extends NodeViewRendererOptions = NodeViewRendererOptions,\n> implements ProseMirrorNodeView {\n  component: Component\n\n  editor: NodeEditor\n\n  options: Options\n\n  extension: NodeViewRendererProps['extension']\n\n  node: NodeViewRendererProps['node']\n\n  decorations: NodeViewRendererProps['decorations']\n\n  innerDecorations: NodeViewRendererProps['innerDecorations']\n\n  view: NodeViewRendererProps['view']\n\n  getPos: NodeViewRendererProps['getPos']\n\n  HTMLAttributes: NodeViewRendererProps['HTMLAttributes']\n\n  isDragging = false\n\n  constructor(component: Component, props: NodeViewRendererProps, options?: Partial<Options>) {\n    this.component = component\n    this.editor = props.editor as NodeEditor\n    this.options = {\n      stopEvent: null,\n      ignoreMutation: null,\n      ...options,\n    } as Options\n    this.extension = props.extension\n    this.node = props.node\n    this.decorations = props.decorations as DecorationWithType[]\n    this.innerDecorations = props.innerDecorations\n    this.view = props.view\n    this.HTMLAttributes = props.HTMLAttributes\n    this.getPos = props.getPos\n    this.mount()\n  }\n\n  mount() {\n    // eslint-disable-next-line\n    return\n  }\n\n  get dom(): HTMLElement {\n    return this.editor.view.dom as HTMLElement\n  }\n\n  get contentDOM(): HTMLElement | null {\n    return null\n  }\n\n  onDragStart(event: DragEvent) {\n    const { view } = this.editor\n    const target = event.target as HTMLElement\n\n    // get the drag handle element\n    // `closest` is not available for text nodes so we may have to use its parent\n    const dragHandle = target.nodeType === 3\n      ? target.parentElement?.closest('[data-drag-handle]')\n      : target.closest('[data-drag-handle]')\n\n    if (!this.dom || this.contentDOM?.contains(target) || !dragHandle) {\n      return\n    }\n\n    let x = 0\n    let y = 0\n\n    // calculate offset for drag element if we use a different drag handle element\n    if (this.dom !== dragHandle) {\n      const domBox = this.dom.getBoundingClientRect()\n      const handleBox = dragHandle.getBoundingClientRect()\n\n      // In React, we have to go through nativeEvent to reach offsetX/offsetY.\n      const offsetX = event.offsetX ?? (event as any).nativeEvent?.offsetX\n      const offsetY = event.offsetY ?? (event as any).nativeEvent?.offsetY\n\n      x = handleBox.x - domBox.x + offsetX\n      y = handleBox.y - domBox.y + offsetY\n    }\n\n    const clonedNode = this.dom.cloneNode(true) as HTMLElement\n\n    event.dataTransfer?.setDragImage(clonedNode, x, y)\n\n    const pos = this.getPos()\n\n    if (typeof pos !== 'number') {\n      return\n    }\n    // we need to tell ProseMirror that we want to move the whole node\n    // so we create a NodeSelection\n    const selection = NodeSelection.create(view.state.doc, pos)\n    const transaction = view.state.tr.setSelection(selection)\n\n    view.dispatch(transaction)\n  }\n\n  stopEvent(event: Event) {\n    if (!this.dom) {\n      return false\n    }\n\n    if (typeof this.options.stopEvent === 'function') {\n      return this.options.stopEvent({ event })\n    }\n\n    const target = event.target as HTMLElement\n    const isInElement = this.dom.contains(target) && !this.contentDOM?.contains(target)\n\n    // any event from child nodes should be handled by ProseMirror\n    if (!isInElement) {\n      return false\n    }\n\n    const isDragEvent = event.type.startsWith('drag')\n    const isDropEvent = event.type === 'drop'\n    const isInput = ['INPUT', 'BUTTON', 'SELECT', 'TEXTAREA'].includes(target.tagName) || target.isContentEditable\n\n    // any input event within node views should be ignored by ProseMirror\n    if (isInput && !isDropEvent && !isDragEvent) {\n      return true\n    }\n\n    const { isEditable } = this.editor\n    const { isDragging } = this\n    const isDraggable = !!this.node.type.spec.draggable\n    const isSelectable = NodeSelection.isSelectable(this.node)\n    const isCopyEvent = event.type === 'copy'\n    const isPasteEvent = event.type === 'paste'\n    const isCutEvent = event.type === 'cut'\n    const isClickEvent = event.type === 'mousedown'\n\n    // ProseMirror tries to drag selectable nodes\n    // even if `draggable` is set to `false`\n    // this fix prevents that\n    if (!isDraggable && isSelectable && isDragEvent && event.target === this.dom) {\n      event.preventDefault()\n    }\n\n    if (isDraggable && isDragEvent && !isDragging && event.target === this.dom) {\n      event.preventDefault()\n      return false\n    }\n\n    // we have to store that dragging started\n    if (isDraggable && isEditable && !isDragging && isClickEvent) {\n      const dragHandle = target.closest('[data-drag-handle]')\n      const isValidDragHandle = dragHandle && (this.dom === dragHandle || this.dom.contains(dragHandle))\n\n      if (isValidDragHandle) {\n        this.isDragging = true\n\n        document.addEventListener(\n          'dragend',\n          () => {\n            this.isDragging = false\n          },\n          { once: true },\n        )\n\n        document.addEventListener(\n          'drop',\n          () => {\n            this.isDragging = false\n          },\n          { once: true },\n        )\n\n        document.addEventListener(\n          'mouseup',\n          () => {\n            this.isDragging = false\n          },\n          { once: true },\n        )\n      }\n    }\n\n    // these events are handled by prosemirror\n    if (\n      isDragging\n      || isDropEvent\n      || isCopyEvent\n      || isPasteEvent\n      || isCutEvent\n      || (isClickEvent && isSelectable)\n    ) {\n      return false\n    }\n\n    return true\n  }\n\n  /**\n   * Called when a DOM [mutation](https://developer.mozilla.org/en-US/docs/Web/API/MutationObserver) or a selection change happens within the view.\n   * @return `false` if the editor should re-read the selection or re-parse the range around the mutation\n   * @return `true` if it can safely be ignored.\n   */\n  ignoreMutation(mutation: ViewMutationRecord) {\n    if (!this.dom || !this.contentDOM) {\n      return true\n    }\n\n    if (typeof this.options.ignoreMutation === 'function') {\n      return this.options.ignoreMutation({ mutation })\n    }\n\n    // a leaf/atom node is like a black box for ProseMirror\n    // and should be fully handled by the node view\n    if (this.node.isLeaf || this.node.isAtom) {\n      return true\n    }\n\n    // ProseMirror should handle any selections\n    if (mutation.type === 'selection') {\n      return false\n    }\n\n    // try to prevent a bug on iOS and Android that will break node views on enter\n    // this is because ProseMirror can’t preventDispatch on enter\n    // this will lead to a re-render of the node view on enter\n    // see: https://github.com/ueberdosis/tiptap/issues/1214\n    // see: https://github.com/ueberdosis/tiptap/issues/2534\n    if (\n      this.dom.contains(mutation.target)\n      && mutation.type === 'childList'\n      && (isiOS() || isAndroid())\n      && this.editor.isFocused\n    ) {\n      const changedNodes = [\n        ...Array.from(mutation.addedNodes),\n        ...Array.from(mutation.removedNodes),\n      ] as HTMLElement[]\n\n      // we’ll check if every changed node is contentEditable\n      // to make sure it’s probably mutated by ProseMirror\n      if (changedNodes.every(node => node.isContentEditable)) {\n        return false\n      }\n    }\n\n    // we will allow mutation contentDOM with attributes\n    // so we can for example adding classes within our node view\n    if (this.contentDOM === mutation.target && mutation.type === 'attributes') {\n      return true\n    }\n\n    // ProseMirror should handle any changes within contentDOM\n    if (this.contentDOM.contains(mutation.target)) {\n      return false\n    }\n\n    return true\n  }\n\n  /**\n   * Update the attributes of the prosemirror node.\n   */\n  updateAttributes(attributes: Record<string, any>): void {\n    this.editor.commands.command(({ tr }) => {\n      const pos = this.getPos()\n\n      if (typeof pos !== 'number') {\n        return false\n      }\n\n      tr.setNodeMarkup(pos, undefined, {\n        ...this.node.attrs,\n        ...attributes,\n      })\n\n      return true\n    })\n  }\n\n  /**\n   * Delete the node.\n   */\n  deleteNode(): void {\n    const from = this.getPos()\n\n    if (typeof from !== 'number') {\n      return\n    }\n    const to = from + this.node.nodeSize\n\n    this.editor.commands.deleteRange({ from, to })\n  }\n}\n", "import { MarkType } from '@tiptap/pm/model'\n\nimport { getMarksBetween } from '../helpers/getMarksBetween.js'\nimport { PasteRule, PasteRuleFinder } from '../PasteRule.js'\nimport { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an paste rule that adds a mark when the\n * matched text is pasted into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules\n */\nexport function markPasteRule(config: {\n  find: PasteRuleFinder\n  type: MarkType\n  getAttributes?:\n    | Record<string, any>\n    | ((match: ExtendedRegExpMatchArray, event: ClipboardEvent) => Record<string, any>)\n    | false\n    | null\n}) {\n  return new PasteRule({\n    find: config.find,\n    handler: ({\n      state, range, match, pasteEvent,\n    }) => {\n      const attributes = callOrReturn(config.getAttributes, undefined, match, pasteEvent)\n\n      if (attributes === false || attributes === null) {\n        return null\n      }\n\n      const { tr } = state\n      const captureGroup = match[match.length - 1]\n      const fullMatch = match[0]\n      let markEnd = range.to\n\n      if (captureGroup) {\n        const startSpaces = fullMatch.search(/\\S/)\n        const textStart = range.from + fullMatch.indexOf(captureGroup)\n        const textEnd = textStart + captureGroup.length\n\n        const excludedMarks = getMarksBetween(range.from, range.to, state.doc)\n          .filter(item => {\n            // @ts-ignore\n            const excluded = item.mark.type.excluded as MarkType[]\n\n            return excluded.find(type => type === config.type && type !== item.mark.type)\n          })\n          .filter(item => item.to > textStart)\n\n        if (excludedMarks.length) {\n          return null\n        }\n\n        if (textEnd < range.to) {\n          tr.delete(textEnd, range.to)\n        }\n\n        if (textStart > range.from) {\n          tr.delete(range.from + startSpaces, textStart)\n        }\n\n        markEnd = range.from + startSpaces + captureGroup.length\n\n        tr.addMark(range.from + startSpaces, markEnd, config.type.create(attributes || {}))\n\n        tr.removeStoredMark(config.type)\n      }\n    },\n  })\n}\n", "// source: https://stackoverflow.com/a/6969486\nexport function escapeForRegEx(string: string): string {\n  return string.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&')\n}\n", "export function isString(value: any): value is string {\n  return typeof value === 'string'\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { PasteRule, PasteRuleFinder } from '../PasteRule.js'\nimport { ExtendedRegExpMatchArray, JSONContent } from '../types.js'\nimport { callOrReturn } from '../utilities/index.js'\n\n/**\n * Build an paste rule that adds a node when the\n * matched text is pasted into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules\n */\nexport function nodePasteRule(config: {\n  find: PasteRuleFinder\n  type: NodeType\n  getAttributes?:\n    | Record<string, any>\n    | ((match: ExtendedRegExpMatchArray, event: ClipboardEvent) => Record<string, any>)\n    | false\n    | null\n  getContent?:\n    | JSONContent[]\n    | ((attrs: Record<string, any>) => JSONContent[])\n    | false\n    | null\n}) {\n  return new PasteRule({\n    find: config.find,\n    handler({\n      match, chain, range, pasteEvent,\n    }) {\n      const attributes = callOrReturn(config.getAttributes, undefined, match, pasteEvent)\n      const content = callOrReturn(config.getContent, undefined, attributes)\n\n      if (attributes === false || attributes === null) {\n        return null\n      }\n\n      const node = { type: config.type.name, attrs: attributes } as JSONContent\n\n      if (content) {\n        node.content = content\n      }\n\n      if (match.input) {\n        chain().deleteRange(range).insertContentAt(range.from, node)\n      }\n    },\n  })\n}\n", "import { PasteRule, PasteRuleFinder } from '../PasteRule.js'\n\n/**\n * Build an paste rule that replaces text when the\n * matched text is pasted into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules\n */\nexport function textPasteRule(config: {\n  find: PasteRuleFinder,\n  replace: string,\n}) {\n  return new PasteRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      let insert = config.replace\n      let start = range.from\n      const end = range.to\n\n      if (match[1]) {\n        const offset = match[0].lastIndexOf(match[1])\n\n        insert += match[0].slice(offset + match[1].length)\n        start += offset\n\n        const cutOff = start - end\n\n        if (cutOff > 0) {\n          insert = match[0].slice(offset - cutOff, offset) + insert\n          start = end\n        }\n      }\n\n      state.tr.insertText(insert, start, end)\n    },\n  })\n}\n", "import { Transaction } from '@tiptap/pm/state'\n\nexport interface TrackerResult {\n  position: number\n  deleted: boolean\n}\n\nexport class Tracker {\n  transaction: Transaction\n\n  currentStep: number\n\n  constructor(transaction: Transaction) {\n    this.transaction = transaction\n    this.currentStep = this.transaction.steps.length\n  }\n\n  map(position: number): TrackerResult {\n    let deleted = false\n\n    const mappedPosition = this.transaction.steps\n      .slice(this.currentStep)\n      .reduce((newPosition, step) => {\n        const mapResult = step.getMap().mapResult(newPosition)\n\n        if (mapResult.deleted) {\n          deleted = true\n        }\n\n        return mapResult.pos\n      }, position)\n\n    return {\n      position: mappedPosition,\n      deleted,\n    }\n  }\n}\n"], "names": ["run", "originalCreateParagraphNear", "originalDeleteSelection", "originalExitCode", "ProseMirrorNode", "originalJoinUp", "originalJoinDown", "originalJoinBackward", "originalJoin<PERSON>orward", "originalCommand", "originalLift", "originalLiftEmptyBlock", "originalLiftListItem", "originalNewlineInCode", "originalSelectNodeBackward", "originalSelectNodeForward", "originalSelectParentNode", "originalSelectTextblockEnd", "originalSelectTextblockStart", "Node", "originalSinkListItem", "originalWrapIn", "originalWrapInList"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;;;;CAIG,GACG,SAAU,oBAAoB,CAAC,MAGpC,EAAA;IACC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM;IACrC,IAAI,EAAE,SAAS,EAAE,GAAG,WAAW;IAC/B,IAAI,EAAE,GAAG,EAAE,GAAG,WAAW;IACzB,IAAI,EAAE,WAAW,EAAE,GAAG,WAAW;IAEjC,OAAO;QACL,GAAG,KAAK;QACR,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAC9B,gBAAgB,EAAE,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC;QACpD,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,MAAM,EAAE,KAAK,CAAC,MAAM;QACpB,WAAW,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;QAC1C,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;QAChC,IAAI,WAAW,IAAA;YACb,OAAO,WAAW;SACnB;QACD,IAAI,SAAS,IAAA;YACX,OAAO,SAAS;SACjB;QACD,IAAI,GAAG,IAAA;YACL,OAAO,GAAG;SACX;QACD,IAAI,EAAE,IAAA;YACJ,SAAS,GAAG,WAAW,CAAC,SAAS;YACjC,GAAG,GAAG,WAAW,CAAC,GAAG;YACrB,WAAW,GAAG,WAAW,CAAC,WAAW;YAErC,OAAO,WAAW;SACnB;KACF;AACH;MCjCa,cAAc,CAAA;IAOzB,WAAA,CAAY,KAA8C,CAAA;QACxD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM;QAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ;QACxD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,KAAK;;IAGhC,IAAI,cAAc,GAAA;QAChB,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW;;IAG3B,IAAI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK;;IAG9C,IAAI,QAAQ,GAAA;QACV,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI;QAC3C,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;QACvB,MAAM,EAAE,EAAE,EAAE,GAAG,KAAK;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;QAEjC,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,KAAI;YAClD,MAAM,MAAM,GAAG,CAAC,GAAG,IAAW,KAAI;gBAChC,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC;gBAExC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;oBAC1D,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;;gBAGnB,OAAO,QAAQ;YACjB,CAAC;YAED,OAAO;gBAAC,IAAI;gBAAE,MAAM;aAAC;SACtB,CAAC,CAC0B;;IAGhC,IAAI,KAAK,GAAA;QACP,OAAO,IAAM,IAAI,CAAC,WAAW,EAAE;;IAGjC,IAAI,GAAG,GAAA;QACL,OAAO,IAAM,IAAI,CAAC,SAAS,EAAE;;IAGxB,WAAW,CAAC,OAAqB,EAAE,cAAc,GAAG,IAAI,EAAA;QAC7D,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI;QAC3C,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;QACvB,MAAM,SAAS,GAAc,EAAE;QAC/B,MAAM,mBAAmB,GAAG,CAAC,CAAC,OAAO;QACrC,MAAM,EAAE,GAAG,OAAO,IAAI,KAAK,CAAC,EAAE;QAE9B,MAAM,GAAG,GAAG,MAAK;YACf,IACE,CAAC,uBACE,kBACA,CAAC,EAAE,CAAC,OAAO,CAAC,iBAAiB,KAC7B,CAAC,IAAI,CAAC,cAAc,EACvB;gBACA,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;;YAGnB,OAAO,SAAS,CAAC,KAAK,EAAC,QAAQ,GAAI,QAAQ,KAAK,IAAI,CAAC;QACvD,CAAC;QAED,MAAM,KAAK,GAAG;YACZ,GAAG,MAAM,CAAC,WAAW,CACnB,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,KAAI;gBAClD,MAAM,cAAc,GAAG,CAAC,GAAG,IAAa,KAAI;oBAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,cAAc,CAAC;oBACjD,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC;oBAExC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAExB,OAAO,KAAK;gBACd,CAAC;gBAED,OAAO;oBAAC,IAAI;oBAAE,cAAc;iBAAC;YAC/B,CAAC,CAAC,CACH;YACD,GAAG;SAC0B;QAE/B,OAAO,KAAK;;IAGP,SAAS,CAAC,OAAqB,EAAA;QACpC,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,IAAI;QACnC,MAAM,QAAQ,GAAG,KAAK;QACtB,MAAM,EAAE,GAAG,OAAO,IAAI,KAAK,CAAC,EAAE;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,QAAQ,CAAC;QAC3C,MAAM,iBAAiB,GAAG,MAAM,CAAC,WAAW,CAC1C,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,KAAI;YAClD,OAAO;gBAAC,IAAI;gBAAE,CAAC,GAAG,IAAa,GAAK,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;wBAAE,GAAG,KAAK;wBAAE,QAAQ,EAAE,SAAS;oBAAA,CAAE,CAAC;aAAC;SACzF,CAAC,CAC0B;QAE9B,OAAO;YACL,GAAG,iBAAiB;YACpB,KAAK,EAAE,IAAM,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,QAAQ,CAAC;SAC7B;;IAGX,UAAU,CAAC,EAAe,EAAE,cAAc,GAAG,IAAI,EAAA;QACtD,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI;QAC3C,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;QAEvB,MAAM,KAAK,GAAiB;YAC1B,EAAE;YACF,MAAM;YACN,IAAI;YACJ,KAAK,EAAE,oBAAoB,CAAC;gBAC1B,KAAK;gBACL,WAAW,EAAE,EAAE;aAChB,CAAC;YACF,QAAQ,EAAE,cAAc,GAAG,IAAM,SAAS,GAAG,SAAS;YACtD,KAAK,EAAE,IAAM,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,cAAc,CAAC;YACjD,GAAG,EAAE,IAAM,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7B,IAAI,QAAQ,IAAA;gBACV,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,KAAI;oBAClD,OAAO;wBAAC,IAAI;wBAAE,CAAC,GAAG,IAAa,GAAK,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC;qBAAC;iBAC7D,CAAC,CAC0B;aAC/B;SACF;QAED,OAAO,KAAK;;AAEf;MCtIY,YAAY,CAAA;IAAzB,WAAA,EAAA;QAEU,IAAS,CAAA,SAAA,GAAqD,CAAA,CAAE;;IAEjE,EAAE,CAAmC,KAAgB,EAAE,EAAkC,EAAA;QAC9F,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAC1B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE;;QAG5B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;QAE9B,OAAO,IAAI;;IAGN,IAAI,CAAmC,KAAgB,EAAE,GAAG,IAAgC,EAAA;QACjG,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;QAEvC,IAAI,SAAS,EAAE;YACb,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;;QAG3D,OAAO,IAAI;;IAGN,GAAG,CAAmC,KAAgB,EAAE,EAAmC,EAAA;QAChG,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;QAEvC,IAAI,SAAS,EAAE;YACb,IAAI,EAAE,EAAE;gBACN,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,MAAM,EAAC,QAAQ,GAAI,QAAQ,KAAK,EAAE,CAAC;mBAChE;gBACL,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;;;QAIhC,OAAO,IAAI;;IAGN,IAAI,CAAmC,KAAgB,EAAE,EAAkC,EAAA;QAChG,MAAM,MAAM,GAAG,CAAC,GAAG,IAAgC,KAAI;YACrD,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;YACvB,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;QACtB,CAAC;QAED,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;;IAGxB,kBAAkB,GAAA;QACvB,IAAI,CAAC,SAAS,GAAG,CAAA,CAAE;;AAEtB;AC1DD;;;;;;CAMG,YACa,iBAAiB,CAC/B,SAAuB,EACvB,KAAa,EACb,OAAmD,EAAA;IAGnD,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,SAAS,IAAI,SAAS,CAAC,MAAM,EAAE;QAC7D,OAAO,iBAAiB,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC;;IAG5D,IAAI,OAAO,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,UAAU,EAAE;QACjD,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;YACzC,GAAG,OAAO;YACV,MAAM,EAAE,SAAS,CAAC,MAAA,GACd,iBAAiB,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,IAClD,IAAI;QACT,CAAA,CAAC;QAEF,OAAO,KAAK;;IAGd,OAAO,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC;AAChC;AC1BM,SAAU,eAAe,CAAC,UAAsB,EAAA;IACpD,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,EAAC,SAAS,GAAI,SAAS,CAAC,IAAI,KAAK,WAAW,CAAgB;IACpG,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,EAAC,SAAS,GAAI,SAAS,CAAC,IAAI,KAAK,MAAM,CAAW;IAC1F,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,EAAC,SAAS,GAAI,SAAS,CAAC,IAAI,KAAK,MAAM,CAAW;IAE1F,OAAO;QACL,cAAc;QACd,cAAc;QACd,cAAc;KACf;AACH;ACJA;;;CAGG,GACG,SAAU,2BAA2B,CAAC,UAAsB,EAAA;IAChE,MAAM,mBAAmB,GAAyB,EAAE;IACpD,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,eAAe,CAAC,UAAU,CAAC;IACtE,MAAM,qBAAqB,GAAG,CAAC;WAAG,cAAc,EAAE;WAAG,cAAc;KAAC;IACpE,MAAM,gBAAgB,GAAwB;QAC5C,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,IAAI;QACd,UAAU,EAAE,IAAI;QAChB,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,IAAI;QACjB,UAAU,EAAE,KAAK;KAClB;IAED,UAAU,CAAC,OAAO,EAAC,SAAS,IAAG;QAC7B,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,UAAU,EAAE,qBAAqB;SAClC;QAED,MAAM,mBAAmB,GAAG,iBAAiB,CAC3C,SAAS,EACT,qBAAqB,EACrB,OAAO,CACR;QAED,IAAI,CAAC,mBAAmB,EAAE;YACxB;;QAGF,MAAM,gBAAgB,GAAG,mBAAmB,EAAE;QAE9C,gBAAgB,CAAC,OAAO,EAAC,eAAe,IAAG;YACzC,eAAe,CAAC,KAAK,CAAC,OAAO,EAAC,IAAI,IAAG;gBACnC,OACG,OAAO,CAAC,eAAe,CAAC,UAAU,EAClC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,SAAS,CAAC,KAAI;oBAC7B,mBAAmB,CAAC,IAAI,CAAC;wBACvB,IAAI;wBACJ,IAAI;wBACJ,SAAS,EAAE;4BACT,GAAG,gBAAgB;4BACnB,GAAG,SAAS;wBACb,CAAA;oBACF,CAAA,CAAC;gBACJ,CAAC,CAAC;YACN,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,qBAAqB,CAAC,OAAO,EAAC,SAAS,IAAG;QACxC,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;SAC3B;QAED,MAAM,aAAa,GAAG,iBAAiB,CACrC,SAAS,EACT,eAAe,EACf,OAAO,CACR;QAED,IAAI,CAAC,aAAa,EAAE;YAClB;;;QAIF,MAAM,UAAU,GAAG,aAAa,EAAgB;QAEhD,OACG,OAAO,CAAC,UAAU,EAClB,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,SAAS,CAAC,KAAI;YAC7B,MAAM,UAAU,GAAG;gBACjB,GAAG,gBAAgB;gBACnB,GAAG,SAAS;aACb;YAED,IAAI,OAAA,CAAO,UAAU,KAAA,IAAA,IAAV,UAAU,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAV,UAAU,CAAE,OAAO,CAAA,KAAK,UAAU,EAAE;gBAC7C,UAAU,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,EAAE;;YAG3C,IAAI,CAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAA,KAAA,IAAA,KAAA,IAAV,UAAU,CAAE,UAAU,KAAI,CAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAA,KAAA,IAAA,KAAA,IAAV,UAAU,CAAE,OAAO,MAAK,SAAS,EAAE;gBAC/D,OAAO,UAAU,CAAC,OAAO;;YAG3B,mBAAmB,CAAC,IAAI,CAAC;gBACvB,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,IAAI;gBACJ,SAAS,EAAE,UAAU;YACtB,CAAA,CAAC;QACJ,CAAC,CAAC;IACN,CAAC,CAAC;IAEF,OAAO,mBAAmB;AAC5B;AC7GgB,SAAA,WAAW,CAAC,UAA6B,EAAE,MAAc,EAAA;IACvE,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;QAClC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;YAC7B,MAAM,KAAK,CACT,CAAA,6BAAA,EAAgC,UAAU,CAAA,yCAAA,CAA2C,CACtF;;QAGH,OAAO,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC;;IAGjC,OAAO,UAAU;AACnB;ACdgB,SAAA,eAAe,CAAC,GAAG,OAA8B,EAAA;IAC/D,OAAO,QACJ,MAAM,EAAC,IAAI,GAAI,CAAC,CAAC,IAAI,EACrB,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;QACtB,MAAM,gBAAgB,GAAG;YAAE,GAAG,KAAK;QAAA,CAAE;QAErC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;YAC5C,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAAC;YAEpC,IAAI,CAAC,MAAM,EAAE;gBACX,gBAAgB,CAAC,GAAG,CAAC,GAAG,KAAK;gBAE7B;;YAGF,IAAI,GAAG,KAAK,OAAO,EAAE;gBACnB,MAAM,YAAY,GAAa,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;gBACpE,MAAM,eAAe,GAAa,gBAAgB,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;gBAE/F,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,EACvC,UAAU,GAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,CACpD;gBAED,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC;uBAAG,eAAe,EAAE;uBAAG,aAAa;iBAAC,CAAC,IAAI,CAAC,GAAG,CAAC;mBACnE,IAAI,GAAG,KAAK,OAAO,EAAE;gBAC1B,MAAM,SAAS,GAAa,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,KAAa,GAAK,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE;gBAC9G,MAAM,cAAc,GAAa,gBAAgB,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,KAAa,GAAK,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE;gBAEnJ,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAkB;gBAE1C,cAAc,CAAC,OAAO,CAAC,KAAK,IAAG;oBAC7B,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAC,IAAI,GAAI,IAAI,CAAC,IAAI,EAAE,CAAC;oBAEjE,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC;gBAC7B,CAAC,CAAC;gBAEF,SAAS,CAAC,OAAO,EAAC,KAAK,IAAG;oBACxB,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAC,IAAI,GAAI,IAAI,CAAC,IAAI,EAAE,CAAC;oBAEjE,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC;gBAC7B,CAAC,CAAC;gBAEF,gBAAgB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAK,GAAG,QAAQ,CAAA,EAAA,EAAK,GAAG,CAAA,CAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;mBAC5G;gBACL,gBAAgB,CAAC,GAAG,CAAC,GAAG,KAAK;;QAEjC,CAAC,CAAC;QAEF,OAAO,gBAAgB;KACxB,EAAE,CAAA,CAAE,CAAC;AACV;AC7CgB,SAAA,qBAAqB,CACnC,UAAuB,EACvB,mBAAyC,EAAA;IAEzC,OAAO,oBACJ,MAAM,EACL,SAAS,GAAI,SAAS,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,CAAC,IAAI,EAErD,MAAM,EAAC,IAAI,GAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EACtC,GAAG,EAAC,IAAI,IAAG;QACV,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;YAC9B,OAAO;gBACL,CAAC,IAAI,CAAC,IAAI,CAAA,EAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;aACzC;;QAGH,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAA,CAAE;IAC1D,CAAC,EACA,MAAM,CAAC,CAAC,UAAU,EAAE,SAAS,GAAK,eAAe,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,CAAA,CAAE,CAAC;AAClF;ACxBA,sEAAA;AACM,SAAU,UAAU,CAAC,KAAU,EAAA;IACnC,OAAO,OAAO,KAAK,KAAK,UAAU;AACpC;ACAA;;;;;;CAMG,GACG,SAAU,YAAY,CAAI,KAAQ,EAAE,OAAe,GAAA,SAAS,EAAE,GAAG,KAAY,EAAA;IACjF,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;QACrB,IAAI,OAAO,EAAE;YACX,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,CAAC;;QAGtC,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC;;IAGxB,OAAO,KAA2B;AACpC;ACpBgB,SAAA,aAAa,CAAC,KAAK,GAAG,CAAA,CAAE,EAAA;IACtC,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,WAAW,KAAK,MAAM;AACxE;ACFM,SAAU,UAAU,CAAC,KAAU,EAAA;IACnC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,KAAK;;IAGd,IAAI,KAAK,CAAC,KAAK,CAAC,sBAAsB,CAAC,EAAE;QACvC,OAAO,MAAM,CAAC,KAAK,CAAC;;IAGtB,IAAI,KAAK,KAAK,MAAM,EAAE;QACpB,OAAO,IAAI;;IAGb,IAAI,KAAK,KAAK,OAAO,EAAE;QACrB,OAAO,KAAK;;IAGd,OAAO,KAAK;AACd;ACbA;;;;;CAKG,GACa,SAAA,oCAAoC,CAClD,SAAoB,EACpB,mBAAyC,EAAA;IAEzC,IAAI,OAAO,IAAI,SAAS,EAAE;QACxB,OAAO,SAAS;;IAGlB,OAAO;QACL,GAAG,SAAS;QACZ,QAAQ,EAAE,CAAC,IAAiB,KAAI;YAC9B,MAAM,aAAa,GAAG,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK;YAErF,IAAI,aAAa,KAAK,KAAK,EAAE;gBAC3B,OAAO,KAAK;;YAGd,MAAM,aAAa,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;gBAC/D,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAA,GACzB,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,IAC7B,UAAU,CAAC,AAAC,IAAI,CAAE,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE9C,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;oBACzC,OAAO,KAAK;;gBAGd,OAAO;oBACL,GAAG,KAAK;oBACR,CAAC,IAAI,CAAC,IAAI,CAAA,EAAG,KAAK;iBACnB;aACF,EAAE,CAAA,CAAE,CAAC;YAEN,OAAO;gBAAE,GAAG,aAAa;gBAAE,GAAG,aAAa;YAAA,CAAE;SAC9C;KACF;AACH;AChCA,SAAS,iBAAiB,CAAI,IAAO,EAAA;IACnC,OAAO,MAAM,CAAC,WAAW;IAEvB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;QAC3C,IAAI,GAAG,KAAK,OAAO,IAAI,aAAa,CAAC,KAA2B,CAAC,EAAE;YACjE,OAAO,KAAK;;QAGd,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;KAC7C,CAAC,CACE;AACR;AAEA;;;;;CAKG,GACa,SAAA,6BAA6B,CAAC,UAAsB,EAAE,MAAe,EAAA;;IACnF,MAAM,aAAa,GAAG,2BAA2B,CAAC,UAAU,CAAC;IAC7D,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,eAAe,CAAC,UAAU,CAAC;IACtE,MAAM,OAAO,GAAG,CAAA,EAAA,GAAA,cAAc,CAAC,IAAI,EAAC,SAAS,GAAI,iBAAiB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,IAAI;IAE/F,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAC9B,cAAc,CAAC,GAAG,EAAC,SAAS,IAAG;QAC7B,MAAM,mBAAmB,GAAG,aAAa,CAAC,MAAM,EAC9C,SAAS,GAAI,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC/C;QACD,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,MAAM;SACP;QAED,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC,KAAI;YACtD,MAAM,gBAAgB,GAAG,iBAAiB,CACxC,CAAC,EACD,kBAAkB,EAClB,OAAO,CACR;YAED,OAAO;gBACL,GAAG,MAAM;gBACT,GAAI,gBAAgB,GAAG,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAA,CAAE,CAAC;aACzD;SACF,EAAE,CAAA,CAAE,CAAC;QAEN,MAAM,MAAM,GAAa,iBAAiB,CAAC;YACzC,GAAG,eAAe;YAClB,OAAO,EAAE,YAAY,CACnB,iBAAiB,CAAwB,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC,CACxE;YACD,KAAK,EAAE,YAAY,CAAC,iBAAiB,CAAsB,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YACxF,KAAK,EAAE,YAAY,CAAC,iBAAiB,CAAsB,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YACxF,MAAM,EAAE,YAAY,CAAC,iBAAiB,CAAuB,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC3F,IAAI,EAAE,YAAY,CAAC,iBAAiB,CAAqB,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YACrF,UAAU,EAAE,YAAY,CACtB,iBAAiB,CAA2B,SAAS,EAAE,YAAY,EAAE,OAAO,CAAC,CAC9E;YACD,SAAS,EAAE,YAAY,CACrB,iBAAiB,CAA0B,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAC5E;YACD,IAAI,EAAE,YAAY,CAAC,iBAAiB,CAAqB,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YACrF,UAAU,EAAE,YAAY,CAAC,iBAAiB,CAA2B,SAAS,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;YACvG,oBAAoB,EAAE,YAAY,CAAC,iBAAiB,CAAqC,SAAS,EAAE,sBAAsB,EAAE,OAAO,CAAC,CAAC;YACrI,QAAQ,EAAE,YAAY,CACpB,iBAAiB,CAAyB,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,CAC1E;YACD,SAAS,EAAE,YAAY,CACrB,iBAAiB,CAA0B,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAC5E;YACD,KAAK,EAAE,MAAM,CAAC,WAAW,CACvB,mBAAmB,CAAC,GAAG,EAAC,kBAAkB,IAAG;;gBAC3C,OAAO;oBAAC,kBAAkB,CAAC,IAAI;oBAAE;wBAAE,OAAO,EAAE,CAAA,EAAA,GAAA,kBAAkB,KAAA,QAAlB,kBAAkB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAlB,kBAAkB,CAAE,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,OAAO;oBAAA,CAAE;iBAAC;YACvF,CAAC,CAAC,CACH;QACF,CAAA,CAAC;QAEF,MAAM,SAAS,GAAG,YAAY,CAC5B,iBAAiB,CAA0B,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAC5E;QAED,IAAI,SAAS,EAAE;YACb,MAAM,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,IAAI,oCAAoC,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAmB;;QAGtI,MAAM,UAAU,GAAG,iBAAiB,CAClC,SAAS,EACT,YAAY,EACZ,OAAO,CACR;QAED,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,KAAK,IAAG,IAAI,GAAI,UAAU,CAAC;oBAChC,IAAI;oBACJ,cAAc,EAAE,qBAAqB,CAAC,IAAI,EAAE,mBAAmB,CAAC;gBACjE,CAAA,CAAC;;QAGJ,MAAM,UAAU,GAAG,iBAAiB,CAClC,SAAS,EACT,YAAY,EACZ,OAAO,CACR;QAED,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,MAAM,GAAG,UAAU;;QAG5B,OAAO;YAAC,SAAS,CAAC,IAAI;YAAE,MAAM;SAAC;KAChC,CAAC,CACH;IAED,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAC9B,cAAc,CAAC,GAAG,CAAC,SAAS,IAAG;QAC7B,MAAM,mBAAmB,GAAG,aAAa,CAAC,MAAM,EAC9C,SAAS,GAAI,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC/C;QACD,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,MAAM;SACP;QAED,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC,KAAI;YACtD,MAAM,gBAAgB,GAAG,iBAAiB,CACxC,CAAC,EACD,kBAAkB,EAClB,OAAO,CACR;YAED,OAAO;gBACL,GAAG,MAAM;gBACT,GAAI,gBAAgB,GAAG,gBAAgB,CAAC,SAAgB,CAAC,GAAG,CAAA,CAAE,CAAC;aAChE;SACF,EAAE,CAAA,CAAE,CAAC;QAEN,MAAM,MAAM,GAAa,iBAAiB,CAAC;YACzC,GAAG,eAAe;YAClB,SAAS,EAAE,YAAY,CACrB,iBAAiB,CAA0B,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAC5E;YACD,QAAQ,EAAE,YAAY,CACpB,iBAAiB,CAAyB,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,CAC1E;YACD,KAAK,EAAE,YAAY,CAAC,iBAAiB,CAAsB,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YACxF,QAAQ,EAAE,YAAY,CACpB,iBAAiB,CAAyB,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,CAC1E;YACD,IAAI,EAAE,YAAY,CAAC,iBAAiB,CAAqB,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YACrF,KAAK,EAAE,MAAM,CAAC,WAAW,CACvB,mBAAmB,CAAC,GAAG,EAAC,kBAAkB,IAAG;;gBAC3C,OAAO;oBAAC,kBAAkB,CAAC,IAAI;oBAAE;wBAAE,OAAO,EAAE,CAAA,EAAA,GAAA,kBAAkB,KAAA,QAAlB,kBAAkB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAlB,kBAAkB,CAAE,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,OAAO;oBAAA,CAAE;iBAAC;YACvF,CAAC,CAAC,CACH;QACF,CAAA,CAAC;QAEF,MAAM,SAAS,GAAG,YAAY,CAC5B,iBAAiB,CAA0B,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAC5E;QAED,IAAI,SAAS,EAAE;YACb,MAAM,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,EAAC,SAAS,GAAI,oCAAoC,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;;QAGpH,MAAM,UAAU,GAAG,iBAAiB,CAClC,SAAS,EACT,YAAY,EACZ,OAAO,CACR;QAED,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,KAAK,IAAG,IAAI,GAAI,UAAU,CAAC;oBAChC,IAAI;oBACJ,cAAc,EAAE,qBAAqB,CAAC,IAAI,EAAE,mBAAmB,CAAC;gBACjE,CAAA,CAAC;;QAGJ,OAAO;YAAC,SAAS,CAAC,IAAI;YAAE,MAAM;SAAC;KAChC,CAAC,CACH;IAED,OAAO,+NAAI,SAAM,CAAC;QAChB,OAAO;QACP,KAAK;QACL,KAAK;IACN,CAAA,CAAC;AACJ;AC1MA;;;;;CAKG,GACa,SAAA,mBAAmB,CAAC,IAAY,EAAE,MAAc,EAAA;IAC9D,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI;AACzD;ACRgB,SAAA,uBAAuB,CAAC,SAAuB,EAAE,OAAoB,EAAA;IACnF,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QAC1B,OAAO,OAAO,CAAC,IAAI,EAAC,gBAAgB,IAAG;YACrC,MAAM,IAAI,GAAG,OAAO,gBAAgB,KAAK,WACrC,mBACA,gBAAgB,CAAC,IAAI;YAEzB,OAAO,IAAI,KAAK,SAAS,CAAC,IAAI;QAChC,CAAC,CAAC;;IAGJ,OAAO,OAAO;AAChB;ACZgB,SAAA,mBAAmB,CAAC,QAAkB,EAAE,MAAc,EAAA;IACpE,MAAM,gBAAgB,8NAAG,gBAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC;IAErF,MAAM,iBAAiB,GAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,EAAE;IACtE,MAAM,SAAS,GAAG,iBAAiB,CAAC,aAAa,CAAC,KAAK,CAAC;IAExD,SAAS,CAAC,WAAW,CAAC,gBAAgB,CAAC;IAEvC,OAAO,SAAS,CAAC,SAAS;AAC5B;ACTA;;;;;CAKG,GACU,MAAA,uBAAuB,GAAG,CAAC,KAAkB,EAAE,QAAQ,GAAG,GAAG,KAAI;IAC5E,IAAI,UAAU,GAAG,EAAE;IAEnB,MAAM,WAAW,GAAG,KAAK,CAAC,YAAY;IAEtC,KAAK,CAAC,MAAM,CAAC,YAAY,CACvB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,QAAQ,CAAC,EACnC,WAAW,EACX,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,KAAI;;QAC3B,MAAM,KAAK,GAAG,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAC,MAAM,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,EAAA;YACpC,IAAI;YACJ,GAAG;YACH,MAAM;YACN,KAAK;SACN,CAAC,KACG,IAAI,CAAC,WAAA,IACL,QAAQ;QAEb,UAAU,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,GAAG,CAAC,CAAC;IACpG,CAAC,CACF;IAED,OAAO,UAAU;AACnB;AC/BM,SAAU,QAAQ,CAAC,KAAU,EAAA;IACjC,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,iBAAiB;AACpE;MCyBa,SAAS,CAAA;IAYpB,WAAA,CAAY,MAUX,CAAA;QACC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QACvB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO;;AAEhC;AAED,MAAM,uBAAuB,GAAG,CAC9B,IAAY,EACZ,IAAqB,KACc;IACnC,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;;IAGxB,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC;IAEjC,IAAI,CAAC,cAAc,EAAE;QACnB,OAAO,IAAI;;IAGb,MAAM,MAAM,GAA6B;QAAC,cAAc,CAAC,IAAI;KAAC;IAE9D,MAAM,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK;IACnC,MAAM,CAAC,KAAK,GAAG,IAAI;IACnB,MAAM,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI;IAEjC,IAAI,cAAc,CAAC,WAAW,EAAE;QAC9B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;YAC7D,OAAO,CAAC,IAAI,CACV,oFAAoF,CACrF;;QAGH,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;;IAGzC,OAAO,MAAM;AACf,CAAC;AAED,SAASA,KAAG,CAAC,MAOZ,EAAA;;IACC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EACtC,GAAG,MAAM;IACV,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IAEvB,IAAI,IAAI,CAAC,SAAS,EAAE;QAClB,OAAO,KAAK;;IAGd,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;IAE1C;IAEE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAA,IAEpB,CAAC,CAAA,CAAC,CAAA,EAAA,GAAC,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,SAAS,AAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,KAAK,CAAC,IAAI,EAAC,IAAI,GAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,EACnF;QACA,OAAO,KAAK;;IAGd,IAAI,OAAO,GAAG,KAAK;IAEnB,MAAM,UAAU,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,IAAI;IAExD,KAAK,CAAC,OAAO,EAAC,IAAI,IAAG;QACnB,IAAI,OAAO,EAAE;YACX;;QAGF,MAAM,KAAK,GAAG,uBAAuB,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC;QAE5D,IAAI,CAAC,KAAK,EAAE;YACV;;QAGF,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE;QACxB,MAAM,KAAK,GAAG,oBAAoB,CAAC;YACjC,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,WAAW,EAAE,EAAE;QAChB,CAAA,CAAC;QACF,MAAM,KAAK,GAAG;YACZ,IAAI,EAAE,IAAI,GAAA,CAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC5C,EAAE;SACH;QAED,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,cAAc,CAAC;YAClD,MAAM;YACN,KAAK;QACN,CAAA,CAAC;QAEF,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;YAC3B,KAAK;YACL,KAAK;YACL,KAAK;YACL,QAAQ;YACR,KAAK;YACL,GAAG;QACJ,CAAA,CAAC;;QAGF,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE;YACxC;;;;QAKF,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE;YACjB,SAAS,EAAE,EAAE;YACb,IAAI;YACJ,EAAE;YACF,IAAI;QACL,CAAA,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;QACjB,OAAO,GAAG,IAAI;IAChB,CAAC,CAAC;IAEF,OAAO,OAAO;AAChB;AAEA;;;;CAIG,GACG,SAAU,gBAAgB,CAAC,KAA6C,EAAA;IAC5E,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK;IAC/B,MAAM,MAAM,GAAG,6NAAI,UAAM,CAAC;QACxB,KAAK,EAAE;YACL,IAAI,GAAA;gBACF,OAAO,IAAI;aACZ;YACD,KAAK,EAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAA;gBACnB,MAAM,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC;gBAEjC,IAAI,MAAM,EAAE;oBACV,OAAO,MAAM;;;gBAIf,MAAM,kBAAkB,GAAG,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAKlD;gBACL,MAAM,gBAAgB,GAAG,CAAC,CAAC,kBAAkB;gBAE7C,IAAI,gBAAgB,EAAE;oBACpB,UAAU,CAAC,MAAK;wBACd,IAAI,EAAE,IAAI,EAAE,GAAG,kBAAkB;wBAEjC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;4BAC5B,IAAI,GAAG,IAAc;+BAChB;4BACL,IAAI,GAAG,mBAAmB,4NAAC,WAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC;;wBAG/D,MAAM,EAAE,IAAI,EAAE,GAAG,kBAAkB;wBACnC,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM;wBAE7BA,KAAG,CAAC;4BACF,MAAM;4BACN,IAAI;4BACJ,EAAE;4BACF,IAAI;4BACJ,KAAK;4BACL,MAAM;wBACP,CAAA,CAAC;oBACJ,CAAC,CAAC;;gBAGJ,OAAO,EAAE,CAAC,YAAY,IAAI,EAAE,CAAC,UAAU,GAAG,IAAI,GAAG,IAAI;aACtD;QACF,CAAA;QAED,KAAK,EAAE;YACL,eAAe,EAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAA;gBAClC,OAAOA,KAAG,CAAC;oBACT,MAAM;oBACN,IAAI;oBACJ,EAAE;oBACF,IAAI;oBACJ,KAAK;oBACL,MAAM;gBACP,CAAA,CAAC;aACH;YAED,eAAe,EAAE;gBACf,cAAc,GAAE,IAAI,IAAG;oBACrB,UAAU,CAAC,MAAK;wBACd,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,SAA0B;wBAEzD,IAAI,OAAO,EAAE;4BACXA,KAAG,CAAC;gCACF,MAAM;gCACN,IAAI,EAAE,OAAO,CAAC,GAAG;gCACjB,EAAE,EAAE,OAAO,CAAC,GAAG;gCACf,IAAI,EAAE,EAAE;gCACR,KAAK;gCACL,MAAM;4BACP,CAAA,CAAC;;oBAEN,CAAC,CAAC;oBAEF,OAAO,KAAK;iBACb;YACF,CAAA;;;YAID,aAAa,EAAC,IAAI,EAAE,KAAK,EAAA;gBACvB,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,EAAE;oBACzB,OAAO,KAAK;;gBAGd,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,SAA0B;gBAEzD,IAAI,OAAO,EAAE;oBACX,OAAOA,KAAG,CAAC;wBACT,MAAM;wBACN,IAAI,EAAE,OAAO,CAAC,GAAG;wBACjB,EAAE,EAAE,OAAO,CAAC,GAAG;wBACf,IAAI,EAAE,IAAI;wBACV,KAAK;wBACL,MAAM;oBACP,CAAA,CAAC;;gBAGJ,OAAO,KAAK;aACb;QACF,CAAA;;QAGD,YAAY,EAAE,IAAI;IACnB,CAAA,CAAW;IAEZ,OAAO,MAAM;AACf;ACtSA,sGAAA;AAEA,SAAS,OAAO,CAAC,KAAU,EAAA;IACzB,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3D;AAEM,SAAU,aAAa,CAAC,KAAU,EAAA;IACtC,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE;QAC/B,OAAO,KAAK;;IAGd,OAAO,KAAK,CAAC,WAAW,KAAK,MAAM,IAAI,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,SAAS;AAC1F;ACVgB,SAAA,SAAS,CAAC,MAA2B,EAAE,MAA2B,EAAA;IAChF,MAAM,MAAM,GAAG;QAAE,GAAG,MAAM;IAAA,CAAE;IAE5B,IAAI,aAAa,CAAC,MAAM,CAAC,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE;QAClD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,EAAC,GAAG,IAAG;YAChC,IAAI,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;gBAC5D,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;mBAC5C;gBACL,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC;;QAE7B,CAAC,CAAC;;IAGJ,OAAO,MAAM;AACf;ACmgBA;;;CAGG,SACU,IAAI,CAAA;IAkBf,WAAA,CAAY,SAAgD,CAAA,CAAE,CAAA;QAjB9D,IAAI,CAAA,IAAA,GAAG,MAAM;QAEb,IAAI,CAAA,IAAA,GAAG,MAAM;QAEb,IAAM,CAAA,MAAA,GAAgB,IAAI;QAE1B,IAAK,CAAA,KAAA,GAAgB,IAAI;QAMzB,IAAA,CAAA,MAAM,GAAe;YACnB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,cAAc,EAAE,CAAA,CAAE;SACnB;QAGC,IAAI,CAAC,MAAM,GAAG;YACZ,GAAG,IAAI,CAAC,MAAM;YACd,GAAG,MAAM;SACV;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;QAE5B,IAAI,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1E,OAAO,CAAC,IAAI,CACV,CAAA,sHAAA,EAAyH,IAAI,CAAC,IAAI,CAAI,EAAA,CAAA,CACvI;;;QAIH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc;QAEzC,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YAC1B,IAAI,CAAC,OAAO,GAAG,YAAY,CACzB,iBAAiB,CAA0B,IAAI,EAAE,YAAY,EAAE;gBAC7D,IAAI,EAAE,IAAI,CAAC,IAAI;YAChB,CAAA,CAAC,CACH;;QAGH,IAAI,CAAC,OAAO,GAAG,YAAY,CACzB,iBAAiB,CAA0B,IAAI,EAAE,YAAY,EAAE;YAC7D,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC,CACH,IAAI,CAAA,CAAE;;IAGT,OAAO,MAAM,CAAmB,MAAA,GAAoC,CAAA,CAAE,EAAA;QACpE,OAAO,IAAI,IAAI,CAAO,MAAM,CAAC;;IAG/B,SAAS,CAAC,UAA4B,CAAA,CAAE,EAAA;;;QAGtC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAmB;YAC9C,GAAG,IAAI,CAAC,MAAM;YACd,UAAU,EAAE,MAAK;gBACf,OAAO,SAAS,CAAC,IAAI,CAAC,OAA8B,EAAE,OAAO,CAAY;aAC1E;QACF,CAAA,CAAC;;QAGF,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;;QAE1B,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;QAE9B,OAAO,SAAS;;IAGlB,MAAM,CACJ,iBAAwE,CAAA,CAAE,EAAA;QAE1E,MAAM,SAAS,GAAG,IAAI,IAAI,CAAmC,cAAc,CAAC;QAE5E,SAAS,CAAC,MAAM,GAAG,IAAI;QAEvB,IAAI,CAAC,KAAK,GAAG,SAAS;QAEtB,SAAS,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI;QAElF,IAAI,cAAc,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1F,OAAO,CAAC,IAAI,CACV,CAAA,sHAAA,EAAyH,SAAS,CAAC,IAAI,CAAI,EAAA,CAAA,CAC5I;;QAGH,SAAS,CAAC,OAAO,GAAG,YAAY,CAC9B,iBAAiB,CAA0B,SAAS,EAAE,YAAY,EAAE;YAClE,IAAI,EAAE,SAAS,CAAC,IAAI;QACrB,CAAA,CAAC,CACH;QAED,SAAS,CAAC,OAAO,GAAG,YAAY,CAC9B,iBAAiB,CAA0B,SAAS,EAAE,YAAY,EAAE;YAClE,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,OAAO,EAAE,SAAS,CAAC,OAAO;QAC3B,CAAA,CAAC,CACH;QAED,OAAO,SAAS;;IAGlB,OAAO,UAAU,CAAC,EAAE,MAAM,EAAE,IAAI,EAAkC,EAAA;QAChE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM,CAAC,KAAK;QAC3B,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK;QAC/C,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,KAAK,UAAU,CAAC,GAAG,EAAE;QAEnD,IAAI,OAAO,EAAE;YACX,MAAM,YAAY,GAAG,UAAU,CAAC,KAAK,EAAE;YACvC,MAAM,QAAQ,GAAG,CAAC,CAAC,YAAY,CAAC,IAAI,EAAC,CAAC,GAAI,CAAA,CAAC,KAAA,IAAA,IAAD,CAAC,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAD,CAAC,CAAE,IAAI,CAAC,IAAI,MAAK,IAAI,CAAC,IAAI,CAAC;YAErE,IAAI,CAAC,QAAQ,EAAE;gBACb,OAAO,KAAK;;YAGd,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAA,CAAC,KAAA,QAAD,CAAC,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAD,CAAC,CAAE,IAAI,CAAC,IAAI,MAAK,IAAI,CAAC,IAAI,CAAC;YAErE,IAAI,UAAU,EAAE;gBACd,EAAE,CAAC,gBAAgB,CAAC,UAAU,CAAC;;YAEjC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC;YAElC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAExB,OAAO,IAAI;;QAGb,OAAO,KAAK;;AAEf;AC5pBK,SAAU,QAAQ,CAAC,KAAU,EAAA;IACjC,OAAO,OAAO,KAAK,KAAK,QAAQ;AAClC;AC2BA;;;CAGG,SACU,SAAS,CAAA;IAcpB,WAAA,CAAY,MAYX,CAAA;QACC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QACvB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO;;AAEhC;AAED,MAAM,uBAAuB,GAAG,CAC9B,IAAY,EACZ,IAAqB,EACrB,KAA6B,KACC;IAC9B,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;QAClB,OAAO,CAAC;eAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;SAAC;;IAGjC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;IAEjC,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,EAAE;;IAGX,OAAO,OAAO,CAAC,GAAG,EAAC,cAAc,IAAG;QAClC,MAAM,MAAM,GAA6B;YAAC,cAAc,CAAC,IAAI;SAAC;QAE9D,MAAM,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK;QACnC,MAAM,CAAC,KAAK,GAAG,IAAI;QACnB,MAAM,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI;QAEjC,IAAI,cAAc,CAAC,WAAW,EAAE;YAC9B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;gBAC7D,OAAO,CAAC,IAAI,CACV,oFAAoF,CACrF;;YAGH,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;;QAGzC,OAAO,MAAM;IACf,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,GAAG,CAAC,MAQZ,EAAA;IACC,MAAM,EACJ,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EACrD,GAAG,MAAM;IAEV,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,cAAc,CAAC;QAClD,MAAM;QACN,KAAK;IACN,CAAA,CAAC;IAEF,MAAM,QAAQ,GAAoB,EAAE;IAEpC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,KAAI;QAC7C,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAC5C;;QAGF,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;QACxC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QACxD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,GAAG,EAAE,UAAU,GAAG,GAAG,EAAE,SAAS,EAAE,QAAQ,CAAC;QAE/F,MAAM,OAAO,GAAG,uBAAuB,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;QAE3E,OAAO,CAAC,OAAO,EAAC,KAAK,IAAG;YACtB,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC7B;;YAGF,MAAM,KAAK,GAAG,YAAY,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC;YAC5C,MAAM,GAAG,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM;YACnC,MAAM,KAAK,GAAG;gBACZ,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;gBACjC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;aAC9B;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;gBAC3B,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;gBACR,KAAK;gBACL,GAAG;gBACH,UAAU;gBACV,SAAS;YACV,CAAA,CAAC;YAEF,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;QACxB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,CAAC;IAE3D,OAAO,OAAO;AAChB;AAEA,8FAAA;AACA,IAAI,yBAAyB,GAAkB,IAAI;AAEnD,MAAM,yBAAyB,GAAG,CAAC,IAAY,KAAI;;IACjD,MAAM,KAAK,GAAG,IAAI,cAAc,CAAC,OAAO,EAAE;QACxC,aAAa,EAAE,IAAI,YAAY,EAAE;IAClC,CAAA,CAAC;IAEF,CAAA,EAAA,GAAA,KAAK,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC;IAE/C,OAAO,KAAK;AACd,CAAC;AAED;;;;CAIG,GACG,SAAU,gBAAgB,CAAC,KAA6C,EAAA;IAC5E,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK;IAC/B,IAAI,iBAAiB,GAAmB,IAAI;IAC5C,IAAI,uBAAuB,GAAG,KAAK;IACnC,IAAI,wBAAwB,GAAG,KAAK;IACpC,IAAI,UAAU,GAAG,OAAO,cAAc,KAAK,WAAW,GAAG,IAAI,cAAc,CAAC,OAAO,CAAC,GAAG,IAAI;IAC3F,IAAI,SAA2B;IAE/B,IAAI;QACF,SAAS,GAAG,OAAO,SAAS,KAAK,WAAW,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI;MAC3E,OAAM;QACN,SAAS,GAAG,IAAI;;IAGlB,MAAM,YAAY,GAAG,CAAC,EACpB,KAAK,EACL,IAAI,EACJ,EAAE,EACF,IAAI,EACJ,QAAQ,EAOT,KAAI;QACH,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE;QACnB,MAAM,cAAc,GAAG,oBAAoB,CAAC;YAC1C,KAAK;YACL,WAAW,EAAE,EAAE;QAChB,CAAA,CAAC;QAEF,MAAM,OAAO,GAAG,GAAG,CAAC;YAClB,MAAM;YACN,KAAK,EAAE,cAAc;YACrB,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;YAC3B,EAAE,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC;YACZ,IAAI;YACJ,UAAU,EAAE,QAAQ;YACpB,SAAS;QACV,CAAA,CAAC;QAEF,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE;YAChC;;QAGF,IAAI;YACF,SAAS,GAAG,OAAO,SAAS,KAAK,WAAW,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI;UAC3E,OAAM;YACN,SAAS,GAAG,IAAI;;QAElB,UAAU,GAAG,OAAO,cAAc,KAAK,WAAW,GAAG,IAAI,cAAc,CAAC,OAAO,CAAC,GAAG,IAAI;QAEvF,OAAO,EAAE;IACX,CAAC;IAED,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,EAAC,IAAI,IAAG;QAC/B,OAAO,IAAI,mOAAM,CAAC;;YAEhB,IAAI,EAAC,IAAI,EAAA;gBACP,MAAM,eAAe,GAAG,CAAC,KAAgB,KAAI;;oBAC3C,iBAAiB,GAAG,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,GAAG,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,QAAQ,CAAC,KAAK,CAAC,MAAiB,CAAC,IACzE,IAAI,CAAC,GAAG,CAAC,aAAA,GACT,IAAI;oBAER,IAAI,iBAAiB,EAAE;wBACrB,yBAAyB,GAAG,MAAM;;gBAEtC,CAAC;gBAED,MAAM,aAAa,GAAG,MAAK;oBACzB,IAAI,yBAAyB,EAAE;wBAC7B,yBAAyB,GAAG,IAAI;;gBAEpC,CAAC;gBAED,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,eAAe,CAAC;gBACrD,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC;gBAEjD,OAAO;oBACL,OAAO,GAAA;wBACL,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,eAAe,CAAC;wBACxD,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,aAAa,CAAC;qBACrD;iBACF;aACF;YAED,KAAK,EAAE;gBACL,eAAe,EAAE;oBACf,IAAI,EAAE,CAAC,IAAI,EAAE,KAAY,KAAI;wBAC3B,wBAAwB,GAAG,iBAAiB,KAAK,IAAI,CAAC,GAAG,CAAC,aAAa;wBACvE,SAAS,GAAG,KAAkB;wBAE9B,IAAI,CAAC,wBAAwB,EAAE;4BAC7B,MAAM,mBAAmB,GAAG,yBAAyB;4BAErD,IAAI,mBAAmB,EAAE;;gCAEvB,UAAU,CAAC,MAAK;oCACd,MAAM,SAAS,GAAG,mBAAmB,CAAC,KAAK,CAAC,SAAS;oCAErD,IAAI,SAAS,EAAE;wCACb,mBAAmB,CAAC,QAAQ,CAAC,WAAW,CAAC;4CAAE,IAAI,EAAE,SAAS,CAAC,IAAI;4CAAE,EAAE,EAAE,SAAS,CAAC,EAAE;wCAAA,CAAE,CAAC;;iCAEvF,EAAE,EAAE,CAAC;;;wBAGV,OAAO,KAAK;qBACb;oBAED,KAAK,EAAE,CAAC,KAAK,EAAE,KAAY,KAAI;;wBAC7B,MAAM,IAAI,GAAG,CAAA,EAAA,GAAC,KAAwB,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,CAAC,WAAW,CAAC;wBAE1E,UAAU,GAAG,KAAuB;wBAEpC,uBAAuB,GAAG,CAAC,CAAA,CAAC,IAAI,KAAJ,IAAA,IAAA,IAAI,KAAJ,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,IAAI,CAAE,QAAQ,CAAC,eAAe,CAAC,CAAA;wBAE3D,OAAO,KAAK;qBACb;gBACF,CAAA;YACF,CAAA;YAED,iBAAiB,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,KAAK,KAAI;gBACnD,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC;gBACnC,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,OAAO,IAAI,CAAC,uBAAuB;gBACtF,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,MAAM,IAAI,CAAC,wBAAwB;;gBAGrF,MAAM,kBAAkB,GAAG,WAAW,CAAC,OAAO,CAAC,iBAAiB,CAED;gBAC/D,MAAM,gBAAgB,GAAG,CAAC,CAAC,kBAAkB;gBAE7C,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,gBAAgB,EAAE;oBAC5C;;;gBAIF,IAAI,gBAAgB,EAAE;oBACpB,IAAI,EAAE,IAAI,EAAE,GAAG,kBAAkB;oBAEjC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;wBAC5B,IAAI,GAAG,IAAc;2BAChB;wBACL,IAAI,GAAG,mBAAmB,4NAAC,WAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC;;oBAG/D,MAAM,EAAE,IAAI,EAAE,GAAG,kBAAkB;oBACnC,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM;oBAE7B,MAAM,QAAQ,GAAG,yBAAyB,CAAC,IAAI,CAAC;oBAEhD,OAAO,YAAY,CAAC;wBAClB,IAAI;wBACJ,KAAK;wBACL,IAAI;wBACJ,EAAE,EAAE;4BAAE,CAAC,EAAE,EAAE;wBAAA,CAAE;wBACb,QAAQ;oBACT,CAAA,CAAC;;;gBAIJ,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;gBAClE,MAAM,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;;gBAG9D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE;oBAC3C;;gBAGF,OAAO,YAAY,CAAC;oBAClB,IAAI;oBACJ,KAAK;oBACL,IAAI;oBACJ,EAAE;oBACF,QAAQ,EAAE,UAAU;gBACrB,CAAA,CAAC;aACH;QACF,CAAA,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO,OAAO;AAChB;AC7WM,SAAU,cAAc,CAAC,KAAY,EAAA;IACzC,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,KAAK,GAAK,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC;IAEzE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;AACtC;MCkBa,gBAAgB,CAAA;IAS3B,WAAY,CAAA,UAAsB,EAAE,MAAc,CAAA;QAFlD,IAAe,CAAA,eAAA,GAAa,EAAE;QAG5B,IAAI,CAAC,MAAM,GAAG,MAAM;QACpB,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,OAAO,CAAC,UAAU,CAAC;QACtD,IAAI,CAAC,MAAM,GAAG,6BAA6B,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC;QACpE,IAAI,CAAC,eAAe,EAAE;;IAGxB;;;;;KAKG,GACH,OAAO,OAAO,CAAC,UAAsB,EAAA;QACnC,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACtF,MAAM,eAAe,GAAG,cAAc,CAAC,kBAAkB,CAAC,GAAG,EAAC,SAAS,GAAI,SAAS,CAAC,IAAI,CAAC,CAAC;QAE3F,IAAI,eAAe,CAAC,MAAM,EAAE;YAC1B,OAAO,CAAC,IAAI,CACV,CAAA,iDAAA,EAAoD,gBACjD,GAAG,EAAC,IAAI,GAAI,CAAI,CAAA,EAAA,IAAI,CAAA,CAAA,CAAG,EACvB,IAAI,CAAC,IAAI,CAAC,CAAA,2BAAA,CAA6B,CAC3C;;QAGH,OAAO,kBAAkB;;IAG3B;;;;KAIG,GACH,OAAO,OAAO,CAAC,UAAsB,EAAA;QACnC,OACE,WACG,GAAG,EAAC,SAAS,IAAG;YACf,MAAM,OAAO,GAAG;gBACd,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;aAC3B;YAED,MAAM,aAAa,GAAG,iBAAiB,CACrC,SAAS,EACT,eAAe,EACf,OAAO,CACR;YAED,IAAI,aAAa,EAAE;gBACjB,OAAO;oBAAC,SAAS,EAAE;uBAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;iBAAC;;YAGtD,OAAO,SAAS;QAClB,CAAC;SAEA,IAAI,CAAC,EAAE,CAAC;;IAIf;;;;KAIG,GACH,OAAO,IAAI,CAAC,UAAsB,EAAA;QAChC,MAAM,eAAe,GAAG,GAAG;QAE3B,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAI;YAC9B,MAAM,SAAS,GAAG,iBAAiB,CAAwB,CAAC,EAAE,UAAU,CAAC,IAAI,eAAe;YAC5F,MAAM,SAAS,GAAG,iBAAiB,CAAwB,CAAC,EAAE,UAAU,CAAC,IAAI,eAAe;YAE5F,IAAI,SAAS,GAAG,SAAS,EAAE;gBACzB,OAAO,CAAC,CAAC;;YAGX,IAAI,SAAS,GAAG,SAAS,EAAE;gBACzB,OAAO,CAAC;;YAGV,OAAO,CAAC;QACV,CAAC,CAAC;;IAGJ;;;KAGG,GACH,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,SAAS,KAAI;YACpD,MAAM,OAAO,GAAG;gBACd,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,IAAI,EAAE,mBAAmB,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;aACvD;YAED,MAAM,WAAW,GAAG,iBAAiB,CACnC,SAAS,EACT,aAAa,EACb,OAAO,CACR;YAED,IAAI,CAAC,WAAW,EAAE;gBAChB,OAAO,QAAQ;;YAGjB,OAAO;gBACL,GAAG,QAAQ;gBACX,GAAG,WAAW,EAAE;aACjB;SACF,EAAE,CAAA,CAAiB,CAAC;;IAGvB;;;KAGG,GACH,IAAI,OAAO,GAAA;QACT,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI;;;;;;QAOvB,MAAM,UAAU,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;eAAG,IAAI,CAAC,UAAU;SAAC,CAAC,OAAO,EAAE,CAAC;QAExE,MAAM,UAAU,GAAgB,EAAE;QAClC,MAAM,UAAU,GAAgB,EAAE;QAElC,MAAM,UAAU,GAAG,WAChB,GAAG,EAAC,SAAS,IAAG;YACf,MAAM,OAAO,GAAG;gBACd,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,MAAM;gBACN,IAAI,EAAE,mBAAmB,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;aACvD;YAED,MAAM,OAAO,GAAa,EAAE;YAE5B,MAAM,oBAAoB,GAAG,iBAAiB,CAC5C,SAAS,EACT,sBAAsB,EACtB,OAAO,CACR;YAED,IAAI,eAAe,GAAkC,CAAA,CAAE;;YAGvD,IAAI,SAAS,CAAC,IAAI,KAAK,MAAM,IAAI,iBAAiB,CAAwB,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,EAAE;gBACzG,eAAe,CAAC,UAAU,GAAG,IAAM,IAAI,CAAC,UAAU,CAAC;wBAAE,MAAM;wBAAE,IAAI,EAAE,SAAiB;oBAAA,CAAE,CAAC;;YAGzF,IAAI,oBAAoB,EAAE;gBACxB,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,CACjC,MAAM,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAI;oBAChE,OAAO;wBAAC,QAAQ;wBAAE,IAAM,MAAM,CAAC;gCAAE,MAAM;4BAAA,CAAE,CAAC;qBAAC;iBAC5C,CAAC,CACH;gBAED,eAAe,GAAG;oBAAE,GAAG,eAAe;oBAAE,GAAG,QAAQ;gBAAA,CAAE;;YAGvD,MAAM,YAAY,mOAAG,SAAA,AAAM,EAAC,eAAe,CAAC;YAE5C,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;YAE1B,MAAM,aAAa,GAAG,iBAAiB,CACrC,SAAS,EACT,eAAe,EACf,OAAO,CACR;YAED,IAAI,uBAAuB,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,aAAa,EAAE;gBACxF,UAAU,CAAC,IAAI,CAAC,GAAG,aAAa,EAAE,CAAC;;YAGrC,MAAM,aAAa,GAAG,iBAAiB,CACrC,SAAS,EACT,eAAe,EACf,OAAO,CACR;YAED,IAAI,uBAAuB,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,aAAa,EAAE;gBACxF,UAAU,CAAC,IAAI,CAAC,GAAG,aAAa,EAAE,CAAC;;YAGrC,MAAM,qBAAqB,GAAG,iBAAiB,CAC7C,SAAS,EACT,uBAAuB,EACvB,OAAO,CACR;YAED,IAAI,qBAAqB,EAAE;gBACzB,MAAM,kBAAkB,GAAG,qBAAqB,EAAE;gBAElD,OAAO,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC;;YAGrC,OAAO,OAAO;QAChB,CAAC,EACA,IAAI,EAAE;QAET,OAAO;YACL,gBAAgB,CAAC;gBACf,MAAM;gBACN,KAAK,EAAE,UAAU;aAClB,CAAC;eACC,gBAAgB,CAAC;gBAClB,MAAM;gBACN,KAAK,EAAE,UAAU;aAClB,CAAC;eACC,UAAU;SACd;;IAGH;;;KAGG,GACH,IAAI,UAAU,GAAA;QACZ,OAAO,2BAA2B,CAAC,IAAI,CAAC,UAAU,CAAC;;IAGrD;;;KAGG,GACH,IAAI,SAAS,GAAA;QACX,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI;QACvB,MAAM,EAAE,cAAc,EAAE,GAAG,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC;QAE3D,OAAO,MAAM,CAAC,WAAW,CACvB,eACG,MAAM,EAAC,SAAS,GAAI,CAAC,CAAC,iBAAiB,CAAC,SAAS,EAAE,aAAa,CAAC,EACjE,GAAG,EAAC,SAAS,IAAG;YACf,MAAM,mBAAmB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAChD,SAAS,GAAI,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC/C;YACD,MAAM,OAAO,GAAG;gBACd,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,MAAM;gBACN,IAAI,EAAE,WAAW,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;aAC/C;YACD,MAAM,WAAW,GAAG,iBAAiB,CACnC,SAAS,EACT,aAAa,EACb,OAAO,CACR;YAED,IAAI,CAAC,WAAW,EAAE;gBAChB,OAAO,EAAE;;YAGX,MAAM,QAAQ,GAAwB,CACpC,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,WAAW,EACX,gBAAgB,KACd;gBACF,MAAM,cAAc,GAAG,qBAAqB,CAAC,IAAI,EAAE,mBAAmB,CAAC;gBAEvE,OAAO,WAAW,EAAE,CAAC;;oBAEnB,IAAI;oBACJ,IAAI;oBACJ,MAAM,EAAE,MAAsB;oBAC9B,WAAW;oBACX,gBAAgB;;oBAEhB,MAAM;oBACN,SAAS;oBACT,cAAc;gBACf,CAAA,CAAC;YACJ,CAAC;YAED,OAAO;gBAAC,SAAS,CAAC,IAAI;gBAAE,QAAQ;aAAC;SAClC,CAAC,CACL;;IAGH;;;KAGG,GACK,eAAe,GAAA;QACrB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAC,SAAS,IAAG;;;YAElC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,OAAO;YAEhE,MAAM,OAAO,GAAG;gBACd,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,IAAI,EAAE,mBAAmB,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;aACvD;YAED,IAAI,SAAS,CAAC,IAAI,KAAK,MAAM,EAAE;gBAC7B,MAAM,WAAW,GAAG,CAAA,EAAA,GAAA,YAAY,CAAC,iBAAiB,CAAC,SAAS,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,IAAI;gBAE9F,IAAI,WAAW,EAAE;oBACf,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;;;YAI7C,MAAM,cAAc,GAAG,iBAAiB,CACtC,SAAS,EACT,gBAAgB,EAChB,OAAO,CACR;YACD,MAAM,QAAQ,GAAG,iBAAiB,CAAwB,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC;YACzF,MAAM,QAAQ,GAAG,iBAAiB,CAAwB,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC;YACzF,MAAM,iBAAiB,GAAG,iBAAiB,CACzC,SAAS,EACT,mBAAmB,EACnB,OAAO,CACR;YACD,MAAM,aAAa,GAAG,iBAAiB,CACrC,SAAS,EACT,eAAe,EACf,OAAO,CACR;YACD,MAAM,OAAO,GAAG,iBAAiB,CAAuB,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;YACtF,MAAM,MAAM,GAAG,iBAAiB,CAAsB,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC;YACnF,MAAM,SAAS,GAAG,iBAAiB,CAAyB,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC;YAE5F,IAAI,cAAc,EAAE;gBAClB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,cAAc,CAAC;;YAGhD,IAAI,QAAQ,EAAE;gBACZ,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;;YAGpC,IAAI,QAAQ,EAAE;gBACZ,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;;YAGpC,IAAI,iBAAiB,EAAE;gBACrB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;;YAGtD,IAAI,aAAa,EAAE;gBACjB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;;YAG9C,IAAI,OAAO,EAAE;gBACX,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;;YAGlC,IAAI,MAAM,EAAE;gBACV,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;;YAGhC,IAAI,SAAS,EAAE;gBACb,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;;QAExC,CAAC,CAAC;;AAEL;ACAD;;;CAGG,SACU,SAAS,CAAA;IAkBpB,WAAA,CAAY,SAAqD,CAAA,CAAE,CAAA;QAjBnE,IAAI,CAAA,IAAA,GAAG,WAAW;QAElB,IAAI,CAAA,IAAA,GAAG,WAAW;QAElB,IAAM,CAAA,MAAA,GAAqB,IAAI;QAE/B,IAAK,CAAA,KAAA,GAAqB,IAAI;QAM9B,IAAA,CAAA,MAAM,GAAoB;YACxB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,cAAc,EAAE,CAAA,CAAE;SACnB;QAGC,IAAI,CAAC,MAAM,GAAG;YACZ,GAAG,IAAI,CAAC,MAAM;YACd,GAAG,MAAM;SACV;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;QAE5B,IAAI,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1E,OAAO,CAAC,IAAI,CACV,CAAA,sHAAA,EAAyH,IAAI,CAAC,IAAI,CAAI,EAAA,CAAA,CACvI;;;QAIH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc;QAEzC,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YAC1B,IAAI,CAAC,OAAO,GAAG,YAAY,CACzB,iBAAiB,CAA0B,IAAI,EAAE,YAAY,EAAE;gBAC7D,IAAI,EAAE,IAAI,CAAC,IAAI;YAChB,CAAA,CAAC,CACH;;QAGH,IAAI,CAAC,OAAO,GAAG,YAAY,CACzB,iBAAiB,CAA0B,IAAI,EAAE,YAAY,EAAE;YAC7D,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC,CACH,IAAI,CAAA,CAAE;;IAGT,OAAO,MAAM,CAAmB,MAAA,GAAyC,CAAA,CAAE,EAAA;QACzE,OAAO,IAAI,SAAS,CAAO,MAAM,CAAC;;IAGpC,SAAS,CAAC,UAA4B,CAAA,CAAE,EAAA;;;QAGtC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAmB;YAC9C,GAAG,IAAI,CAAC,MAAM;YACd,UAAU,EAAE,MAAK;gBACf,OAAO,SAAS,CAAC,IAAI,CAAC,OAA8B,EAAE,OAAO,CAAY;aAC1E;QACF,CAAA,CAAC;;QAGF,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;;QAE1B,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;QAE9B,OAAO,SAAS;;IAGlB,MAAM,CACJ,iBAA6E,CAAA,CAAE,EAAA;QAE/E,MAAM,SAAS,GAAG,IAAI,SAAS,CAAmC;YAAE,GAAG,IAAI,CAAC,MAAM;YAAE,GAAG,cAAc;QAAA,CAAE,CAAC;QAExG,SAAS,CAAC,MAAM,GAAG,IAAI;QAEvB,IAAI,CAAC,KAAK,GAAG,SAAS;QAEtB,SAAS,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI;QAElF,IAAI,cAAc,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1F,OAAO,CAAC,IAAI,CACV,CAAA,sHAAA,EAAyH,SAAS,CAAC,IAAI,CAAI,EAAA,CAAA,CAC5I;;QAGH,SAAS,CAAC,OAAO,GAAG,YAAY,CAC9B,iBAAiB,CAA0B,SAAS,EAAE,YAAY,EAAE;YAClE,IAAI,EAAE,SAAS,CAAC,IAAI;QACrB,CAAA,CAAC,CACH;QAED,SAAS,CAAC,OAAO,GAAG,YAAY,CAC9B,iBAAiB,CAA0B,SAAS,EAAE,YAAY,EAAE;YAClE,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,OAAO,EAAE,SAAS,CAAC,OAAO;QAC3B,CAAA,CAAC,CACH;QAED,OAAO,SAAS;;AAEnB;ACvfD;;;;;;;CAOG,YACa,cAAc,CAC5B,SAA0B,EAC1B,KAAY,EACZ,OAGC,EAAA;IAED,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,KAAK;IAC1B,MAAM,EAAE,cAAc,GAAG,MAAM,EAAE,eAAe,GAAG,CAAA,CAAE,EAAE,GAAG,OAAO,IAAI,CAAA,CAAE;IACvE,IAAI,IAAI,GAAG,EAAE;IAEb,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,KAAI;;QAC5D,IAAI,IAAI,CAAC,OAAO,IAAI,GAAG,GAAG,IAAI,EAAE;YAC9B,IAAI,IAAI,cAAc;;QAGxB,MAAM,cAAc,GAAG,eAAe,KAAA,IAAA,IAAf,eAAe,KAAf,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,eAAe,CAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAExD,IAAI,cAAc,EAAE;YAClB,IAAI,MAAM,EAAE;gBACV,IAAI,IAAI,cAAc,CAAC;oBACrB,IAAI;oBACJ,GAAG;oBACH,MAAM;oBACN,KAAK;oBACL,KAAK;gBACN,CAAA,CAAC;;;YAGJ,OAAO,KAAK;;QAGd,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,IAAI,CAAA,EAAA,GAAA,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAJ,IAAI,CAAE,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC,CAAA,CAAA,sBAAA;;IAElE,CAAC,CAAC;IAEF,OAAO,IAAI;AACb;AC/CA;;;;CAIG,GACG,SAAU,4BAA4B,CAAC,MAAc,EAAA;IACzD,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EACxB,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EACrC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,GAAK;YAAC,IAAI;YAAE,IAAI,CAAC,IAAI,CAAC,MAAM;SAAC,CAAC,CACnD;AACH;ACLO,MAAM,uBAAuB,GAAG,SAAS,CAAC,MAAM,CAAiC;IACtF,IAAI,EAAE,yBAAyB;IAE/B,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,SAAS;SAC1B;KACF;IAED,qBAAqB,GAAA;QACnB,OAAO;YACL,IAAI,mOAAM,CAAC;gBACT,GAAG,EAAE,8NAAI,YAAS,CAAC,yBAAyB,CAAC;gBAC7C,KAAK,EAAE;oBACL,uBAAuB,EAAE,MAAK;wBAC5B,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI;wBACvB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM;wBAChC,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,KAAK;wBAChC,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS;wBAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;wBAC9D,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,EAAC,KAAK,GAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;wBAC1D,MAAM,eAAe,GAAG,4BAA4B,CAAC,MAAM,CAAC;wBAC5D,MAAM,KAAK,GAAG;4BAAE,IAAI;4BAAE,EAAE;wBAAA,CAAE;wBAE1B,OAAO,cAAc,CAAC,GAAG,EAAE,KAAK,EAAE;4BAChC,GAAI,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,YAChC;gCAAE,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;4BAAA,IAC7C,CAAA,CAAE,CAAC;4BACP,eAAe;wBAChB,CAAA,CAAC;qBACH;gBACF,CAAA;aACF,CAAC;SACH;KACF;AACF,CAAA,CAAC;AC/BK,MAAM,IAAI,GAAwB,IAAM,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,KAAI;QAClE,qBAAqB,CAAC,MAAK;;YACzB,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;gBACtB,IAAI,CAAC,GAAmB,CAAC,IAAI,EAAE;;;gBAIhC,CAAA,EAAA,GAAA,MAAM,KAAN,IAAA,IAAA,MAAM,KAAN,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,MAAM,CAAE,YAAY,EAAE,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,EAAE;;QAE7C,CAAC,CAAC;QAEF,OAAO,IAAI;IACb,CAAC;ACXM,MAAM,YAAY,GAAgC,CAAC,UAAU,GAAG,KAAK,GAAK,CAAC,EAAE,QAAQ,EAAE,KAAI;QAChG,OAAO,QAAQ,CAAC,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC;IAC5C,CAAC;ACDM,MAAM,UAAU,GAA8B,IAAM,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;QACrF,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;QACxB,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS;QAE5B,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,IAAI;;QAGb,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,KAAI;YAChC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,KAAI;gBACvD,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBACpB;;gBAGF,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE;gBAC3B,MAAM,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACjD,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC/D,MAAM,SAAS,GAAG,WAAW,CAAC,UAAU,CAAC,SAAS,CAAC;gBAEnD,IAAI,CAAC,SAAS,EAAE;oBACd;;gBAGF,MAAM,eAAe,yOAAG,cAAA,AAAU,EAAC,SAAS,CAAC;gBAE7C,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;oBACzB,MAAM,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;oBAE9E,EAAE,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,EAAE,WAAW,CAAC;;gBAGhD,IAAI,eAAe,IAAI,eAAe,KAAK,CAAC,EAAE;oBAC5C,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC;;YAEvC,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF,OAAO,IAAI;IACb,CAAC;ACnCM,MAAM,OAAO,IAA2B,EAAE,IAAI,KAAK,IAAG;QAC3D,OAAO,EAAE,CAAC,KAAK,CAAC;IAClB,CAAC;ACLM,MAAM,mBAAmB,GAAuC,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QACnG,0OAAOC,uBAAAA,AAA2B,EAAC,KAAK,EAAE,QAAQ,CAAC;IACrD,CAAC;ACEM,MAAM,GAAG,GAAuB,CAAC,WAAW,EAAE,SAAS,GAAK,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,KAAI;QACpF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM;QAExB,MAAM,YAAY,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE,CAAC;QAEtE,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE,CAAC;QAChD,MAAM,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC;QAExC,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,OAAO,CAAC;QAEvC,EAAE,CAAC,YAAY,CAAC,IAAI,0OAAa,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QAE9D,OAAO,IAAI;IACb,CAAC;ACnBM,MAAM,iBAAiB,GAAqC,IAAM,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;QAC5F,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;QACxB,MAAM,WAAW,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE;;QAG5C,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,EAAE;YAChC,OAAO,KAAK;;QAGd,MAAM,IAAI,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO;QAEjC,IAAK,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,CAAE;YAClD,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;YAE7B,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,EAAE;gBAClC,IAAI,QAAQ,EAAE;oBACZ,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;oBAC/B,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;oBAE5B,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,cAAc,EAAE;;gBAGtC,OAAO,IAAI;;;QAIf,OAAO,KAAK;IACd,CAAC;ACvBM,MAAM,UAAU,IAA8B,UAAU,GAAI,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QAC7F,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;QAClD,MAAM,IAAI,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO;QAEjC,IAAK,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,CAAE;YAClD,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;YAE7B,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;gBACtB,IAAI,QAAQ,EAAE;oBACZ,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;oBAC/B,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;oBAE5B,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,cAAc,EAAE;;gBAGtC,OAAO,IAAI;;;QAIf,OAAO,KAAK;IACd,CAAC;ACvBM,MAAM,WAAW,IAA+B,KAAK,GAAI,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;QACnF,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,KAAK;QAE1B,IAAI,QAAQ,EAAE;YACZ,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;;QAGrB,OAAO,IAAI;IACb,CAAC;ACPM,MAAM,eAAe,GAAmC,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QAC3F,2OAAOC,kBAAAA,AAAuB,EAAC,KAAK,EAAE,QAAQ,CAAC;IACjD,CAAC;ACJM,MAAM,KAAK,GAAyB,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;QAChE,OAAO,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC;IAC3C,CAAC;ACAM,MAAM,QAAQ,GAA4B,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QAC7E,2OAAOC,WAAAA,AAAgB,EAAC,KAAK,EAAE,QAAQ,CAAC;IAC1C,CAAC;AChBD;;;;CAIG,GACa,SAAA,cAAc,CAC5B,OAA4B,EAC5B,OAA4B,EAC5B,OAAA,GAA+B;IAAE,MAAM,EAAE,IAAI;AAAA,CAAE,EAAA;IAE/C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;IAEjC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAChB,OAAO,IAAI;;IAGb,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,IAAG;QACtB,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC;;QAGtC,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;YAC1B,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;;QAGxC,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC;IACtC,CAAC,CAAC;AACJ;ACxBA,SAAS,aAAa,CACpB,KAAwB,EACxB,IAAc,EACd,aAAkC,CAAA,CAAE,EAAA;IAEpC,OAAO,KAAK,CAAC,IAAI,EAAC,IAAI,IAAG;QACvB,OACE,IAAI,CAAC,IAAI,KAAK,QACX,cAAc;QAEf,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,EAAC,CAAC,GAAI;gBAAC,CAAC;gBAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aAAC,CAAC,CAAC,EACxE,UAAU,CACX;IAEL,CAAC,CAAC;AACJ;AAEA,SAAS,WAAW,CAClB,KAAwB,EACxB,IAAc,EACd,aAAkC,CAAA,CAAE,EAAA;IAEpC,OAAO,CAAC,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;AACjD;AAEA;;CAEG,YACa,YAAY,CAC1B;;CAEG,GACH,IAAiB,EACjB;;CAEG,GACH,IAAc,EACd;;;CAGG,GACH,UAAgC,EAAA;;IAEhC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;QAClB;;IAEF,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC;;IAGrD,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAC,IAAI,GAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE;QACrE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;;;IAIpD,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE;QACrE;;;IAIF,UAAU,GAAG,UAAU,IAAA,CAAI,CAAA,EAAA,GAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,CAAA;;;IAIrD,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC;WAAG,KAAK,CAAC,IAAI,CAAC,KAAK;KAAC,EAAE,IAAI,EAAE,UAAU,CAAC;IAEnE,IAAI,CAAC,IAAI,EAAE;QACT;;IAGF,IAAI,UAAU,GAAG,KAAK,CAAC,KAAK;IAC5B,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,MAAM;IAC1C,IAAI,QAAQ,GAAG,UAAU,GAAG,CAAC;IAC7B,IAAI,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ;IAE3C,MACE,UAAU,GAAG,KACV,WAAW,CAAC,CAAC;WAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,KAAK;KAAC,EAAE,IAAI,EAAE,UAAU,CAAC,CAC9E;QACA,UAAU,IAAI,CAAC;QACf,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,QAAQ;;IAGpD,MACE,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,UAAA,IACpB,WAAW,CAAC,CAAC;WAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,KAAK;KAAC,EAAE,IAAI,EAAE,UAAU,CAAC,CACxE;QACA,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,QAAQ;QAC9C,QAAQ,IAAI,CAAC;;IAGf,OAAO;QACL,IAAI,EAAE,QAAQ;QACd,EAAE,EAAE,MAAM;KACX;AACH;ACjGgB,SAAA,WAAW,CAAC,UAA6B,EAAE,MAAc,EAAA;IACvE,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;QAClC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;YAC7B,MAAM,KAAK,CACT,CAAA,6BAAA,EAAgC,UAAU,CAAA,yCAAA,CAA2C,CACtF;;QAGH,OAAO,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC;;IAGjC,OAAO,UAAU;AACnB;ACkBO,MAAM,eAAe,GAAmC,CAAC,UAAU,EAAE,UAAU,GAAG,CAAA,CAAE,GAAK,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QAC1H,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;QAClD,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE;QAC7B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,SAAS;QAErC,IAAI,QAAQ,EAAE;YACZ,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;YAEnD,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,EAAE,IAAI,EAAE,EAAE;gBACjD,MAAM,YAAY,GAAG,0OAAa,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;gBAEpE,EAAE,CAAC,YAAY,CAAC,YAAY,CAAC;;;QAIjC,OAAO,IAAI;IACb,CAAC;ACjCM,MAAM,KAAK,IAAyB,QAAQ,IAAI,KAAK,IAAG;QAC7D,MAAM,KAAK,GAAG,OAAO,QAAQ,KAAK,aAC9B,QAAQ,CAAC,KAAK,IACd,QAAQ;QAEZ,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAE;YACxC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBACnB,OAAO,IAAI;;;QAIf,OAAO,KAAK;IACd,CAAC;ACzBK,SAAU,eAAe,CAAC,KAAc,EAAA;IAC5C,OAAO,KAAK,qOAAY,iBAAa;AACvC;ACJgB,SAAA,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAA;IAChD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC;AAC5C;SCIgB,oBAAoB,CAClC,GAAoB,EACpB,WAA0B,IAAI,EAAA;IAE9B,IAAI,CAAC,QAAQ,EAAE;QACb,OAAO,IAAI;;IAGb,MAAM,gBAAgB,6NAAG,YAAS,CAAC,OAAO,CAAC,GAAG,CAAC;IAC/C,MAAM,cAAc,6NAAG,YAAS,CAAC,KAAK,CAAC,GAAG,CAAC;IAE3C,IAAI,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,IAAI,EAAE;QAC7C,OAAO,gBAAgB;;IAGzB,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,OAAO,cAAc;;IAGvB,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI;IACpC,MAAM,MAAM,GAAG,cAAc,CAAC,EAAE;IAEhC,IAAI,QAAQ,KAAK,KAAK,EAAE;QACtB,iOAAO,gBAAa,CAAC,MAAM,CACzB,GAAG,EACH,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EACzB,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CACzC;;IAGH,iOAAO,gBAAa,CAAC,MAAM,CACzB,GAAG,EACH,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,EAChC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CACjC;AACH;SCzCgB,SAAS,GAAA;IACvB,OAAO,SAAS,CAAC,QAAQ,KAAK,SAAS,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;AACjF;SCFgB,KAAK,GAAA;IACnB,OAAO;QACL,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;QAChB,MAAM;QACN,QAAQ;QACR,MAAM;KACP,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,KAEzB,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,YAAY,IAAI,QAAQ,CAAC;AACtE;ACuBO,MAAM,KAAK,GAAyB,CAAC,QAAQ,GAAG,IAAI,EAAE,OAAO,GAAG,CAAA,CAAE,GAAK,CAAC,EAC7E,MAAM,EACN,IAAI,EACJ,EAAE,EACF,QAAQ,EACT,KAAI;QACH,OAAO,GAAG;YACR,cAAc,EAAE,IAAI;YACpB,GAAG,OAAO;SACX;QAED,MAAM,YAAY,GAAG,MAAK;;;YAGxB,IAAI,KAAK,EAAE,IAAI,SAAS,EAAE,EAAE;gBACzB,IAAI,CAAC,GAAmB,CAAC,KAAK,EAAE;;;;YAKnC,qBAAqB,CAAC,MAAK;gBACzB,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;oBACvB,IAAI,CAAC,KAAK,EAAE;oBAEZ,IAAI,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,cAAc,EAAE;wBAC3B,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE;;;YAGtC,CAAC,CAAC;QACJ,CAAC;QAED,IAAI,AAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,QAAQ,KAAK,IAAI,IAAK,QAAQ,KAAK,KAAK,EAAE;YAChE,OAAO,IAAI;;;QAIb,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;YAC7E,YAAY,EAAE;YACd,OAAO,IAAI;;;;QAKb,MAAM,SAAS,GAAG,oBAAoB,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS;QAClF,MAAM,eAAe,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;QAE5D,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,eAAe,EAAE;gBACpB,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC;;;;YAK5B,IAAI,eAAe,IAAI,EAAE,CAAC,WAAW,EAAE;gBACrC,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,WAAW,CAAC;;YAGnC,YAAY,EAAE;;QAGhB,OAAO,IAAI;IACb,CAAC;AC1EM,MAAM,OAAO,GAA2B,CAAC,KAAK,EAAE,EAAE,IAAK,KAAK,IAAG;QACpE,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,GAAK,EAAE,CAAC,IAAI,EAAE;gBAAE,GAAG,KAAK;gBAAE,KAAK;YAAA,CAAE,CAAC,CAAC;IACpE,CAAC;ACgBM,MAAM,aAAa,GAAiC,CAAC,KAAK,EAAE,OAAO,GAAK,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;QAClG,OAAO,QAAQ,CAAC,eAAe,CAC7B;YAAE,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI;YAAE,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE;QAAA,CAAE,EAChD,KAAK,EACL,OAAO,CACR;IACH,CAAC;AC7CD,MAAM,iBAAiB,GAAG,CAAC,IAAiB,KAAI;IAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU;IAEhC,IAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAE;QAChD,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC;QAEzB,IAAI,KAAK,CAAC,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,SAAS,IAAI,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;YACpF,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;eAClB,IAAI,KAAK,CAAC,QAAQ,KAAK,CAAC,EAAE;YAC/B,iBAAiB,CAAC,KAAoB,CAAC;;;IAI3C,OAAO,IAAI;AACb,CAAC;AAEK,SAAU,iBAAiB,CAAC,KAAa,EAAA;;IAE7C,MAAM,YAAY,GAAG,CAAS,MAAA,EAAA,KAAK,CAAA,OAAA,CAAS;IAE5C,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC,IAAI;IAEnF,OAAO,iBAAiB,CAAC,IAAI,CAAC;AAChC;ACNA;;;;;;CAMG,YACa,qBAAqB,CACnC,OAA6C,EAC7C,MAAc,EACd,OAAsC,EAAA;IAEtC,IAAI,OAAO,uOAAYC,OAAe,IAAI,OAAO,uOAAY,WAAQ,EAAE;QACrE,OAAO,OAAO;;IAEhB,OAAO,GAAG;QACR,KAAK,EAAE,IAAI;QACX,YAAY,EAAE,CAAA,CAAE;QAChB,GAAG,OAAO;KACX;IAED,MAAM,aAAa,GAAG,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,IAAI;IACrE,MAAM,aAAa,GAAG,OAAO,OAAO,KAAK,QAAQ;IAEjD,IAAI,aAAa,EAAE;QACjB,IAAI;YACF,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC;;YAGnE,IAAI,cAAc,EAAE;gBAClB,kOAAO,WAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,EAAC,IAAI,GAAI,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;;YAG3E,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC;YAEzC,IAAI,OAAO,CAAC,qBAAqB,EAAE;gBACjC,IAAI,CAAC,KAAK,EAAE;;YAGd,OAAO,IAAI;UACX,OAAO,KAAK,EAAE;YACd,IAAI,OAAO,CAAC,qBAAqB,EAAE;gBACjC,MAAM,IAAI,KAAK,CAAC,sCAAsC,EAAE;oBAAE,KAAK,EAAE,KAAc;gBAAA,CAAE,CAAC;;YAGpF,OAAO,CAAC,IAAI,CAAC,iCAAiC,EAAE,eAAe,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;YAE1F,OAAO,qBAAqB,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC;;;IAIrD,IAAI,aAAa,EAAE;;QAGjB,IAAI,OAAO,CAAC,qBAAqB,EAAE;YACjC,IAAI,iBAAiB,GAAG,KAAK;YAC7B,IAAI,cAAc,GAAG,EAAE;;YAGvB,MAAM,kBAAkB,GAAG,+NAAI,SAAM,CAAC;gBACpC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO;gBAC5B,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK;;;gBAGxB,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;oBAC9B,4CAA4C,EAAE;wBAC5C,OAAO,EAAE,SAAS;wBAClB,KAAK,EAAE,OAAO;wBACd,QAAQ,EAAE;4BACR;gCACE,GAAG,EAAE,GAAG;gCACR,QAAQ,GAAE,CAAC,IAAG;;oCAEZ,iBAAiB,GAAG,IAAI;;oCAExB,cAAc,GAAG,OAAO,CAAC,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS;oCACxD,OAAO,IAAI;iCACZ;4BACF,CAAA;yBACF;oBACF,CAAA;iBACF,CAAC;YACH,CAAA,CAAC;YAEF,IAAI,OAAO,CAAC,KAAK,EAAE;2OACjB,YAAS,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,UAAU,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC;mBAChG;0OACL,aAAS,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC;;YAGlG,IAAI,OAAO,CAAC,qBAAqB,IAAI,iBAAiB,EAAE;gBACtD,MAAM,IAAI,KAAK,CAAC,sCAAsC,EAAE;oBAAE,KAAK,EAAE,IAAI,KAAK,CAAC,CAA0B,uBAAA,EAAA,cAAc,EAAE,CAAC;gBAAA,CAAE,CAAC;;;QAI7H,MAAM,MAAM,8NAAG,YAAS,CAAC,UAAU,CAAC,MAAM,CAAC;QAE3C,IAAI,OAAO,CAAC,KAAK,EAAE;YACjB,OAAO,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO;;QAGpF,OAAO,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC;;IAIvE,OAAO,qBAAqB,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC;AACnD;ACxHA,6FAAA;SACgB,uBAAuB,CAAC,EAAe,EAAE,QAAgB,EAAE,IAAY,EAAA;IACrF,MAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;IAEhC,IAAI,IAAI,GAAG,QAAQ,EAAE;QACnB;;IAGF,MAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;IAE3B,IAAI,CAAA,CAAE,IAAI,+OAAY,cAAW,IAAI,IAAI,+OAAY,oBAAiB,CAAC,EAAE;QACvE;;IAGF,MAAM,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;IACjC,IAAI,GAAG,GAAG,CAAC;IAEX,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,KAAI;QAC1C,IAAI,GAAG,KAAK,CAAC,EAAE;YACb,GAAG,GAAG,KAAK;;IAEf,CAAC,CAAC;IAEF,EAAE,CAAC,YAAY,2NAAC,YAAS,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AAC5D;AC+BA,MAAM,UAAU,GAAG,CAAC,cAA0C,KAAgC;IAC5F,OAAO,CAAA,CAAE,MAAM,IAAI,cAAc,CAAC;AACpC,CAAC;AAEM,MAAM,eAAe,GAAmC,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,GAAK,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAI;;QACxH,IAAI,QAAQ,EAAE;YACZ,OAAO,GAAG;gBACR,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,YAAY;gBACzC,eAAe,EAAE,IAAI;gBACrB,eAAe,EAAE,KAAK;gBACtB,eAAe,EAAE,KAAK;gBACtB,GAAG,OAAO;aACX;YAED,IAAI,OAAmC;YAEvC,IAAI;gBACF,OAAO,GAAG,qBAAqB,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE;oBACpD,YAAY,EAAE;wBACZ,kBAAkB,EAAE,MAAM;wBAC1B,GAAG,OAAO,CAAC,YAAY;oBACxB,CAAA;oBACD,qBAAqB,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,qBAAqB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,MAAM,CAAC,OAAO,CAAC,kBAAkB;gBAC1F,CAAA,CAAC;cACF,OAAO,CAAC,EAAE;gBACV,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE;oBAC1B,MAAM;oBACN,KAAK,EAAE,CAAU;oBACjB,oBAAoB,EAAE,MAAK;wBACzB,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE;4BAChC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,GAAG,IAAI;;qBAEjD;gBACF,CAAA,CAAC;gBACF,OAAO,KAAK;;YAGd,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,OAAO,QAAQ,KAAK,QAAQ,GAAG;gBAAE,IAAI,EAAE,QAAQ;gBAAE,EAAE,EAAE,QAAQ;YAAA,CAAE,GAAG;gBAAE,IAAI,EAAE,QAAQ,CAAC,IAAI;gBAAE,EAAE,EAAE,QAAQ,CAAC,EAAE;YAAA,CAAE;YAE7H,IAAI,iBAAiB,GAAG,IAAI;YAC5B,IAAI,kBAAkB,GAAG,IAAI;YAC7B,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG;gBAAC,OAAO;aAAC;YAEvD,KAAK,CAAC,OAAO,EAAC,IAAI,IAAG;;gBAEnB,IAAI,CAAC,KAAK,EAAE;gBAEZ,iBAAiB,GAAG,iBAAiB,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,KAAK;gBAEtF,kBAAkB,GAAG,kBAAkB,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK;YAChE,CAAC,CAAC;;;;;;YAOF,IAAI,IAAI,KAAK,EAAE,IAAI,kBAAkB,EAAE;gBACrC,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;gBACvC,MAAM,gBAAgB,GAAG,MAAM,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU;gBAE3F,IAAI,gBAAgB,EAAE;oBACpB,IAAI,IAAI,CAAC;oBACT,EAAE,IAAI,CAAC;;;YAIX,IAAI,UAAU;;;YAId,IAAI,iBAAiB,EAAE;;;gBAGrB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBACxB,UAAU,GAAG,KAAK,CAAC,GAAG,EAAC,CAAC,GAAI,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;uBAC7C,IAAI,KAAK,uOAAY,WAAQ,EAAE;oBACpC,IAAI,IAAI,GAAG,EAAE;oBAEb,KAAK,CAAC,OAAO,EAAC,IAAI,IAAG;wBACnB,IAAI,IAAI,CAAC,IAAI,EAAE;4BACb,IAAI,IAAI,IAAI,CAAC,IAAI;;oBAErB,CAAC,CAAC;oBAEF,UAAU,GAAG,IAAI;uBACZ,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE;oBAC/D,UAAU,GAAG,KAAK,CAAC,IAAI;uBAClB;oBACL,UAAU,GAAG,KAAe;;gBAG9B,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,CAAC;mBAC9B;gBACL,UAAU,GAAG,OAAO;gBAEpB,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,EAAE,UAAU,CAAC;;;YAItC,IAAI,OAAO,CAAC,eAAe,EAAE;gBAC3B,uBAAuB,CAAC,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;;YAGtD,IAAI,OAAO,CAAC,eAAe,EAAE;gBAC3B,EAAE,CAAC,OAAO,CAAC,iBAAiB,EAAE;oBAAE,IAAI;oBAAE,IAAI,EAAE,UAAU;gBAAA,CAAE,CAAC;;YAG3D,IAAI,OAAO,CAAC,eAAe,EAAE;gBAC3B,EAAE,CAAC,OAAO,CAAC,iBAAiB,EAAE;oBAAE,IAAI;oBAAE,IAAI,EAAE,UAAU;gBAAA,CAAE,CAAC;;;QAI7D,OAAO,IAAI;IACb,CAAC;AC9HM,MAAM,MAAM,GAA0B,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QACzE,2OAAOC,SAAAA,AAAc,EAAC,KAAK,EAAE,QAAQ,CAAC;IACxC,CAAC;AAEM,MAAM,QAAQ,GAA4B,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QAC7E,2OAAOC,WAAAA,AAAgB,EAAC,KAAK,EAAE,QAAQ,CAAC;IAC1C,CAAC;AAEM,MAAM,YAAY,GAAgC,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QACrF,2OAAOC,eAAAA,AAAoB,EAAC,KAAK,EAAE,QAAQ,CAAC;IAC9C,CAAC;AAEM,MAAM,WAAW,GAA+B,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QACnF,QAAOC,iPAAAA,AAAmB,EAAC,KAAK,EAAE,QAAQ,CAAC;IAC7C,CAAC;AC5CM,MAAM,gBAAgB,GAAoC,IAAM,CAAC,EACtE,KAAK,EACL,QAAQ,EACR,EAAE,EACH,KAAI;QACH,IAAI;YACF,MAAM,KAAK,0OAAG,YAAA,AAAS,EAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAEjE,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;gBACzC,OAAO,KAAK;;YAGd,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YAEjB,IAAI,QAAQ,EAAE;gBACZ,QAAQ,CAAC,EAAE,CAAC;;YAGd,OAAO,IAAI;UACX,OAAM;YACN,OAAO,KAAK;;IAEhB,CAAC;ACtBM,MAAM,eAAe,GAAmC,IAAM,CAAC,EACpE,KAAK,EACL,QAAQ,EACR,EAAE,EACH,KAAI;QACH,IAAI;YACF,MAAM,KAAK,0OAAG,YAAA,AAAS,EAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAEjE,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;gBACzC,OAAO,KAAK;;YAGd,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YAEjB,IAAI,QAAQ,EAAE;gBACZ,QAAQ,CAAC,EAAE,CAAC;;YAGd,OAAO,IAAI;UACX,OAAM;YACN,OAAO,KAAK;;IAEhB,CAAC;ACvBM,MAAM,qBAAqB,GAAyC,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QACvG,2OAAOC,wBAAAA,AAAe,EAAC,KAAK,EAAE,QAAQ,CAAC;IACzC,CAAC;ACFM,MAAM,oBAAoB,GAAwC,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QACrG,2OAAOA,uBAAAA,AAAe,EAAC,KAAK,EAAE,QAAQ,CAAC;IACzC,CAAC;SCjBe,OAAO,GAAA;IACrB,OAAO,OAAO,SAAS,KAAK,cACxB,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,IAC7B,KAAK;AACX;ACAA,SAAS,gBAAgB,CAAC,IAAY,EAAA;IACpC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAClC,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAEpC,IAAI,MAAM,KAAK,OAAO,EAAE;QACtB,MAAM,GAAG,GAAG;;IAGd,IAAI,GAAG;IACP,IAAI,IAAI;IACR,IAAI,KAAK;IACT,IAAI,IAAI;IAER,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAE;QAC5C,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC;QAEpB,IAAI,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAC/B,IAAI,GAAG,IAAI;eACN,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAChC,GAAG,GAAG,IAAI;eACL,IAAI,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAC1C,IAAI,GAAG,IAAI;eACN,IAAI,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAClC,KAAK,GAAG,IAAI;eACP,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAC7B,IAAI,KAAK,EAAE,IAAI,OAAO,EAAE,EAAE;gBACxB,IAAI,GAAG,IAAI;mBACN;gBACL,IAAI,GAAG,IAAI;;eAER;YACL,MAAM,IAAI,KAAK,CAAC,CAAA,4BAAA,EAA+B,GAAG,CAAA,CAAE,CAAC;;;IAIzD,IAAI,GAAG,EAAE;QACP,MAAM,GAAG,CAAA,IAAA,EAAO,MAAM,CAAA,CAAE;;IAG1B,IAAI,IAAI,EAAE;QACR,MAAM,GAAG,CAAA,KAAA,EAAQ,MAAM,CAAA,CAAE;;IAG3B,IAAI,IAAI,EAAE;QACR,MAAM,GAAG,CAAA,KAAA,EAAQ,MAAM,CAAA,CAAE;;IAG3B,IAAI,KAAK,EAAE;QACT,MAAM,GAAG,CAAA,MAAA,EAAS,MAAM,CAAA,CAAE;;IAG5B,OAAO,MAAM;AACf;AAeO,MAAM,gBAAgB,IAAoC,IAAI,GAAI,CAAC,EACxE,MAAM,EACN,IAAI,EACJ,EAAE,EACF,QAAQ,EACT,KAAI;QACH,MAAM,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC;QACnD,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAC,IAAI,GAAI,CAAC;gBAAC,KAAK;gBAAE,MAAM;gBAAE,MAAM;gBAAE,OAAO;aAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC/E,MAAM,KAAK,GAAG,IAAI,aAAa,CAAC,SAAS,EAAE;YACzC,GAAG,EAAE,GAAG,KAAK,UACT,MACA,GAAG;YACP,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC5B,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC9B,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC9B,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAChC,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,IAAI;QACjB,CAAA,CAAC;QAEF,MAAM,mBAAmB,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAK;YACzD,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAE,CAAC,GAAI,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC,CAAC;QAEF,mBAAmB,KAAA,IAAA,IAAnB,mBAAmB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAnB,mBAAmB,CAAE,KAAK,CAAC,OAAO,EAAC,IAAI,IAAG;YACxC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC;YAEpC,IAAI,OAAO,IAAI,QAAQ,EAAE;gBACvB,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC;;QAEzB,CAAC,CAAC;QAEF,OAAO,IAAI;IACb,CAAC;ACjGK,SAAU,YAAY,CAC1B,KAAkB,EAClB,UAAoC,EACpC,aAAkC,CAAA,CAAE,EAAA;IAEpC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC,SAAS;IAC3C,MAAM,IAAI,GAAG,UAAU,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI;IAEtE,MAAM,UAAU,GAAgB,EAAE;IAElC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,KAAI;QAC7C,IAAI,IAAI,CAAC,MAAM,EAAE;YACf;;QAGF,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;QACxC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEpD,UAAU,CAAC,IAAI,CAAC;YACd,IAAI;YACJ,IAAI,EAAE,YAAY;YAClB,EAAE,EAAE,UAAU;QACf,CAAA,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,EAAE,GAAG,IAAI;IAChC,MAAM,iBAAiB,GAAG,WACvB,MAAM,EAAC,SAAS,IAAG;QAClB,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,IAAI;;QAGb,OAAO,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;IAC/C,CAAC,EACA,MAAM,EAAC,SAAS,GAAI,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE;YAAE,MAAM,EAAE,KAAK;QAAA,CAAE,CAAC,CAAC;IAE3F,IAAI,KAAK,EAAE;QACT,OAAO,CAAC,CAAC,iBAAiB,CAAC,MAAM;;IAGnC,MAAM,KAAK,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,SAAS,GAAK,GAAG,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;IAElG,OAAO,KAAK,IAAI,cAAc;AAChC;AC5BO,MAAM,IAAI,GAAwB,CAAC,UAAU,EAAE,UAAU,GAAG,CAAA,CAAE,GAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QAChG,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;QAClD,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;QAEtD,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,KAAK;;QAGd,2OAAOC,OAAAA,AAAY,EAAC,KAAK,EAAE,QAAQ,CAAC;IACtC,CAAC;ACfM,MAAM,cAAc,GAAkC,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QACzF,2OAAOC,iBAAAA,AAAsB,EAAC,KAAK,EAAE,QAAQ,CAAC;IAChD,CAAC;ACCM,MAAM,YAAY,IAAgC,UAAU,GAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QAC7F,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;QAElD,OAAOC,+PAAAA,AAAoB,EAAC,IAAI,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC;IACpD,CAAC;ACPM,MAAM,aAAa,GAAiC,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QACvF,2OAAOC,gBAAAA,AAAqB,EAAC,KAAK,EAAE,QAAQ,CAAC;IAC/C,CAAC;AChBD;;;;;CAKG,GACa,SAAA,uBAAuB,CAAC,IAAY,EAAE,MAAc,EAAA;IAClE,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACtB,OAAO,MAAM;;IAGf,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACtB,OAAO,MAAM;;IAGf,OAAO,IAAI;AACb;AClBA;;;;CAIG,GACa,SAAA,WAAW,CAAC,GAAwB,EAAE,WAA8B,EAAA;IAClF,MAAM,KAAK,GAAG,OAAO,WAAW,KAAK,WACjC;QAAC,WAAW;KAAA,GACZ,WAAW;IAEf,OAAO,OACJ,IAAI,CAAC,GAAG,EACR,MAAM,CAAC,CAAC,MAA2B,EAAE,IAAI,KAAI;QAC5C,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC;;QAG1B,OAAO,MAAM;KACd,EAAE,CAAA,CAAE,CAAC;AACV;ACMO,MAAM,eAAe,GAAmC,CAAC,UAAU,EAAE,UAAU,GAAK,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QACrH,IAAI,QAAQ,GAAoB,IAAI;QACpC,IAAI,QAAQ,GAAoB,IAAI;QAEpC,MAAM,UAAU,GAAG,uBAAuB,CACxC,OAAO,UAAU,KAAK,QAAQ,GAAG,UAAU,GAAG,UAAU,CAAC,IAAI,EAC7D,KAAK,CAAC,MAAM,CACb;QAED,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,KAAK;;QAGd,IAAI,UAAU,KAAK,MAAM,EAAE;YACzB,QAAQ,GAAG,WAAW,CAAC,UAAsB,EAAE,KAAK,CAAC,MAAM,CAAC;;QAG9D,IAAI,UAAU,KAAK,MAAM,EAAE;YACzB,QAAQ,GAAG,WAAW,CAAC,UAAsB,EAAE,KAAK,CAAC,MAAM,CAAC;;QAG9D,IAAI,QAAQ,EAAE;YACZ,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,IAAG;gBAClC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,KAAI;oBACnE,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,EAAE;wBACtC,EAAE,CAAC,aAAa,CAAC,GAAG,EAAE,SAAS,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;;oBAGvE,IAAI,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;wBACjC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAC,IAAI,IAAG;4BACxB,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,EAAE;gCAC1B,EAAE,CAAC,OAAO,CACR,GAAG,EACH,GAAG,GAAG,IAAI,CAAC,QAAQ,EACnB,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CACrD;;wBAEL,CAAC,CAAC;;gBAEN,CAAC,CAAC;YACJ,CAAC,CAAC;;QAGJ,OAAO,IAAI;IACb,CAAC;ACvDM,MAAM,cAAc,GAAkC,IAAM,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;QACtF,IAAI,QAAQ,EAAE;YACZ,EAAE,CAAC,cAAc,EAAE;;QAGrB,OAAO,IAAI;IACb,CAAC;ACJM,MAAM,SAAS,GAA6B,IAAM,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;QAC5E,IAAI,QAAQ,EAAE;YACZ,MAAM,SAAS,GAAG,8NAAI,eAAY,CAAC,EAAE,CAAC,GAAG,CAAC;YAE1C,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC;;QAG5B,OAAO,IAAI;IACb,CAAC;ACRM,MAAM,kBAAkB,GAAsC,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QACjG,2OAAOC,qBAAAA,AAA0B,EAAC,KAAK,EAAE,QAAQ,CAAC;IACpD,CAAC;ACFM,MAAM,iBAAiB,GAAqC,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QAC/F,2OAAOC,oBAAAA,AAAyB,EAAC,KAAK,EAAE,QAAQ,CAAC;IACnD,CAAC;ACFM,MAAM,gBAAgB,GAAoC,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QAC7F,2OAAOC,mBAAAA,AAAwB,EAAC,KAAK,EAAE,QAAQ,CAAC;IAClD,CAAC;AClBD,aAAA;AACA,iDAAA;AAiBO,MAAM,kBAAkB,GAAsC,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QACjG,2OAAOC,qBAAAA,AAA0B,EAAC,KAAK,EAAE,QAAQ,CAAC;IACpD,CAAC;ACpBD,aAAA;AACA,iDAAA;AAiBO,MAAM,oBAAoB,GAAwC,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QACrG,WAAOC,uPAAAA,AAA4B,EAAC,KAAK,EAAE,QAAQ,CAAC;IACtD,CAAC;ACbD;;;;;;CAMG,GACG,SAAU,cAAc,CAC5B,OAA6C,EAC7C,MAAc,EACd,YAA6B,GAAA,CAAA,CAAE,EAC/B,OAAA,GAA+C,CAAA,CAAE,EAAA;IAEjD,OAAO,qBAAqB,CAAC,OAAO,EAAE,MAAM,EAAE;QAC5C,KAAK,EAAE,KAAK;QACZ,YAAY;QACZ,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;IACrD,CAAA,CAAoB;AACvB;ACqBO,MAAM,UAAU,GAA8B,CAAC,OAAO,EAAE,UAAU,GAAG,KAAK,EAAE,YAAY,GAAG,CAAA,CAAE,EAAE,OAAO,GAAG,CAAA,CAAE,GAAK,CAAC,EACtH,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAC/B,KAAI;;QACH,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;;;QAIlB,IAAI,YAAY,CAAC,kBAAkB,KAAK,MAAM,EAAE;YAC9C,MAAM,QAAQ,IAAG,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,YAAY,EAAE;gBACpE,qBAAqB,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,qBAAqB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,MAAM,CAAC,OAAO,CAAC,kBAAkB;YAC1F,CAAA,CAAC;YAEF,IAAI,QAAQ,EAAE;gBACZ,EAAE,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAC,OAAO,CAAC,eAAe,EAAE,CAAC,UAAU,CAAC;;YAErF,OAAO,IAAI;;QAGb,IAAI,QAAQ,EAAE;YACZ,EAAE,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,UAAU,CAAC;;QAG1C,OAAO,QAAQ,CAAC,eAAe,CAAC;YAAE,IAAI,EAAE,CAAC;YAAE,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI;QAAA,CAAE,EAAE,OAAO,EAAE;YAC1E,YAAY;YACZ,qBAAqB,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,qBAAqB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,MAAM,CAAC,OAAO,CAAC,kBAAkB;QAC1F,CAAA,CAAC;IACJ,CAAC;ACnEe,SAAA,iBAAiB,CAC/B,KAAkB,EAClB,UAA6B,EAAA;IAE7B,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;IAClD,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC,SAAS;IAC3C,MAAM,KAAK,GAAW,EAAE;IAExB,IAAI,KAAK,EAAE;QACT,IAAI,KAAK,CAAC,WAAW,EAAE;YACrB,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC;;QAGlC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;WACvC;QACL,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,GAAE,IAAI,IAAG;YACtC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;QAC3B,CAAC,CAAC;;IAGJ,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,EAAC,QAAQ,GAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC;IAErE,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,CAAA,CAAE;;IAGX,OAAO;QAAE,GAAG,IAAI,CAAC,KAAK;IAAA,CAAE;AAC1B;AC5BA;;;;;CAKG,GACa,SAAA,uBAAuB,CACrC,MAAuB,EACvB,YAA2B,EAAA;IAE3B,MAAM,SAAS,GAAG,uOAAI,YAAS,CAAC,MAAM,CAAC;IAEvC,YAAY,CAAC,OAAO,EAAC,WAAW,IAAG;QACjC,WAAW,CAAC,KAAK,CAAC,OAAO,EAAC,IAAI,IAAG;YAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;QACtB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO,SAAS;AAClB;ACrBA;;;;CAIG,GACG,SAAU,cAAc,CAAC,KAAmB,EAAA;IAChD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAE;QAC3C,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;QAE9B,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE;YAChD,OAAO,IAAI;;;IAIf,OAAO,IAAI;AACb;ACbA;;;;;CAKG,GACa,SAAA,YAAY,CAAC,IAAqB,EAAE,SAAoB,EAAA;IACtE,MAAM,YAAY,GAAkB,EAAE;IAEtC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,GAAG,KAAI;QAC9B,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE;YACpB,YAAY,CAAC,IAAI,CAAC;gBAChB,IAAI,EAAE,KAAK;gBACX,GAAG;YACJ,CAAA,CAAC;;IAEN,CAAC,CAAC;IAEF,OAAO,YAAY;AACrB;ACnBA;;;;;;CAMG,YACa,mBAAmB,CACjC,IAAqB,EACrB,KAAY,EACZ,SAAoB,EAAA;IAEpB,MAAM,YAAY,GAAkB,EAAE;;;;;;;;;;IAatC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,GAAG,KAAI;QACrD,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE;YACpB,YAAY,CAAC,IAAI,CAAC;gBAChB,IAAI,EAAE,KAAK;gBACX,GAAG;YACJ,CAAA,CAAC;;IAEN,CAAC,CAAC;IAEF,OAAO,YAAY;AACrB;ACnCA;;;;;;;;CAQG,GACa,SAAA,0BAA0B,CACxC,IAAiB,EACjB,SAAoB,EAAA;IASpB,IAAK,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAE;QACtC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAEzB,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;YACnB,OAAO;gBACL,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;gBAC/B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;gBACpB,KAAK,EAAE,CAAC;gBACR,IAAI;aACL;;;AAGP;AC/BA;;;;;;;CAOG,GACG,SAAU,cAAc,CAAC,SAAoB,EAAA;IACjD,OAAO,CAAC,SAAoB,GAAK,0BAA0B,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC;AACzF;ACRgB,SAAA,SAAS,CAAC,UAAsB,EAAE,MAAe,EAAA;IAC/D,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,OAAO,CAAC,UAAU,CAAC;IAE/D,OAAO,6BAA6B,CAAC,kBAAkB,EAAE,MAAM,CAAC;AAClE;ACLA;;;;;CAKG,GACa,SAAA,YAAY,CAAC,GAAgB,EAAE,UAAsB,EAAA;IACnE,MAAM,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;IACpC,MAAM,WAAW,GAAGC,kOAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC;IAE9C,OAAO,mBAAmB,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC;AACzD;ACXA;;;;;CAKG,GACa,SAAA,YAAY,CAAC,IAAY,EAAE,UAAsB,EAAA;IAC/D,MAAM,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;IACpC,MAAM,GAAG,GAAG,iBAAiB,CAAC,IAAI,CAAC;IAEnC,kOAAO,YAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE;AACzD;ACZA;;;;;;;;CAQG,GACa,SAAA,OAAO,CACrB,IAAqB,EACrB,OAGC,EAAA;IAED,MAAM,KAAK,GAAG;QACZ,IAAI,EAAE,CAAC;QACP,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;KACtB;IAED,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC;AAC7C;ACpBA;;;;;;CAMG,YACa,YAAY,CAC1B,GAAgB,EAChB,UAAsB,EACtB,OAGC,EAAA;IAED,MAAM,EAAE,cAAc,GAAG,MAAM,EAAE,eAAe,GAAG,CAAA,CAAE,EAAE,GAAG,OAAO,IAAI,CAAA,CAAE;IACvE,MAAM,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;IACpC,MAAM,WAAW,8NAAGA,OAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC;IAE9C,OAAO,OAAO,CAAC,WAAW,EAAE;QAC1B,cAAc;QACd,eAAe,EAAE;YACf,GAAG,4BAA4B,CAAC,MAAM,CAAC;YACvC,GAAG,eAAe;QACnB,CAAA;IACF,CAAA,CAAC;AACJ;AC5BgB,SAAA,iBAAiB,CAC/B,KAAkB,EAClB,UAA6B,EAAA;IAE7B,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;IAClD,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC,SAAS;IACpC,MAAM,KAAK,GAAW,EAAE;IAExB,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,IAAG;QACtC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;IAClB,CAAC,CAAC;IAEF,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC,IAAI,EAAC,QAAQ,GAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC;IAE/E,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,CAAA,CAAE;;IAGX,OAAO;QAAE,GAAG,IAAI,CAAC,KAAK;IAAA,CAAE;AAC1B;ACjBA;;;;;CAKG,GACa,SAAA,aAAa,CAC3B,KAAkB,EAClB,UAAwC,EAAA;IAExC,MAAM,UAAU,GAAG,uBAAuB,CACxC,OAAO,UAAU,KAAK,QAAQ,GAAG,UAAU,GAAG,UAAU,CAAC,IAAI,EAC7D,KAAK,CAAC,MAAM,CACb;IAED,IAAI,UAAU,KAAK,MAAM,EAAE;QACzB,OAAO,iBAAiB,CAAC,KAAK,EAAE,UAAsB,CAAC;;IAGzD,IAAI,UAAU,KAAK,MAAM,EAAE;QACzB,OAAO,iBAAiB,CAAC,KAAK,EAAE,UAAsB,CAAC;;IAGzD,OAAO,CAAA,CAAE;AACX;AC/BA;;;CAGG,GACG,SAAU,gBAAgB,CAAI,KAAU,EAAE,EAAE,GAAG,IAAI,CAAC,SAAS,EAAA;IACjE,MAAM,IAAI,GAAqB,CAAA,CAAE;IAEjC,OAAO,KAAK,CAAC,MAAM,EAAC,IAAI,IAAG;QACzB,MAAM,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC;QAEpB,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IACjD,QACC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;IACxB,CAAC,CAAC;AACJ;ACJA;;;CAGG,GACH,SAAS,qBAAqB,CAAC,OAAuB,EAAA;IACpD,MAAM,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC;IAE/C,OAAO,aAAa,CAAC,MAAM,KAAK,IAC5B,gBACA,aAAa,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,KAAI;QACvC,MAAM,IAAI,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAK,CAAC,KAAK,KAAK,CAAC;QAExD,OAAO,CAAC,IAAI,CAAC,IAAI,EAAC,WAAW,IAAG;YAC9B,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAA,IAC/C,MAAM,CAAC,QAAQ,CAAC,EAAE,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAA,IAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAA,IAC7C,MAAM,CAAC,QAAQ,CAAC,EAAE,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE;QACpD,CAAC,CAAC;IACJ,CAAC,CAAC;AACN;AAEA;;;CAGG,GACG,SAAU,gBAAgB,CAAC,SAAoB,EAAA;IACnD,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,SAAS;IACpC,MAAM,OAAO,GAAmB,EAAE;IAElC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,KAAI;QACtC,MAAM,MAAM,GAAY,EAAE;;;;QAK1B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE;YAC1B,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC,KAAK,CAG/B;YAED,IAAI,IAAI,KAAK,SAAS,IAAI,EAAE,KAAK,SAAS,EAAE;gBAC1C;;YAGF,MAAM,CAAC,IAAI,CAAC;gBAAE,IAAI;gBAAE,EAAE;YAAA,CAAE,CAAC;eACpB;YACL,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,KAAI;gBAC3B,MAAM,CAAC,IAAI,CAAC;oBAAE,IAAI;oBAAE,EAAE;gBAAA,CAAE,CAAC;YAC3B,CAAC,CAAC;;QAGJ,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,KAAI;YAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACnD,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;YAC3C,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YACnD,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC;YAE3C,OAAO,CAAC,IAAI,CAAC;gBACX,QAAQ,EAAE;oBACR,IAAI,EAAE,QAAQ;oBACd,EAAE,EAAE,MAAM;gBACX,CAAA;gBACD,QAAQ,EAAE;oBACR,IAAI,EAAE,QAAQ;oBACd,EAAE,EAAE,MAAM;gBACX,CAAA;YACF,CAAA,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO,qBAAqB,CAAC,OAAO,CAAC;AACvC;SCzEgB,YAAY,CAAC,IAAqB,EAAE,WAAW,GAAG,CAAC,EAAA;IACjE,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW;IAC5D,MAAM,SAAS,GAAG,SAAS,GAAG,CAAC,GAAG,CAAC;IACnC,MAAM,IAAI,GAAG,WAAW;IACxB,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,QAAQ;IAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAG;QAClC,MAAM,MAAM,GAAkD;YAC5D,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;SACrB;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE;YAClC,MAAM,CAAC,KAAK,GAAG;gBAAE,GAAG,IAAI,CAAC,KAAK;YAAA,CAAE;;QAGlC,OAAO,MAAM;IACf,CAAC,CAAC;IACF,MAAM,KAAK,GAAG;QAAE,GAAG,IAAI,CAAC,KAAK;IAAA,CAAE;IAC/B,MAAM,MAAM,GAAqB;QAC/B,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;QACpB,IAAI;QACJ,EAAE;KACH;IAED,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE;QAC7B,MAAM,CAAC,KAAK,GAAG,KAAK;;IAGtB,IAAI,KAAK,CAAC,MAAM,EAAE;QAChB,MAAM,CAAC,KAAK,GAAG,KAAK;;IAGtB,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;QAC3B,MAAM,CAAC,OAAO,GAAG,EAAE;QAEnB,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,MAAM,KAAI;;YAC7B,CAAA,EAAA,GAAA,MAAM,CAAC,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,GAAG,MAAM,GAAG,SAAS,CAAC,CAAC;QAC7E,CAAC,CAAC;;IAGJ,IAAI,IAAI,CAAC,IAAI,EAAE;QACb,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;;IAGzB,OAAO,MAAM;AACf;SChDgB,eAAe,CAAC,IAAY,EAAE,EAAU,EAAE,GAAoB,EAAA;IAC5E,MAAM,KAAK,GAAgB,EAAE;;IAG7B,IAAI,IAAI,KAAK,EAAE,EAAE;QACf,IACG,OAAO,CAAC,IAAI,EACZ,KAAK,GACL,OAAO,EAAC,IAAI,IAAG;YACd,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;YAC9B,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC;YAE3C,IAAI,CAAC,KAAK,EAAE;gBACV;;YAGF,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI;gBACJ,GAAG,KAAK;YACT,CAAA,CAAC;QACJ,CAAC,CAAC;WACC;QACL,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,KAAI;YACvC,IAAI,CAAC,IAAI,IAAI,CAAA,IAAI,KAAJ,IAAA,IAAA,IAAI,KAAJ,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,IAAI,CAAE,QAAQ,MAAK,SAAS,EAAE;gBACzC;;YAGF,KAAK,CAAC,IAAI,CACR,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAC,IAAI,GAAA,CAAK;oBACzB,IAAI,EAAE,GAAG;oBACT,EAAE,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ;oBACvB,IAAI;iBACL,CAAC,CAAC,CACJ;QACH,CAAC,CAAC;;IAGJ,OAAO,KAAK;AACd;ACxCA;;;;;;;CAOG,GACI,MAAM,iBAAiB,GAAG,CAAC,KAAkB,EAAE,UAA6B,EAAE,GAAW,EAAE,QAAQ,GAAG,EAAE,KAAI;IACjH,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC;IAEnC,IAAI,YAAY,GAAG,QAAQ;IAC3B,IAAI,IAAI,GAAgB,IAAI;IAE5B,MAAO,YAAY,GAAG,CAAC,IAAI,IAAI,KAAK,IAAI,CAAE;QACxC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;QAE3C,IAAI,CAAA,WAAW,KAAX,IAAA,IAAA,WAAW,KAAX,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,WAAW,CAAE,IAAI,CAAC,IAAI,MAAK,UAAU,EAAE;YACzC,IAAI,GAAG,WAAW;eACb;YACL,YAAY,IAAI,CAAC;;;IAIrB,OAAO;QAAC,IAAI;QAAE,YAAY;KAA0B;AACtD;AC1BA;;;;;;CAMG,YACa,qBAAqB,CACnC,mBAAyC,EACzC,QAAgB,EAChB,UAA+B,EAAA;IAE/B,OAAO,MAAM,CAAC,WAAW,CAAC,OACvB,OAAO,CAAC,UAAU,EAClB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAI;QACjB,MAAM,kBAAkB,GAAG,mBAAmB,CAAC,IAAI,EAAC,IAAI,IAAG;YACzD,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI;QACrD,CAAC,CAAC;QAEF,IAAI,CAAC,kBAAkB,EAAE;YACvB,OAAO,KAAK;;QAGd,OAAO,kBAAkB,CAAC,SAAS,CAAC,WAAW;KAChD,CAAC,CAAC;AACP;ACpBM,SAAU,YAAY,CAC1B,KAAkB,EAClB,UAAoC,EACpC,aAAkC,CAAA,CAAE,EAAA;IAEpC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS;IACzC,MAAM,IAAI,GAAG,UAAU,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI;IAEtE,IAAI,KAAK,EAAE;QACT,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,EACzD,MAAM,CAAC,IAAI,IAAG;YACb,IAAI,CAAC,IAAI,EAAE;gBACT,OAAO,IAAI;;YAGb,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI;QACrC,CAAC,EACA,IAAI,CAAC,IAAI,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE;gBAAE,MAAM,EAAE,KAAK;YAAA,CAAE,CAAC,CAAC;;IAG5E,IAAI,cAAc,GAAG,CAAC;IACtB,MAAM,UAAU,GAAgB,EAAE;IAElC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,KAAI;QAChC,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG;QACtB,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG;QAElB,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,KAAI;YAC7C,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBACtC;;YAGF,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;YACxC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;YACpD,MAAM,KAAK,GAAG,UAAU,GAAG,YAAY;YAEvC,cAAc,IAAI,KAAK;YAEvB,UAAU,CAAC,IAAI,CACb,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAC,IAAI,GAAA,CAAK;oBACzB,IAAI;oBACJ,IAAI,EAAE,YAAY;oBAClB,EAAE,EAAE,UAAU;iBACf,CAAC,CAAC,CACJ;QACH,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAI,cAAc,KAAK,CAAC,EAAE;QACxB,OAAO,KAAK;;;IAId,MAAM,YAAY,GAAG,WAClB,MAAM,EAAC,SAAS,IAAG;QAClB,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,IAAI;;QAGb,OAAO,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;IAC/C,CAAC,EACA,MAAM,EAAC,SAAS,GAAI,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE;YAAE,MAAM,EAAE,KAAK;QAAA,CAAE,CAAC,EACvF,MAAM,CAAC,CAAC,GAAG,EAAE,SAAS,GAAK,GAAG,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;;;IAIrE,MAAM,aAAa,GAAG,WACnB,MAAM,EAAC,SAAS,IAAG;QAClB,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,IAAI;;QAGb,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC3E,CAAC,EACA,MAAM,CAAC,CAAC,GAAG,EAAE,SAAS,GAAK,GAAG,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;;;IAIrE,MAAM,KAAK,GAAG,YAAY,GAAG,CAAC,GAAG,YAAY,GAAG,aAAa,GAAG,YAAY;IAE5E,OAAO,KAAK,IAAI,cAAc;AAChC;AClFM,SAAU,QAAQ,CACtB,KAAkB,EAClB,IAAmB,EACnB,aAAkC,CAAA,CAAE,EAAA;IAEpC,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;;IAGvF,MAAM,UAAU,GAAG,uBAAuB,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC;IAE9D,IAAI,UAAU,KAAK,MAAM,EAAE;QACzB,OAAO,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;;IAG9C,IAAI,UAAU,KAAK,MAAM,EAAE;QACzB,OAAO,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;;IAG9C,OAAO,KAAK;AACd;MCtBa,aAAa,GAAG,CAAC,KAAkB,EAAE,QAAiB,KAAI;IACrE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC,SAAS;IAE/C,IAAI,QAAQ,EAAE;QACZ,MAAM,UAAU,GAAG,cAAc,EAAC,IAAI,GAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC;QAEvF,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,KAAK;;QAGd,MAAM,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC;QAExD,IAAI,OAAO,CAAC,GAAG,GAAG,CAAC,KAAK,UAAU,CAAC,GAAG,EAAE,EAAE;YACxC,OAAO,IAAI;;QAGb,OAAO,KAAK;;IAGd,IAAI,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,EAAE;QACvE,OAAO,KAAK;;IAGd,OAAO,IAAI;AACb;AC1Ba,MAAA,eAAe,GAAG,CAAC,KAAkB,KAAI;IACpD,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC,SAAS;IAEtC,IAAI,KAAK,CAAC,YAAY,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,EAAE;QACnD,OAAO,KAAK;;IAGd,OAAO,IAAI;AACb;ACJgB,SAAA,MAAM,CAAC,IAAY,EAAE,UAAsB,EAAA;IACzD,MAAM,EAAE,cAAc,EAAE,GAAG,eAAe,CAAC,UAAU,CAAC;IACtD,MAAM,SAAS,GAAG,cAAc,CAAC,IAAI,EAAC,IAAI,GAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;IAEjE,IAAI,CAAC,SAAS,EAAE;QACd,OAAO,KAAK;;IAGd,MAAM,OAAO,GAAG;QACd,IAAI,EAAE,SAAS,CAAC,IAAI;QACpB,OAAO,EAAE,SAAS,CAAC,OAAO;QAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;KAC3B;IACD,MAAM,KAAK,GAAG,YAAY,CAAC,iBAAiB,CAAsB,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAE/F,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,KAAK;;IAGd,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC1C;ACxBA;;CAEG,GACa,SAAA,WAAW,CACzB,IAAqB,EACrB,EACE,aAAa,GAAG,IAAI,EACpB,gBAAgB,GAAG,KAAK,EAAA,GAUtB,CAAA,CAAE,EAAA;;IAEN,IAAI,gBAAgB,EAAE;QACpB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE;;YAElC,OAAO,IAAI;;QAEb,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE,CAAC;;;IAIzC,IAAI,IAAI,CAAC,MAAM,EAAE;QACf,OAAO,CAAC,IAAI,CAAC,IAAI;;IAGnB,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE;QAC9B,OAAO,KAAK;;IAGd,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,CAAC,EAAE;QACjC,OAAO,IAAI;;IAGb,IAAI,aAAa,EAAE;QACjB,IAAI,cAAc,GAAG,IAAI;QAEzB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,SAAS,IAAG;YAC/B,IAAI,cAAc,KAAK,KAAK,EAAE;;gBAE5B;;YAGF,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;gBAAE,gBAAgB;gBAAE,aAAa;YAAA,CAAE,CAAC,EAAE;gBAChE,cAAc,GAAG,KAAK;;QAE1B,CAAC,CAAC;QAEF,OAAO,cAAc;;IAGvB,OAAO,KAAK;AACd;AC3DM,SAAU,eAAe,CAAC,KAAc,EAAA;IAC5C,OAAO,KAAK,sOAAY,gBAAa;AACvC;SCAgB,YAAY,CAAC,IAAgB,EAAE,IAAY,EAAE,EAAU,EAAA;IACrE,MAAM,MAAM,GAAG,CAAC;IAChB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI;IAC1C,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC;IACjD,MAAM,WAAW,GAAG,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC;IAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC;IAC5C,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC7C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC;IACxC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC;IACjD,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC;IAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC;IAC9C,MAAM,KAAK,GAAG,KAAK,GAAG,IAAI;IAC1B,MAAM,MAAM,GAAG,MAAM,GAAG,GAAG;IAC3B,MAAM,CAAC,GAAG,IAAI;IACd,MAAM,CAAC,GAAG,GAAG;IACb,MAAM,IAAI,GAAG;QACX,GAAG;QACH,MAAM;QACN,IAAI;QACJ,KAAK;QACL,KAAK;QACL,MAAM;QACN,CAAC;QACD,CAAC;KACF;IAED,OAAO;QACL,GAAG,IAAI;QACP,MAAM,EAAE,IAAM,IAAI;KACnB;AACH;ACXA;;CAEG,GACH,SAAS,0BAA0B,CAAC,EAClC,IAAI,EACJ,UAAU,EACV,UAAU,EACV,OAAO,EACP,gBAAgB,GAAG,EAAE,EAOtB,EAAA;IAUC,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QAC3C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAG;YACpC,MAAM,IAAI,GAAG,OAAO,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI;YAExD,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACxB,OAAO,IAAI;;YAGb,gBAAgB,CAAC,IAAI,CAAC;gBACpB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC1C,WAAW,EAAE,IAAI;YAClB,CAAA,CAAC;;YAEF,OAAO,KAAK;QACd,CAAC,CAAC;;IAGJ,IAAI,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;QAC/C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAA,CACjB,GAAG,EACF,KAAK,GAAI,0BAA0B,CAAC;gBAClC,IAAI,EAAE,KAAK;gBACX,UAAU;gBACV,UAAU;gBACV,OAAO;gBACP,gBAAgB;aACjB,CAAC,CAAC,IAAI,EAER,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,SAAS,CAAC;;IAG/C,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC3C,gBAAgB,CAAC,IAAI,CAAC;YACpB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC1C,WAAW,EAAE,IAAI,CAAC,IAAI;QACvB,CAAA,CAAC;QAEF,IAAI,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAK,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,mBAAmB,MAAK,KAAK,CAAC,CAAE;;YAE3F,IAAI,CAAC,IAAI,GAAG,WAAW;YAEvB,OAAO;gBACL,IAAI;gBACJ,gBAAgB;aACjB;;;QAIH,OAAO;YACL,IAAI,EAAE,IAAI;YACV,gBAAgB;SACjB;;IAGH,OAAO;QAAE,IAAI;QAAE,gBAAgB;IAAA,CAAE;AACnC;AAEA;;;CAGG,YACa,qBAAqB,CACnC;;CAEG,GACH,IAAiB,EACjB;;CAEG,GACH,MAAc,EACd;;CAEG,GACH,OAAsC,EAAA;IAoBtC,OAAO,0BAA0B,CAAC;QAChC,IAAI;QACJ,UAAU,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC9C,UAAU,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC9C,OAAO;IACR,CAAA,CAAC;AACJ;AC9HA,SAAS,UAAU,CAAC,KAAkB,EAAE,EAAe,EAAE,WAAqB,EAAA;;IAC5E,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;IACxB,IAAI,MAAM,GAAuB,IAAI;IAErC,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE;QAC9B,MAAM,GAAG,SAAS,CAAC,OAAO;;IAG5B,IAAI,MAAM,EAAE;QACV,MAAM,YAAY,GAAG,CAAA,EAAA,GAAA,KAAK,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,MAAM,CAAC,KAAK,EAAE;;QAGxD,OACE,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY,KAC/B,CAAC,YAAY,CAAC,IAAI,EAAC,IAAI,GAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;;IAIlE,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS;IAE5B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,KAAI;QACpC,IAAI,oBAAoB,GAAG,KAAK,CAAC,KAAK,KAAK,IACvC,KAAK,CAAC,GAAG,CAAC,aAAa,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,IACpE,KAAK;QAET,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,KAAI;;YAEhE,IAAI,oBAAoB,EAAE;gBACxB,OAAO,KAAK;;YAGd,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,MAAM,oBAAoB,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;gBAC/E,MAAM,yBAAyB,GAAG,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,KAC7D,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAC,SAAS,GAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBAExE,oBAAoB,GAAG,oBAAoB,IAAI,yBAAyB;;YAE1E,OAAO,CAAC,oBAAoB;QAC9B,CAAC,CAAC;QAEF,OAAO,oBAAoB;IAC7B,CAAC,CAAC;AACJ;AACO,MAAM,OAAO,GAA2B,CAAC,UAAU,EAAE,UAAU,GAAG,CAAA,CAAE,GAAK,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QAC1G,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;QACxB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,SAAS;QACnC,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;QAElD,IAAI,QAAQ,EAAE;YACZ,IAAI,KAAK,EAAE;gBACT,MAAM,aAAa,GAAG,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC;gBAEpD,EAAE,CAAC,aAAa,CACd,IAAI,CAAC,MAAM,CAAC;oBACV,GAAG,aAAa;oBAChB,GAAG,UAAU;gBACd,CAAA,CAAC,CACH;mBACI;gBACL,MAAM,CAAC,OAAO,EAAC,KAAK,IAAG;oBACrB,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG;oBAC5B,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG;oBAExB,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,KAAI;wBAC7C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;wBACvC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;wBACnD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAC,IAAI,GAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;;;;wBAK/D,IAAI,WAAW,EAAE;4BACf,IAAI,CAAC,KAAK,CAAC,OAAO,EAAC,IAAI,IAAG;gCACxB,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;oCACtB,EAAE,CAAC,OAAO,CACR,WAAW,EACX,SAAS,EACT,IAAI,CAAC,MAAM,CAAC;wCACV,GAAG,IAAI,CAAC,KAAK;wCACb,GAAG,UAAU;oCACd,CAAA,CAAC,CACH;;4BAEL,CAAC,CAAC;+BACG;4BACL,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;;oBAE/D,CAAC,CAAC;gBACJ,CAAC,CAAC;;;QAIN,OAAO,UAAU,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC;IACpC,CAAC;ACjGM,MAAM,OAAO,GAA2B,CAAC,GAAG,EAAE,KAAK,GAAK,CAAC,EAAE,EAAE,EAAE,KAAI;QACxE,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC;QAEtB,OAAO,IAAI;IACb,CAAC;ACFM,MAAM,OAAO,GAA2B,CAAC,UAAU,EAAE,UAAU,GAAG,CAAA,CAAE,GAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAI;QAC7G,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;QAElD,IAAI,gBAAiD;QAErD,IAAI,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;;YAE7D,gBAAgB,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK;;;QAIzD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,OAAO,CAAC,IAAI,CAAC,sEAAsE,CAAC;YAEpF,OAAO,KAAK;;QAGd,OACE,KAAK;SAEF,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAI;YACxB,MAAM,WAAW,uOAAG,eAAA,AAAY,EAAC,IAAI,EAAE;gBAAE,GAAG,gBAAgB;gBAAE,GAAG,UAAU;YAAA,CAAE,CAAC,CAAC,KAAK,CAAC;YAErF,IAAI,WAAW,EAAE;gBACf,OAAO,IAAI;;YAGb,OAAO,QAAQ,CAAC,UAAU,EAAE;QAC9B,CAAC,EACA,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAI;YACnC,WAAO,+OAAA,AAAY,EAAC,IAAI,EAAE;gBAAE,GAAG,gBAAgB;gBAAE,GAAG,UAAU;YAAA,CAAE,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC;QAC3F,CAAC,EACA,GAAG,EAAE;IAEZ,CAAC;ACpCM,MAAM,gBAAgB,IAAoC,QAAQ,GAAI,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;QAChG,IAAI,QAAQ,EAAE;YACZ,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;YAClB,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;YAClD,MAAM,SAAS,6NAAG,gBAAa,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;YAEjD,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC;;QAG5B,OAAO,IAAI;IACb,CAAC;ACVM,MAAM,gBAAgB,IAAoC,QAAQ,GAAI,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;QAChG,IAAI,QAAQ,EAAE;YACZ,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;YAClB,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,OAAO,QAAQ,KAAK,QAAQ,GAAG;gBAAE,IAAI,EAAE,QAAQ;gBAAE,EAAE,EAAE,QAAQ;YAAA,CAAE,GAAG,QAAQ;YAC/F,MAAM,MAAM,6NAAG,gBAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI;YAC9C,MAAM,MAAM,6NAAG,gBAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE;YAC1C,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC;YACjD,MAAM,WAAW,GAAG,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC;YAC9C,MAAM,SAAS,6NAAG,gBAAa,CAAC,MAAM,CAAC,GAAG,EAAE,YAAY,EAAE,WAAW,CAAC;YAEtE,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC;;QAG5B,OAAO,IAAI;IACb,CAAC;ACbM,MAAM,YAAY,IAAgC,UAAU,GAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QAC7F,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;QAElD,uPAAOC,eAAAA,AAAoB,EAAC,IAAI,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC;IACpD,CAAC;AChBD,SAAS,WAAW,CAAC,KAAkB,EAAE,eAA0B,EAAA;IACjE,MAAM,KAAK,GAAG,KAAK,CAAC,WAAW,IAAK,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IAEtG,IAAI,KAAK,EAAE;QACT,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,EAAC,IAAI,GAAI,eAAe,KAAA,IAAA,IAAf,eAAe,KAAf,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,eAAe,CAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAErF,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC;;AAEvC;AAgBO,MAAM,UAAU,GAA8B,CAAC,EAAE,SAAS,GAAG,IAAI,EAAE,GAAG,CAAA,CAAE,GAAK,CAAC,EACnF,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAC5B,KAAI;QACH,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE;QAC7B,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,SAAS;QAChC,MAAM,mBAAmB,GAAG,MAAM,CAAC,gBAAgB,CAAC,UAAU;QAC9D,MAAM,aAAa,GAAG,qBAAqB,CACzC,mBAAmB,EACnB,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,EACtB,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CACnB;QAED,IAAI,SAAS,sOAAY,gBAAa,IAAI,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE;YAChE,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,EAAC,iPAAA,AAAQ,EAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE;gBACpD,OAAO,KAAK;;YAGd,IAAI,QAAQ,EAAE;gBACZ,IAAI,SAAS,EAAE;oBACb,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC;;gBAG7D,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE;;YAGtC,OAAO,IAAI;;QAGb,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE;YACzB,OAAO,KAAK;;QAGd,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,KAAK,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;QAE1D,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,KAAK,IAC1B,YACA,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEvE,IAAI,KAAK,GAAG,KAAK,IAAI,QACjB;YACA;gBACE,IAAI,EAAE,KAAK;gBACX,KAAK,EAAE,aAAa;YACrB,CAAA;SACF,GACC,SAAS;QAEb,IAAI,GAAG,0OAAG,WAAA,AAAQ,EAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;QAE/D,IACE,CAAC,SACI,CAAC,8OACD,WAAA,AAAQ,EAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,KAAK,GAAG;YAAC;gBAAE,IAAI,EAAE,KAAK;YAAA,CAAE;SAAC,GAAG,SAAS,CAAC,EAC1F;YACA,GAAG,GAAG,IAAI;YACV,KAAK,GAAG,QACJ;gBACA;oBACE,IAAI,EAAE,KAAK;oBACX,KAAK,EAAE,aAAa;gBACrB,CAAA;aACF,GACC,SAAS;;QAGf,IAAI,QAAQ,EAAE;YACZ,IAAI,GAAG,EAAE;gBACP,IAAI,SAAS,sOAAY,gBAAa,EAAE;oBACtC,EAAE,CAAC,eAAe,EAAE;;gBAGtB,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;gBAE7C,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE;oBACzE,MAAM,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;oBAC5C,MAAM,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC;oBAEpC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE;wBAC5E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC;;;;YAK7D,IAAI,SAAS,EAAE;gBACb,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC;;YAG7D,EAAE,CAAC,cAAc,EAAE;;QAGrB,OAAO,GAAG;IACZ,CAAC;AClGM,MAAM,aAAa,GAAiC,CAAC,UAAU,EAAE,aAAa,GAAG,CAAA,CAAE,GAAK,CAAC,EAC9F,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAC5B,KAAI;;QACH,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;QAClD,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC,SAAS;;;QAIpC,MAAM,IAAI,GAAoB,KAAK,CAAC,SAAS,CAAC,IAAI;QAEpD,IAAI,AAAC,IAAI,IAAI,IAAI,CAAC,OAAO,IAAK,KAAK,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACvE,OAAO,KAAK;;QAGd,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAElC,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,EAAE;YAC7B,OAAO,KAAK;;QAGd,MAAM,mBAAmB,GAAG,MAAM,CAAC,gBAAgB,CAAC,UAAU;QAE9D,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,KAAK,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;;;;YAIzF,IACE,KAAK,CAAC,KAAK,KAAK,KACX,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QACxB,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,EACtD;gBACA,OAAO,KAAK;;YAGd,IAAI,QAAQ,EAAE;gBACZ,IAAI,IAAI,8NAAG,WAAQ,CAAC,KAAK;;gBAEvB,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;;;gBAInE,IAAK,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,WAAW,EAAE,CAAC,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAE;oBACpE,IAAI,8NAAG,WAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;gBAI9C,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC;;gBAGpI,MAAM,qBAAqB,GAAG;oBAC5B,GAAG,qBAAqB,CACtB,mBAAmB,EACnB,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,EACtB,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CACnB;oBACD,GAAG,aAAa;iBACjB;gBACD,MAAM,QAAQ,GAAG,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,aAAa,CAAC,qBAAqB,CAAC,KAAI,SAAS;gBAEjG,IAAI,GAAG,IAAI,CAAC,MAAM,4NAAC,WAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,SAAS,CAAC,CAAC;gBAElF,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,GAAA,CAAI,WAAW,GAAG,CAAC,CAAC,CAAC;gBAE3D,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,EAAE,+NAAI,QAAK,CAAC,IAAI,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,CAAC,CAAC;gBAEhF,IAAI,GAAG,GAAG,CAAC,CAAC;gBAEZ,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,GAAG,KAAI;oBACzD,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE;wBACZ,OAAO,KAAK;;oBAGd,IAAI,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE;wBACzC,GAAG,GAAG,GAAG,GAAG,CAAC;;gBAEjB,CAAC,CAAC;gBAEF,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE;oBACZ,EAAE,CAAC,YAAY,2NAAC,gBAAa,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;;gBAG1D,EAAE,CAAC,cAAc,EAAE;;YAGrB,OAAO,IAAI;;QAGb,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI;QAE3F,MAAM,iBAAiB,GAAG;YACxB,GAAG,qBAAqB,CACtB,mBAAmB,EACnB,WAAW,CAAC,IAAI,CAAC,IAAI,EACrB,WAAW,CAAC,KAAK,CAClB;YACD,GAAG,aAAa;SACjB;QACD,MAAM,qBAAqB,GAAG;YAC5B,GAAG,qBAAqB,CACtB,mBAAmB,EACnB,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,EACtB,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CACnB;YACD,GAAG,aAAa;SACjB;QAED,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC;QAE7B,MAAM,KAAK,GAAG,WACV;YACA;gBAAE,IAAI;gBAAE,KAAK,EAAE,iBAAiB;YAAA,CAAE;YAClC;gBAAE,IAAI,EAAE,QAAQ;gBAAE,KAAK,EAAE,qBAAqB;YAAA,CAAE;SACjD,GACC;YAAC;gBAAE,IAAI;gBAAE,KAAK,EAAE,iBAAiB;YAAA,CAAE;SAAC;QAExC,IAAI,wOAAC,WAAA,AAAQ,EAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE;YACnC,OAAO,KAAK;;QAGd,IAAI,QAAQ,EAAE;YACZ,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,KAAK;YACxC,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,CAAC,gBAAgB;YACnD,MAAM,KAAK,GAAG,WAAW,IAAK,SAAS,CAAC,GAAG,CAAC,YAAY,IAAI,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YAEpF,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,cAAc,EAAE;YAE9C,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI;;YAGb,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,EAAC,IAAI,GAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEpF,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC;;QAG/B,OAAO,IAAI;IACb,CAAC;ACvJD,MAAM,iBAAiB,GAAG,CAAC,EAAe,EAAE,QAAkB,KAAa;IACzE,MAAM,IAAI,GAAG,cAAc,EAAC,IAAI,GAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC;IAEzE,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,IAAI;;IAGb,MAAM,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;IAE3E,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,OAAO,IAAI;;IAGb,MAAM,UAAU,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC;IACxC,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,KAAA,CAAK,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,IAAI,CAAA,IAAI,iPAAA,AAAO,EAAC,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC;IAEzF,IAAI,CAAC,gBAAgB,EAAE;QACrB,OAAO,IAAI;;IAGb,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;IAEjB,OAAO,IAAI;AACb,CAAC;AAED,MAAM,gBAAgB,GAAG,CAAC,EAAe,EAAE,QAAkB,KAAa;IACxE,MAAM,IAAI,GAAG,cAAc,EAAC,IAAI,GAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC;IAEzE,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,IAAI;;IAGb,MAAM,KAAK,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;IAE1D,IAAI,KAAK,KAAK,SAAS,EAAE;QACvB,OAAO,IAAI;;IAGb,MAAM,SAAS,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC;IACtC,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,KAAA,CAAK,SAAS,KAAA,IAAA,IAAT,SAAS,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAT,SAAS,CAAE,IAAI,CAAA,IAAI,iPAAA,AAAO,EAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC;IAEpF,IAAI,CAAC,eAAe,EAAE;QACpB,OAAO,IAAI;;IAGb,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;IAEd,OAAO,IAAI;AACb,CAAC;AAkBM,MAAM,UAAU,GAA8B,CAAC,cAAc,EAAE,cAAc,EAAE,SAAS,EAAE,UAAU,GAAG,CAAA,CAAE,GAAK,CAAC,EACpH,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,EAClD,KAAI;QACH,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,GAAG,MAAM,CAAC,gBAAgB;QAC/D,MAAM,QAAQ,GAAG,WAAW,CAAC,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC;QAC1D,MAAM,QAAQ,GAAG,WAAW,CAAC,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC;QAC1D,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,KAAK;QACxC,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,SAAS;QAChC,MAAM,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC;QAEnC,MAAM,KAAK,GAAG,WAAW,IAAK,SAAS,CAAC,GAAG,CAAC,YAAY,IAAI,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QAEpF,IAAI,CAAC,KAAK,EAAE;YACV,OAAO,KAAK;;QAGd,MAAM,UAAU,GAAG,cAAc,EAAC,IAAI,GAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC;QAExF,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,UAAU,IAAI,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,IAAI,CAAC,EAAE;;YAEzE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACrC,OAAO,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC;;;YAIxC,IACE,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,KACvC,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,KAC7C,QAAQ,EACb;gBACA,OAAO,KAAK,GACT,OAAO,CAAC,MAAK;oBACZ,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,EAAE,QAAQ,CAAC;oBAE1C,OAAO,IAAI;gBACb,CAAC,EACA,OAAO,CAAC,IAAM,iBAAiB,CAAC,EAAE,EAAE,QAAQ,CAAC,EAC7C,OAAO,CAAC,IAAM,gBAAgB,CAAC,EAAE,EAAE,QAAQ,CAAC,EAC5C,GAAG,EAAE;;;QAGZ,IAAI,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE;YAErC,OAAO,KAAK;aAET,OAAO,CAAC,MAAK;gBACZ,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC;gBAE5D,IAAI,aAAa,EAAE;oBACjB,OAAO,IAAI;;gBAGb,OAAO,QAAQ,CAAC,UAAU,EAAE;YAC9B,CAAC,EACA,UAAU,CAAC,QAAQ,EAAE,UAAU,EAC/B,OAAO,CAAC,IAAM,iBAAiB,CAAC,EAAE,EAAE,QAAQ,CAAC,EAC7C,OAAO,CAAC,IAAM,gBAAgB,CAAC,EAAE,EAAE,QAAQ,CAAC,EAC5C,GAAG,EAAE;;QAGV,OACE,KAAK;SAEF,OAAO,CAAC,MAAK;YACZ,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC;YAE5D,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,EAAC,IAAI,GAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEpF,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC;YAE7B,IAAI,aAAa,EAAE;gBACjB,OAAO,IAAI;;YAGb,OAAO,QAAQ,CAAC,UAAU,EAAE;QAC9B,CAAC,EACA,UAAU,CAAC,QAAQ,EAAE,UAAU,EAC/B,OAAO,CAAC,IAAM,iBAAiB,CAAC,EAAE,EAAE,QAAQ,CAAC,EAC7C,OAAO,CAAC,IAAM,gBAAgB,CAAC,EAAE,EAAE,QAAQ,CAAC,EAC5C,GAAG,EAAE;IAEZ,CAAC;ACtHM,MAAM,UAAU,GAA8B,CAAC,UAAU,EAAE,UAAU,GAAG,CAAA,CAAE,EAAE,OAAO,GAAG,CAAA,CAAE,GAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QAC1H,MAAM,EAAE,oBAAoB,GAAG,KAAK,EAAE,GAAG,OAAO;QAChD,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;QAClD,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;QAEtD,IAAI,QAAQ,EAAE;YACZ,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE;gBAAE,oBAAoB;YAAA,CAAE,CAAC;;QAG3D,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC;IAC3C,CAAC;ACvBM,MAAM,UAAU,GAA8B,CAAC,UAAU,EAAE,gBAAgB,EAAE,UAAU,GAAG,CAAA,CAAE,GAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QAC9H,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;QAClD,MAAM,UAAU,GAAG,WAAW,CAAC,gBAAgB,EAAE,KAAK,CAAC,MAAM,CAAC;QAC9D,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;QAEtD,IAAI,gBAAiD;QAErD,IAAI,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;;YAE7D,gBAAgB,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK;;QAGzD,IAAI,QAAQ,EAAE;YACZ,OAAO,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,gBAAgB,CAAC;;;;QAKvD,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE;YAAE,GAAG,gBAAgB;YAAE,GAAG,UAAU;QAAA,CAAE,CAAC;IACvE,CAAC;ACxBM,MAAM,UAAU,GAA8B,CAAC,UAAU,EAAE,UAAU,GAAG,CAAA,CAAE,GAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QAC5G,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;QAClD,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;QAEtD,IAAI,QAAQ,EAAE;YACZ,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;;QAG5B,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC;IAC1C,CAAC;ACfM,MAAM,aAAa,GAAiC,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QACvF,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;QAE7B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAE;YAC1C,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC;YACzB,IAAI,QAAQ;;;YAIZ,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,IAAA,CAAK,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;gBACnE,IAAI,QAAQ,EAAE;oBACZ,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE;oBACnB,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS;oBAEjC,IAAK,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAE;wBACpD,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;oBAGjD,IAAI,QAAQ,CAAC,IAAI,EAAE;wBACjB,MAAM,KAAK,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE;wBAEnD,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;2BAC9E;wBACL,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC;;;gBAIzC,OAAO,IAAI;;;QAIf,OAAO,KAAK;IACd,CAAC;AChCM,MAAM,aAAa,GAAiC,IAAM,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;QACpF,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;QACxB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,SAAS;QAEnC,IAAI,KAAK,EAAE;YACT,OAAO,IAAI;;QAGb,IAAI,QAAQ,EAAE;YACZ,MAAM,CAAC,OAAO,EAAC,KAAK,IAAG;gBACrB,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;YAC/C,CAAC,CAAC;;QAGJ,OAAO,IAAI;IACb,CAAC;ACGM,MAAM,SAAS,GAA6B,CAAC,UAAU,EAAE,OAAO,GAAG,CAAA,CAAE,GAAK,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;;QAC3G,MAAM,EAAE,oBAAoB,GAAG,KAAK,EAAE,GAAG,OAAO;QAChD,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;QACxB,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;QAClD,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,SAAS;QAE1C,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,IAAI;;QAGb,IAAI,KAAK,IAAI,oBAAoB,EAAE;YACjC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,SAAS;YAC5B,MAAM,KAAK,GAAG,CAAA,EAAA,GAAA,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,EAAC,IAAI,GAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK;YACnE,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;YAE9C,IAAI,KAAK,EAAE;gBACT,IAAI,GAAG,KAAK,CAAC,IAAI;gBACjB,EAAE,GAAG,KAAK,CAAC,EAAE;;YAGf,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC;eACxB;YACL,MAAM,CAAC,OAAO,EAAC,KAAK,IAAG;gBACrB,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;YACrD,CAAC,CAAC;;QAGJ,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC;QAEzB,OAAO,IAAI;IACb,CAAC;AC5BM,MAAM,gBAAgB,GAAoC,CAAC,UAAU,EAAE,UAAU,GAAG,CAAA,CAAE,GAAK,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QAE5H,IAAI,QAAQ,GAAoB,IAAI;QACpC,IAAI,QAAQ,GAAoB,IAAI;QAEpC,MAAM,UAAU,GAAG,uBAAuB,CACxC,OAAO,UAAU,KAAK,QAAQ,GAAG,UAAU,GAAG,UAAU,CAAC,IAAI,EAC7D,KAAK,CAAC,MAAM,CACb;QAED,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,KAAK;;QAGd,IAAI,UAAU,KAAK,MAAM,EAAE;YACzB,QAAQ,GAAG,WAAW,CAAC,UAAsB,EAAE,KAAK,CAAC,MAAM,CAAC;;QAG9D,IAAI,UAAU,KAAK,MAAM,EAAE;YACzB,QAAQ,GAAG,WAAW,CAAC,UAAsB,EAAE,KAAK,CAAC,MAAM,CAAC;;QAG9D,IAAI,QAAQ,EAAE;YACZ,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAqB,KAAI;gBAEpD,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG;gBAC5B,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG;gBAExB,IAAI,OAA2B;gBAC/B,IAAI,QAA0B;gBAC9B,IAAI,WAAmB;gBACvB,IAAI,SAAiB;gBAErB,IAAI,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE;oBACtB,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAU,EAAE,GAAW,KAAI;wBAE3D,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,EAAE;4BACtC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;4BACjC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;4BAC7C,OAAO,GAAG,GAAG;4BACb,QAAQ,GAAG,IAAI;;oBAEnB,CAAC,CAAC;uBACG;oBACL,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAU,EAAE,GAAW,KAAI;wBAE3D,IAAI,GAAG,GAAG,IAAI,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,EAAE;4BACpD,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;4BACjC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;4BAC7C,OAAO,GAAG,GAAG;4BACb,QAAQ,GAAG,IAAI;;wBAGjB,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,EAAE;4BAE5B,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,EAAE;gCACtC,EAAE,CAAC,aAAa,CAAC,GAAG,EAAE,SAAS,EAAE;oCAC/B,GAAG,IAAI,CAAC,KAAK;oCACb,GAAG,UAAU;gCACd,CAAA,CAAC;;4BAGJ,IAAI,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gCACjC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAU,KAAI;oCAEhC,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,EAAE;wCAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;wCACxC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;wCAEpD,EAAE,CAAC,OAAO,CACR,YAAY,EACZ,UAAU,EACV,QAAQ,CAAC,MAAM,CAAC;4CACd,GAAG,IAAI,CAAC,KAAK;4CACb,GAAG,UAAU;wCACd,CAAA,CAAC,CACH;;gCAEL,CAAC,CAAC;;;oBAGR,CAAC,CAAC;;gBAGJ,IAAI,QAAQ,EAAE;oBAEZ,IAAI,OAAO,KAAK,SAAS,EAAE;wBACzB,EAAE,CAAC,aAAa,CAAC,OAAO,EAAE,SAAS,EAAE;4BACnC,GAAG,QAAQ,CAAC,KAAK;4BACjB,GAAG,UAAU;wBACd,CAAA,CAAC;;oBAGJ,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE;wBACrC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAU,KAAI;4BAEpC,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,EAAE;gCAC1B,EAAE,CAAC,OAAO,CACR,WAAW,EACX,SAAS,EACT,QAAQ,CAAC,MAAM,CAAC;oCACd,GAAG,IAAI,CAAC,KAAK;oCACb,GAAG,UAAU;gCACd,CAAA,CAAC,CACH;;wBAEL,CAAC,CAAC;;;YAGR,CAAC,CAAC;;QAGJ,OAAO,IAAI;IACb,CAAC;AC/HM,MAAM,MAAM,GAA0B,CAAC,UAAU,EAAE,UAAU,GAAG,CAAA,CAAE,GAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QACpG,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;QAElD,0OAAOC,UAAAA,AAAc,EAAC,IAAI,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC;IAC1D,CAAC;ACJM,MAAM,UAAU,GAA8B,CAAC,UAAU,EAAE,UAAU,GAAG,CAAA,CAAE,GAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;QAC5G,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC;QAElD,OAAOC,6PAAAA,AAAkB,EAAC,IAAI,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC;IAC9D,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnBM,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC;IACvC,IAAI,EAAE,UAAU;IAEhB,WAAW,GAAA;QACT,OAAO;YACL,GAAG,QAAQ;SACZ;KACF;AACF,CAAA,CAAC;ACTK,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC;IACnC,IAAI,EAAE,MAAM;IAEZ,qBAAqB,GAAA;QACnB,OAAO;YACL,8NAAI,SAAM,CAAC;gBACT,GAAG,EAAE,8NAAI,YAAS,CAAC,YAAY,CAAC;gBAEhC,KAAK,EAAE;oBACL,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,KAAI;wBACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE;4BACvB,MAAM,EAAE,IAAI,CAAC,MAAM;4BACnB,KAAK,EAAE,CAAC;4BACR,KAAK;4BACL,KAAK;wBACN,CAAA,CAAC;qBACH;gBACF,CAAA;aACF,CAAC;SACH;KACF;AACF,CAAA,CAAC;ACrBK,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC;IACvC,IAAI,EAAE,UAAU;IAEhB,qBAAqB,GAAA;QACnB,OAAO;YACL,8NAAI,SAAM,CAAC;gBACT,GAAG,EAAE,8NAAI,YAAS,CAAC,UAAU,CAAC;gBAC9B,KAAK,EAAE;oBACL,QAAQ,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ;gBAC7C,CAAA;aACF,CAAC;SACH;KACF;AACF,CAAA,CAAC;ACbK,MAAM,oBAAoB,GAAG,8NAAI,YAAS,CAAC,aAAa,CAAC;AAEzD,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC;IAC1C,IAAI,EAAE,aAAa;IAEnB,qBAAqB,GAAA;QACnB,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI;QAEvB,OAAO;YACL,8NAAI,SAAM,CAAC;gBACT,GAAG,EAAE,oBAAoB;gBACzB,KAAK,EAAE;oBACL,eAAe,EAAE;wBACf,KAAK,EAAE,CAAC,IAAI,EAAE,KAAY,KAAI;4BAC5B,MAAM,CAAC,SAAS,GAAG,IAAI;4BAEvB,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,EAAA,CAC9B,OAAO,CAAC,OAAO,EAAE;gCAAE,KAAK;4BAAA,CAAE,EAC1B,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC;4BAEjC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;4BAE1B,OAAO,KAAK;yBACb;wBACD,IAAI,EAAE,CAAC,IAAI,EAAE,KAAY,KAAI;4BAC3B,MAAM,CAAC,SAAS,GAAG,KAAK;4BAExB,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,EAAA,CAC9B,OAAO,CAAC,MAAM,EAAE;gCAAE,KAAK;4BAAA,CAAE,EACzB,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC;4BAEjC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;4BAE1B,OAAO,KAAK;yBACb;oBACF,CAAA;gBACF,CAAA;aACF,CAAC;SACH;KACF;AACF,CAAA,CAAC;ACnCK,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;IACrC,IAAI,EAAE,QAAQ;IAEd,oBAAoB,GAAA;QAClB,MAAM,eAAe,GAAG,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,GAAK;oBACzE,IAAM,QAAQ,CAAC,aAAa,EAAE;;oBAG9B,IAAM,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,KAAI;4BAChC,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE;4BAC7B,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,SAAS;4BACpC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,OAAO;4BAC/B,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,OAAO;4BAC5F,MAAM,iBAAiB,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS;4BAE/D,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,YAAY;4BAEpD,MAAM,SAAS,GAAG,AAAC,iBAAiB,IAAI,UAAU,CAAC,MAAM,CAAC,UAAU,KAAK,CAAC,GACtE,SAAS,KAAK,OAAO,CAAC,GAAA,GACtB,sOAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG;4BAEvC,IACE,CAAC,SACE,CAAC,MAAM,CAAC,IAAI,CAAC,WAAA,IACb,MAAM,CAAC,WAAW,CAAC,MAAA,IACnB,CAAC,aACA,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC,iFAAA;8BAC1D;gCACA,OAAO,KAAK;;4BAGd,OAAO,QAAQ,CAAC,UAAU,EAAE;wBAC9B,CAAC,CAAC;oBAEF,IAAM,QAAQ,CAAC,eAAe,EAAE;oBAChC,IAAM,QAAQ,CAAC,YAAY,EAAE;oBAC7B,IAAM,QAAQ,CAAC,kBAAkB,EAAE;iBACpC,CAAC;QAEF,MAAM,YAAY,GAAG,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,GAAK;oBACtE,IAAM,QAAQ,CAAC,eAAe,EAAE;oBAChC,IAAM,QAAQ,CAAC,iBAAiB,EAAE;oBAClC,IAAM,QAAQ,CAAC,WAAW,EAAE;oBAC5B,IAAM,QAAQ,CAAC,iBAAiB,EAAE;iBACnC,CAAC;QAEF,MAAM,WAAW,GAAG,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,GAAK;oBACrE,IAAM,QAAQ,CAAC,aAAa,EAAE;oBAC9B,IAAM,QAAQ,CAAC,mBAAmB,EAAE;oBACpC,IAAM,QAAQ,CAAC,cAAc,EAAE;oBAC/B,IAAM,QAAQ,CAAC,UAAU,EAAE;iBAC5B,CAAC;QAEF,MAAM,UAAU,GAAG;YACjB,KAAK,EAAE,WAAW;YAClB,WAAW,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE;YAClD,SAAS,EAAE,eAAe;YAC1B,eAAe,EAAE,eAAe;YAChC,iBAAiB,EAAE,eAAe;YAClC,MAAM,EAAE,YAAY;YACpB,YAAY,EAAE,YAAY;YAC1B,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE;SAChD;QAED,MAAM,QAAQ,GAAG;YACf,GAAG,UAAU;SACd;QAED,MAAM,SAAS,GAAG;YAChB,GAAG,UAAU;YACb,QAAQ,EAAE,eAAe;YACzB,eAAe,EAAE,eAAe;YAChC,QAAQ,EAAE,YAAY;YACtB,oBAAoB,EAAE,YAAY;YAClC,YAAY,EAAE,YAAY;YAC1B,OAAO,EAAE,YAAY;YACrB,QAAQ,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,oBAAoB,EAAE;YAC3D,QAAQ,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE;SAC1D;QAED,IAAI,KAAK,EAAE,IAAI,OAAO,EAAE,EAAE;YACxB,OAAO,SAAS;;QAGlB,OAAO,QAAQ;KAChB;IAED,qBAAqB,GAAA;QACnB,OAAO;;;;;;YAML,8NAAI,SAAM,CAAC;gBACT,GAAG,EAAE,8NAAI,YAAS,CAAC,eAAe,CAAC;gBACnC,iBAAiB,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,KAAI;oBACtD,IAAI,YAAY,CAAC,IAAI,EAAC,EAAE,GAAI,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE;wBACtD;;oBAGF,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,EAAC,WAAW,GAAI,WAAW,CAAC,UAAU,KACrE,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC;oBAEnC,MAAM,QAAQ,GAAG,YAAY,CAAC,IAAI,EAAC,WAAW,GAAI,WAAW,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;oBAE9F,IAAI,CAAC,UAAU,IAAI,QAAQ,EAAE;wBAC3B;;oBAGF,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,QAAQ,CAAC,SAAS;oBAC9C,MAAM,OAAO,6NAAG,YAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI;oBACpD,MAAM,MAAM,6NAAG,YAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;oBAC/C,MAAM,cAAc,GAAG,IAAI,KAAK,OAAO,IAAI,EAAE,KAAK,MAAM;oBAExD,IAAI,KAAK,IAAI,CAAC,cAAc,EAAE;wBAC5B;;oBAGF,MAAM,OAAO,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC;oBAEzC,IAAI,CAAC,OAAO,EAAE;wBACZ;;oBAGF,MAAM,EAAE,GAAG,QAAQ,CAAC,EAAE;oBACtB,MAAM,KAAK,GAAG,oBAAoB,CAAC;wBACjC,KAAK,EAAE,QAAQ;wBACf,WAAW,EAAE,EAAE;oBAChB,CAAA,CAAC;oBACF,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,cAAc,CAAC;wBACtC,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,KAAK;oBACN,CAAA,CAAC;oBAEF,QAAQ,CAAC,UAAU,EAAE;oBAErB,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE;wBACpB;;oBAGF,OAAO,EAAE;iBACV;aACF,CAAC;SACH;KACF;AACF,CAAA,CAAC;ACvJK,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC;IACpC,IAAI,EAAE,OAAO;IAEb,qBAAqB,GAAA;QAEnB,OAAO;YACL,8NAAI,SAAM,CAAC;gBACT,GAAG,EAAE,8NAAI,YAAS,CAAC,aAAa,CAAC;gBAEjC,KAAK,EAAE;oBACL,WAAW,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,KAAI;wBAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;4BACxB,MAAM,EAAE,IAAI,CAAC,MAAM;4BACnB,KAAK,EAAE,CAAC;4BACR,KAAK;wBACN,CAAA,CAAC;qBACH;gBACF,CAAA;aACF,CAAC;SACH;KACF;AACF,CAAA,CAAC;ACrBK,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC;IACvC,IAAI,EAAE,UAAU;IAEhB,qBAAqB,GAAA;QACnB,OAAO;YACL,8NAAI,SAAM,CAAC;gBACT,GAAG,EAAE,IAAI,sOAAS,CAAC,UAAU,CAAC;gBAC9B,KAAK,EAAE;oBACL,UAAU,EAAE,IAAoC,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG;4BAAE,QAAQ,EAAE,GAAG;wBAAA,CAAE,GAAG,CAAA,CAAE,CAAC;gBACjG,CAAA;aACF,CAAC;SACH;KACF;AACF,CAAA,CAAC;;;;;;;;;;;;;MCVW,OAAO,CAAA;IAOlB,IAAY,IAAI,GAAA;QACd,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;;IAG5B,WAAY,CAAA,GAAgB,EAAE,MAAc,EAAE,OAAO,GAAG,KAAK,EAAE,IAAA,GAAoB,IAAI,CAAA;QAO/E,IAAW,CAAA,WAAA,GAAgB,IAAI;QAUhC,IAAW,CAAA,WAAA,GAAkB,IAAI;QAhBtC,IAAI,CAAC,OAAO,GAAG,OAAO;QACtB,IAAI,CAAC,WAAW,GAAG,GAAG;QACtB,IAAI,CAAC,MAAM,GAAG,MAAM;QACpB,IAAI,CAAC,WAAW,GAAG,IAAI;;IAKzB,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;;IAGpD,IAAI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAmB;;IAKhE,IAAI,KAAK,GAAA;;QACP,OAAO,CAAA,EAAA,GAAA,IAAI,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,IAAI,CAAC,WAAW,CAAC,KAAK;;IAGnD,IAAI,GAAG,GAAA;QACL,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG;;IAG7B,IAAI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO;;IAG1B,IAAI,OAAO,CAAC,OAAgB,EAAA;QAC1B,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI;QACpB,IAAI,EAAE,GAAG,IAAI,CAAC,EAAE;QAEhB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE;gBAC3B,OAAO,CAAC,KAAK,CAAC,CAAA,+DAAA,EAAkE,IAAI,CAAC,IAAI,CAAA,IAAA,EAAO,IAAI,CAAC,GAAG,CAAA,CAAE,CAAC;gBAC3G;;YAGF,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC;YACpB,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC;;QAGlB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC;YAAE,IAAI;YAAE,EAAE;QAAA,CAAE,EAAE,OAAO,CAAC;;IAG7D,IAAI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;;IAGxB,IAAI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW;;IAG9B,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ;;IAG3B,IAAI,IAAI,GAAA;QACN,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO,IAAI,CAAC,GAAG;;QAGjB,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;;IAGvD,IAAI,KAAK,GAAA;QACP,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,EAAE,EAAE,IAAI,CAAC,EAAE;SACZ;;IAGH,IAAI,EAAE,GAAA;QACJ,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI;;QAG7B,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAA,CAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;;IAGlF,IAAI,MAAM,GAAA;QACR,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE;YACpB,OAAO,IAAI;;QAGb,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC;QACpE,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC;QAEpD,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;;IAGvC,IAAI,MAAM,GAAA;QACR,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,GAAA,CAAI,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAE3E,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;YAC7B,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;;QAGpD,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;;IAGvC,IAAI,KAAK,GAAA;QACP,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,GAAA,CAAI,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAEzE,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;YAC7B,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;;QAGlD,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;;IAGvC,IAAI,QAAQ,GAAA;QACV,MAAM,QAAQ,GAAc,EAAE;QAE9B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,MAAM,KAAI;YACzC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW;YACjD,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM;YAEjD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAA,CAAI,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;YAC7D,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC;YAEpD,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE;gBACxC;;YAGF,MAAM,YAAY,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;YAEnF,IAAI,OAAO,EAAE;gBACX,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC;;YAG3C,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;QAC/E,CAAC,CAAC;QAEF,OAAO,QAAQ;;IAGjB,IAAI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI;;IAGjC,IAAI,SAAS,GAAA;QACX,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;QAE9B,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI;;IAG9C,OAAO,CAAC,QAAgB,EAAE,UAAA,GAAqC,CAAA,CAAE,EAAA;QAC/D,IAAI,IAAI,GAAmB,IAAI;QAC/B,IAAI,WAAW,GAAG,IAAI,CAAC,MAAM;QAE7B,MAAO,WAAW,IAAI,CAAC,IAAI,CAAE;YAC3B,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;gBAC3C,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;oBACtC,MAAM,cAAc,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK;oBAC7C,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBAExC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,CAAE;wBACvD,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC;wBAE3B,IAAI,cAAc,CAAC,GAAG,CAAC,KAAK,UAAU,CAAC,GAAG,CAAC,EAAE;4BAC3C;;;uBAGC;oBACL,IAAI,GAAG,WAAW;;;YAItB,WAAW,GAAG,WAAW,CAAC,MAAM;;QAGlC,OAAO,IAAI;;IAGb,aAAa,CAAC,QAAgB,EAAE,UAAA,GAAqC,CAAA,CAAE,EAAA;QACrE,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;;IAGrE,gBAAgB,CAAC,QAAgB,EAAE,UAAA,GAAqC,CAAA,CAAE,EAAE,aAAa,GAAG,KAAK,EAAA;QAC/F,IAAI,KAAK,GAAc,EAAE;QAEzB,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YAChD,OAAO,KAAK;;QAEd,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAExC;;;SAGG,GACH,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAC,QAAQ,IAAG;;YAE/B,IAAI,aAAa,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrC;;YAGF,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACxC,MAAM,sBAAsB,GAAG,QAAQ,CAAC,KAAK,EAAC,GAAG,GAAI,UAAU,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAElG,IAAI,sBAAsB,EAAE;oBAC1B,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;;;;YAKxB,IAAI,aAAa,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrC;;YAGF,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;QACtF,CAAC,CAAC;QAEF,OAAO,KAAK;;IAGd,YAAY,CAAC,UAAkC,EAAA;QAC7C,MAAM,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK;QAEhC,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE;YACrC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;YAClB,GAAG,UAAU;QACd,CAAA,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;;AAEhC;ACvPM,MAAM,KAAK,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA0EnB;SC1Ec,cAAc,CAAC,KAAa,EAAE,KAAc,EAAE,MAAe,EAAA;IAC3E,MAAM,cAAc,GAAsB,QAAQ,CAAC,aAAa,CAAC,CAAA,uBAAA,EAA0B,MAAM,GAAG,CAAI,CAAA,EAAA,MAAM,EAAE,GAAG,EAAE,CAAA,CAAA,CAAG,CAAE;IAE1H,IAAI,cAAc,KAAK,IAAI,EAAE;QAC3B,OAAO,cAAc;;IAGvB,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;IAEjD,IAAI,KAAK,EAAE;QACT,SAAS,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC;;IAGxC,SAAS,CAAC,YAAY,CAAC,CAAA,iBAAA,EAAoB,MAAM,GAAG,CAAA,CAAA,EAAI,MAAM,CAAE,CAAA,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;IAC5E,SAAS,CAAC,SAAS,GAAG,KAAK;IAC3B,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC;IAE/D,OAAO,SAAS;AAClB;AC8BM,MAAO,MAAO,SAAQ,YAA0B,CAAA;IAgDpD,WAAA,CAAY,UAAkC,CAAA,CAAE,CAAA;QAC9C,KAAK,EAAE;QAtCF,IAAS,CAAA,SAAA,GAAG,KAAK;QAExB;;SAEG,GACI,IAAa,CAAA,aAAA,GAAG,KAAK;QAErB,IAAgB,CAAA,gBAAA,GAAwB,CAAA,CAAE;QAE1C,IAAA,CAAA,OAAO,GAAkB;YAC9B,OAAO,EAAE,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;YACtC,OAAO,EAAE,EAAE;YACX,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,SAAS;YACtB,UAAU,EAAE,EAAE;YACd,SAAS,EAAE,KAAK;YAChB,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,CAAA,CAAE;YACf,YAAY,EAAE,CAAA,CAAE;YAChB,oBAAoB,EAAE,CAAA,CAAE;YACxB,gBAAgB,EAAE,IAAI;YACtB,gBAAgB,EAAE,IAAI;YACtB,oBAAoB,EAAE,IAAI;YAC1B,kBAAkB,EAAE,KAAK;YACzB,cAAc,EAAE,IAAM,IAAI;YAC1B,QAAQ,EAAE,IAAM,IAAI;YACpB,QAAQ,EAAE,IAAM,IAAI;YACpB,iBAAiB,EAAE,IAAM,IAAI;YAC7B,aAAa,EAAE,IAAM,IAAI;YACzB,OAAO,EAAE,IAAM,IAAI;YACnB,MAAM,EAAE,IAAM,IAAI;YAClB,SAAS,EAAE,IAAM,IAAI;YACrB,cAAc,EAAE,CAAC,EAAE,KAAK,EAAE,KAAI;gBAAG,MAAM,KAAK,CAAA;YAAA,CAAE;YAC9C,OAAO,EAAE,IAAM,IAAI;YACnB,MAAM,EAAE,IAAM,IAAI;SACnB;QAgUM,IAAsB,CAAA,sBAAA,GAAG,KAAK;QAE7B,IAAmB,CAAA,mBAAA,GAAuB,IAAI;QA9TpD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACxB,IAAI,CAAC,sBAAsB,EAAE;QAC7B,IAAI,CAAC,oBAAoB,EAAE;QAC3B,IAAI,CAAC,YAAY,EAAE;QACnB,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;QACpD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE,CAAC;QAC3C,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;QACpD,IAAI,CAAC,UAAU,EAAE;QACjB,IAAI,CAAC,SAAS,EAAE;QAChB,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QACxC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QACxC,IAAI,CAAC,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC;QAC1D,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;QAClD,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QACtC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACpC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QAC1C,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAK,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACtF,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,GAAK,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAE1E,MAAM,CAAC,UAAU,CAAC,MAAK;YACrB,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB;;YAGF,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAAE,MAAM,EAAE,IAAI;YAAA,CAAE,CAAC;YACrC,IAAI,CAAC,aAAa,GAAG,IAAI;SAC1B,EAAE,CAAC,CAAC;;IAGP;;KAEG,GACH,IAAW,OAAO,GAAA;QAChB,OAAO,IAAI,CAAC,gBAAgB;;IAG9B;;KAEG,GACH,IAAW,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ;;IAGrC;;KAEG,GACI,KAAK,GAAA;QACV,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;;IAGpC;;KAEG,GACI,GAAG,GAAA;QACR,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE;;IAGlC;;KAEG,GACK,SAAS,GAAA;QACf,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,QAAQ,EAAE;YACtC,IAAI,CAAC,GAAG,GAAG,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;;;IAI9D;;;;KAIG,GACI,UAAU,CAAC,UAAkC,CAAA,CAAE,EAAA;QACpD,IAAI,CAAC,OAAO,GAAG;YACb,GAAG,IAAI,CAAC,OAAO;YACf,GAAG,OAAO;SACX;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,WAAW,EAAE;YACjD;;QAGF,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;;QAG9C,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;;IAGnC;;KAEG,GACI,WAAW,CAAC,QAAiB,EAAE,UAAU,GAAG,IAAI,EAAA;QACrD,IAAI,CAAC,UAAU,CAAC;YAAE,QAAQ;QAAA,CAAE,CAAC;QAE7B,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAAE,MAAM,EAAE,IAAI;gBAAE,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;YAAA,CAAE,CAAC;;;IAIrE;;KAEG,GACH,IAAW,UAAU,GAAA;;;;QAInB,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ;;IAGjE;;KAEG,GACH,IAAW,KAAK,GAAA;QACd,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;;IAGxB;;;;;;KAMG,GACI,cAAc,CACnB,MAAc,EACd,aAAkE,EAAA;QAElE,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa,IACpC,aAAa,CAAC,MAAM,EAAE,CAAC;eAAG,IAAI,CAAC,KAAK,CAAC,OAAO;SAAC,IAC7C,CAAC;eAAG,IAAI,CAAC,KAAK,CAAC,OAAO;YAAE,MAAM;SAAC;QAEnC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;YAAE,OAAO;QAAA,CAAE,CAAC;QAEjD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;QAE5B,OAAO,KAAK;;IAGd;;;;;KAKG,GACI,gBAAgB,CAAC,uBAAoE,EAAA;QAC1F,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,SAAS;;QAGlB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO;QACtC,IAAI,OAAO,GAAG,WAAW;QAExB,EAA6B,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC,OAAO,CAAC,eAAe,IAAG;;YAEvF,MAAM,IAAI,GAAG,OAAO,eAAe,KAAK,QAAQ,GAAG,CAAG,EAAA,eAAe,CAAA,CAAA,CAAG,GAAG,eAAe,CAAC,GAAG;;YAG9F,OAAO,GAAG,WAAW,CAAC,MAAM,EAAC,MAAM,GAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACtE,CAAC,CAAC;QAEF,IAAI,WAAW,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE;;YAEzC,OAAO,SAAS;;QAGlB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;YACnC,OAAO;QACR,CAAA,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;QAE5B,OAAO,KAAK;;IAGd;;KAEG,GACK,sBAAsB,GAAA;;QAE5B,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG;YACzD,QAAQ;YACR,uBAAuB,CAAC,SAAS,CAAC;gBAChC,cAAc,EAAE,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,oBAAoB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,uBAAuB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,cAAc;aAC3F,CAAC;YACF,QAAQ;YACR,WAAW;YACX,MAAM;YACN,QAAQ;YACR,IAAI;YACJ,KAAK;SACN,CAAC,MAAM,EAAC,GAAG,IAAG;YACb,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,KAAK,QAAQ,EAAE;gBACzD,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAsD,CAAC,KAAK,KAAK;;YAEhH,OAAO,IAAI;QACb,CAAC,CAAC,GAAG,EAAE;QACP,MAAM,aAAa,GAAG,CAAC;eAAG,cAAc,EAAE;eAAG,IAAI,CAAC,OAAO,CAAC,UAAU;SAAC,CAAC,MAAM,EAAC,SAAS,IAAG;YACvF,OAAO;gBAAC,WAAW;gBAAE,MAAM;gBAAE,MAAM;aAAC,CAAC,QAAQ,CAAC,SAAS,KAAA,QAAT,SAAS,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAT,SAAS,CAAE,IAAI,CAAC;QAChE,CAAC,CAAC;QAEF,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC;;IAGnE;;KAEG,GACK,oBAAoB,GAAA;QAC1B,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC;YACvC,MAAM,EAAE,IAAI;QACb,CAAA,CAAC;;IAGJ;;KAEG,GACK,YAAY,GAAA;QAClB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM;;IAG5C;;KAEG,GACK,UAAU,GAAA;;QAChB,IAAI,GAAoB;QAExB,IAAI;YACF,GAAG,GAAG,cAAc,CAClB,IAAI,CAAC,OAAO,CAAC,OAAO,EACpB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,OAAO,CAAC,YAAY,EACzB;gBAAE,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB;YAAA,CAAE,CAC3D;UACD,OAAO,CAAC,EAAE;YACV,IAAI,CAAA,CAAE,CAAC,YAAY,KAAK,CAAC,IAAI,CAAC;gBAAC,sCAAsC;gBAAE,sCAAsC;aAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE;;gBAElI,MAAM,CAAC;;YAET,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACxB,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,CAAU;gBACjB,oBAAoB,EAAE,MAAK;oBACzB,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;wBAC9B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,GAAG,IAAI;;;oBAG9C,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,EAAC,SAAS,GAAI,SAAS,CAAC,IAAI,KAAK,eAAe,CAAC;;oBAGzG,IAAI,CAAC,sBAAsB,EAAE;iBAC9B;YACF,CAAA,CAAC;;YAGF,GAAG,GAAG,cAAc,CAClB,IAAI,CAAC,OAAO,CAAC,OAAO,EACpB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,OAAO,CAAC,YAAY,EACzB;gBAAE,qBAAqB,EAAE,KAAK;YAAA,CAAE,CACjC;;QAEH,MAAM,SAAS,GAAG,oBAAoB,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QAEnE,IAAI,CAAC,IAAI,GAAG,6NAAI,aAAU,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YAC/C,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW;YAC3B,UAAU,EAAE;;gBAEV,IAAI,EAAE,SAAS;gBACf,GAAG,CAAA,KAAA,IAAI,CAAC,OAAO,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,UAAU;YACxC,CAAA;YACD,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC;YACxD,KAAK,4NAAE,cAAW,CAAC,MAAM,CAAC;gBACxB,GAAG;gBACH,SAAS,EAAE,SAAS,IAAI,SAAS;aAClC,CAAC;QACH,CAAA,CAAC;;;QAIF,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;YACtC,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO;QACvC,CAAA,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;QAE/B,IAAI,CAAC,eAAe,EAAE;QACtB,IAAI,CAAC,YAAY,EAAE;;;;QAKnB,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAA8B;QAEpD,GAAG,CAAC,MAAM,GAAG,IAAI;;IAGnB;;KAEG,GACI,eAAe,GAAA;QACpB,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACzB;;QAGF,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;YACjB,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,SAAS;QAC3C,CAAA,CAAC;;IAGJ;;KAEG,GACI,YAAY,GAAA;QACjB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,CAAA,OAAA,EAAU,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE;;IAOxD,kBAAkB,CAAC,EAAc,EAAA;QACtC,IAAI,CAAC,sBAAsB,GAAG,IAAI;QAClC,EAAE,EAAE;QACJ,IAAI,CAAC,sBAAsB,GAAG,KAAK;QAEnC,MAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB;QAEnC,IAAI,CAAC,mBAAmB,GAAG,IAAI;QAE/B,OAAO,EAAE;;IAGX;;;;KAIG,GACK,mBAAmB,CAAC,WAAwB,EAAA;;;QAGlD,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACzB;;QAGF,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC7B,IAAI,CAAC,mBAAmB,GAAG,WAAW;gBAEtC;;YAGF,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;gBAAA,IAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,IAAI,CAAC,mBAAmB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI,CAAC,IAAI,CAAC,CAAA;YAAA,CAAA,CAAC;YAEvE;;QAGF,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC;QAC3C,MAAM,mBAAmB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC;QAErE,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,MAAM,EAAE,IAAI;YACZ,WAAW;YACX,SAAS,EAAE,KAAK;QACjB,CAAA,CAAC;QACF,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,MAAM,EAAE,IAAI;YACZ,WAAW;QACZ,CAAA,CAAC;QAEF,IAAI,mBAAmB,EAAE;YACvB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBAC3B,MAAM,EAAE,IAAI;gBACZ,WAAW;YACZ,CAAA,CAAC;;QAGJ,MAAM,KAAK,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC;QAC1C,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC;QAExC,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACjB,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,WAAW;YACZ,CAAA,CAAC;;QAGJ,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAChB,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,WAAW;YACZ,CAAA,CAAC;;QAGJ,IAAI,CAAC,WAAW,CAAC,UAAU,IAAI,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE;YACnE;;QAGF,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,MAAM,EAAE,IAAI;YACZ,WAAW;QACZ,CAAA,CAAC;;IAGJ;;KAEG,GACI,aAAa,CAAC,UAAwC,EAAA;QAC3D,OAAO,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC;;IAWvC,QAAQ,CAAC,gBAAwB,EAAE,qBAA0B,EAAA;QAClE,MAAM,IAAI,GAAG,OAAO,gBAAgB,KAAK,QAAQ,GAAG,gBAAgB,GAAG,IAAI;QAE3E,MAAM,UAAU,GAAG,OAAO,gBAAgB,KAAK,QAAQ,GAAG,qBAAqB,GAAG,gBAAgB;QAElG,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC;;IAG/C;;KAEG,GACI,OAAO,GAAA;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE;;IAGhC;;KAEG,GACI,OAAO,GAAA;QACZ,OAAO,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC;;IAGjE;;KAEG,GACI,OAAO,CAAC,OAGd,EAAA;QACC,MAAM,EAAE,cAAc,GAAG,MAAM,EAAE,eAAe,GAAG,CAAA,CAAE,EAAE,GAAG,OAAO,IAAI,CAAA,CAAE;QAEvE,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;YAC7B,cAAc;YACd,eAAe,EAAE;gBACf,GAAG,4BAA4B,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC5C,GAAG,eAAe;YACnB,CAAA;QACF,CAAA,CAAC;;IAGJ;;KAEG,GACH,IAAW,OAAO,GAAA;QAChB,OAAO,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;;IAGpC;;;;KAIG,GACI,iBAAiB,GAAA;QACtB,OAAO,CAAC,IAAI,CACV,6HAA6H,CAC9H;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC;;IAGxC;;KAEG,GACI,OAAO,GAAA;QACZ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;QAEpB,IAAI,IAAI,CAAC,IAAI,EAAE;;;YAGb,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAA8B;YAEpD,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE;gBACrB,OAAO,GAAG,CAAC,MAAM;;YAEnB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;;QAGrB,IAAI,CAAC,kBAAkB,EAAE;;IAG3B;;KAEG,GACH,IAAW,WAAW,GAAA;;;QAEpB,OAAO,CAAA,CAAC,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,CAAA;;IAGrB,KAAK,CAAC,QAAgB,EAAE,UAAmC,EAAA;;QAChE,OAAO,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,KAAI,IAAI;;IAGxD,MAAM,CAAC,QAAgB,EAAE,UAAmC,EAAA;;QACjE,OAAO,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,gBAAgB,CAAC,QAAQ,EAAE,UAAU,CAAC,KAAI,IAAI;;IAG3D,IAAI,CAAC,GAAW,EAAA;QACrB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC;QAExC,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;;IAGhC,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;;AAEtB;ACzmBD;;;;CAIG,GACG,SAAU,aAAa,CAAC,MAQ7B,EAAA;IACC,OAAO,IAAI,SAAS,CAAC;QACnB,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAI;YACnC,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,CAAC;YAEvE,IAAI,UAAU,KAAK,KAAK,IAAI,UAAU,KAAK,IAAI,EAAE;gBAC/C,OAAO,IAAI;;YAGb,MAAM,EAAE,EAAE,EAAE,GAAG,KAAK;YACpB,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;YAC5C,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC;YAE1B,IAAI,YAAY,EAAE;gBAChB,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;gBAC1C,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC;gBAC9D,MAAM,OAAO,GAAG,SAAS,GAAG,YAAY,CAAC,MAAM;gBAE/C,MAAM,aAAa,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,EAClE,MAAM,EAAC,IAAI,IAAG;;oBAEb,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAsB;oBAEtD,OAAO,QAAQ,CAAC,IAAI,EAAC,IAAI,GAAI,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC/E,CAAC,EACA,MAAM,EAAC,IAAI,GAAI,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC;gBAEtC,IAAI,aAAa,CAAC,MAAM,EAAE;oBACxB,OAAO,IAAI;;gBAGb,IAAI,OAAO,GAAG,KAAK,CAAC,EAAE,EAAE;oBACtB,EAAE,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC;;gBAG9B,IAAI,SAAS,GAAG,KAAK,CAAC,IAAI,EAAE;oBAC1B,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,WAAW,EAAE,SAAS,CAAC;;gBAGhD,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,GAAG,WAAW,GAAG,YAAY,CAAC,MAAM;gBAE9D,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAA,CAAE,CAAC,CAAC;gBAEnF,EAAE,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC;;SAEnC;IACF,CAAA,CAAC;AACJ;AC9DA;;;;CAIG,GACG,SAAU,aAAa,CAAC,MAoB7B,EAAA;IACC,OAAO,IAAI,SAAS,CAAC;QACnB,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAI;YACnC,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,CAAA,CAAE;YAC7E,MAAM,EAAE,EAAE,EAAE,GAAG,KAAK;YACpB,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI;YACxB,IAAI,GAAG,GAAG,KAAK,CAAC,EAAE;YAElB,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YAE9C,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;gBACZ,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC7C,IAAI,UAAU,GAAG,KAAK,GAAG,MAAM;gBAE/B,IAAI,UAAU,GAAG,GAAG,EAAE;oBACpB,UAAU,GAAG,GAAG;uBACX;oBACL,GAAG,GAAG,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM;;;gBAIpC,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;gBAE9C,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;;gBAGpD,EAAE,CAAC,WAAW,CAAC,UAAU,EAAE,GAAG,EAAE,OAAO,CAAC;mBACnC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;gBACnB,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC;gBAE/D,EAAE,CAAC,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAC9D,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EACrB,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CACpB;;YAGH,EAAE,CAAC,cAAc,EAAE;SACpB;IACF,CAAA,CAAC;AACJ;ACjEA;;;;;;CAMG,GACG,SAAU,sBAAsB,CAAC,MAQtC,EAAA;IACC,OAAO,IAAI,SAAS,CAAC;QACnB,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAI;YACnC,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;YAC5C,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,CAAA,CAAE;YAE7E,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;gBACzF,OAAO,IAAI;;YAGb,KAAK,CAAC,EAAA,CACH,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAC3B,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC;SACjE;IACF,CAAA,CAAC;AACJ;ACnCA;;;;CAIG,GACG,SAAU,aAAa,CAAC,MAG7B,EAAA;IACC,OAAO,IAAI,SAAS,CAAC;QACnB,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAI;YACnC,IAAI,MAAM,GAAG,MAAM,CAAC,OAAO;YAC3B,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI;YACtB,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE;YAEpB,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;gBACZ,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAE7C,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;gBAClD,KAAK,IAAI,MAAM;gBAEf,MAAM,MAAM,GAAG,KAAK,GAAG,GAAG;gBAE1B,IAAI,MAAM,GAAG,CAAC,EAAE;oBACd,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,EAAE,MAAM,CAAC,GAAG,MAAM;oBACzD,KAAK,GAAG,GAAG;;;YAIf,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC;SACxC;IACF,CAAA,CAAC;AACJ;AC3BA;;;;;;;;;;;;;;CAcG,GACG,SAAU,iBAAiB,CAAC,MAajC,EAAA;IACC,OAAO,IAAI,SAAS,CAAC;QACnB,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,OAAO,EAAE,CAAC,EACR,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAC3B,KAAI;YACH,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,CAAA,CAAE;YAC7E,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;YAChD,MAAM,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;YACzC,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,EAAE;YACtC,MAAM,QAAQ,GAAG,UAAU,2OAAI,eAAA,AAAY,EAAC,UAAU,EAAE,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC;YAEhF,IAAI,CAAC,QAAQ,EAAE;gBACb,OAAO,IAAI;;YAGb,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC;YAE7B,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,MAAM,EAAE;gBACrC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,KAAK;gBACxC,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB;gBAC1D,MAAM,KAAK,GAAG,WAAW,IAAK,SAAS,CAAC,GAAG,CAAC,YAAY,IAAI,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;gBAEpF,IAAI,KAAK,EAAE;oBACT,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,EAAC,IAAI,GAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAEpF,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC;;;YAGjC,IAAI,MAAM,CAAC,cAAc,EAAE;yGAEzB,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,aAAa,GAAG,UAAU,GAAG,UAAU;gBAElH,KAAK,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,GAAG,EAAE;;YAGtD,MAAM,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,UAAU;YAExD,IACE,UACG,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAA,2OACvB,UAAA,AAAO,EAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,KAC9B,CAAC,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,EACjE;gBACA,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;;SAE1B;IACF,CAAA,CAAC;AACJ;ACipBA;;;CAGG,SACU,IAAI,CAAA;IAkBf,WAAA,CAAY,SAAgD,CAAA,CAAE,CAAA;QAjB9D,IAAI,CAAA,IAAA,GAAG,MAAM;QAEb,IAAI,CAAA,IAAA,GAAG,MAAM;QAEb,IAAM,CAAA,MAAA,GAAgB,IAAI;QAE1B,IAAK,CAAA,KAAA,GAAgB,IAAI;QAMzB,IAAA,CAAA,MAAM,GAAe;YACnB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,cAAc,EAAE,CAAA,CAAE;SACnB;QAGC,IAAI,CAAC,MAAM,GAAG;YACZ,GAAG,IAAI,CAAC,MAAM;YACd,GAAG,MAAM;SACV;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;QAE5B,IAAI,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1E,OAAO,CAAC,IAAI,CACV,CAAA,sHAAA,EAAyH,IAAI,CAAC,IAAI,CAAI,EAAA,CAAA,CACvI;;;QAIH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc;QAEzC,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YAC1B,IAAI,CAAC,OAAO,GAAG,YAAY,CACzB,iBAAiB,CAA0B,IAAI,EAAE,YAAY,EAAE;gBAC7D,IAAI,EAAE,IAAI,CAAC,IAAI;YAChB,CAAA,CAAC,CACH;;QAGH,IAAI,CAAC,OAAO,GAAG,YAAY,CACzB,iBAAiB,CAA0B,IAAI,EAAE,YAAY,EAAE;YAC7D,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC,CACH,IAAI,CAAA,CAAE;;IAGT,OAAO,MAAM,CAAmB,MAAA,GAAoC,CAAA,CAAE,EAAA;QACpE,OAAO,IAAI,IAAI,CAAO,MAAM,CAAC;;IAG/B,SAAS,CAAC,UAA4B,CAAA,CAAE,EAAA;;;QAGtC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAmB;YAC9C,GAAG,IAAI,CAAC,MAAM;YACd,UAAU,EAAE,MAAK;gBACf,OAAO,SAAS,CAAC,IAAI,CAAC,OAA8B,EAAE,OAAO,CAAY;aAC1E;QACF,CAAA,CAAC;;QAGF,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;;QAE1B,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;QAE9B,OAAO,SAAS;;IAGlB,MAAM,CACJ,iBAAwE,CAAA,CAAE,EAAA;QAE1E,MAAM,SAAS,GAAG,IAAI,IAAI,CAAmC,cAAc,CAAC;QAE5E,SAAS,CAAC,MAAM,GAAG,IAAI;QAEvB,IAAI,CAAC,KAAK,GAAG,SAAS;QAEtB,SAAS,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI;QAElF,IAAI,cAAc,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1F,OAAO,CAAC,IAAI,CACV,CAAA,sHAAA,EAAyH,SAAS,CAAC,IAAI,CAAI,EAAA,CAAA,CAC5I;;QAGH,SAAS,CAAC,OAAO,GAAG,YAAY,CAC9B,iBAAiB,CAA0B,SAAS,EAAE,YAAY,EAAE;YAClE,IAAI,EAAE,SAAS,CAAC,IAAI;QACrB,CAAA,CAAC,CACH;QAED,SAAS,CAAC,OAAO,GAAG,YAAY,CAC9B,iBAAiB,CAA0B,SAAS,EAAE,YAAY,EAAE;YAClE,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,OAAO,EAAE,SAAS,CAAC,OAAO;QAC3B,CAAA,CAAC,CACH;QAED,OAAO,SAAS;;AAEnB;AC10BD;;;CAGG,SACU,QAAQ,CAAA;IA2BnB,WAAA,CAAY,SAAoB,EAAE,KAA4B,EAAE,OAA0B,CAAA;QAF1F,IAAU,CAAA,UAAA,GAAG,KAAK;QAGhB,IAAI,CAAC,SAAS,GAAG,SAAS;QAC1B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAoB;QACxC,IAAI,CAAC,OAAO,GAAG;YACb,SAAS,EAAE,IAAI;YACf,cAAc,EAAE,IAAI;YACpB,GAAG,OAAO;SACA;QACZ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS;QAChC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;QACtB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,WAAmC;QAC5D,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,gBAAgB;QAC9C,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;QACtB,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,cAAc;QAC1C,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM;QAC1B,IAAI,CAAC,KAAK,EAAE;;IAGd,KAAK,GAAA;;QAEH;;IAGF,IAAI,GAAG,GAAA;QACL,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAkB;;IAG5C,IAAI,UAAU,GAAA;QACZ,OAAO,IAAI;;IAGb,WAAW,CAAC,KAAgB,EAAA;;QAC1B,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM;QAC5B,MAAM,MAAM,GAAG,KAAK,CAAC,MAAqB;;;QAI1C,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,KAAK,IACnC,CAAA,KAAA,MAAM,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,CAAC,oBAAoB,IAClD,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC;QAExC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAA,CAAI,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAI,CAAC,UAAU,EAAE;YACjE;;QAGF,IAAI,CAAC,GAAG,CAAC;QACT,IAAI,CAAC,GAAG,CAAC;;QAGT,IAAI,IAAI,CAAC,GAAG,KAAK,UAAU,EAAE;YAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE;YAC/C,MAAM,SAAS,GAAG,UAAU,CAAC,qBAAqB,EAAE;;YAGpD,MAAM,OAAO,GAAG,CAAA,EAAA,GAAA,KAAK,CAAC,OAAO,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,CAAA,EAAA,GAAC,KAAa,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO;YACpE,MAAM,OAAO,GAAG,CAAA,EAAA,GAAA,KAAK,CAAC,OAAO,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,CAAA,EAAA,GAAC,KAAa,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO;YAEpE,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,OAAO;YACpC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,OAAO;;QAGtC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAgB;QAE1D,CAAA,EAAA,GAAA,KAAK,CAAC,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,YAAY,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;QAElD,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE;QAEzB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B;;;;QAIF,MAAM,SAAS,6NAAG,gBAAa,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC;QAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC;QAEzD,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;;IAG5B,SAAS,CAAC,KAAY,EAAA;;QACpB,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACb,OAAO,KAAK;;QAGd,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,UAAU,EAAE;YAChD,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;gBAAE,KAAK;YAAA,CAAE,CAAC;;QAG1C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAqB;QAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAA,CAAC,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,QAAQ,CAAC,MAAM,CAAC,CAAA;;QAGnF,IAAI,CAAC,WAAW,EAAE;YAChB,OAAO,KAAK;;QAGd,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QACjD,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,KAAK,MAAM;QACzC,MAAM,OAAO,GAAG;YAAC,OAAO;YAAE,QAAQ;YAAE,QAAQ;YAAE,UAAU;SAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,iBAAiB;;QAG9G,IAAI,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,EAAE;YAC3C,OAAO,IAAI;;QAGb,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,MAAM;QAClC,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI;QAC3B,MAAM,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS;QACnD,MAAM,YAAY,6NAAG,gBAAa,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1D,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,KAAK,MAAM;QACzC,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,KAAK,OAAO;QAC3C,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,KAAK,KAAK;QACvC,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,KAAK,WAAW;;;;QAK/C,IAAI,CAAC,WAAW,IAAI,YAAY,IAAI,WAAW,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,GAAG,EAAE;YAC5E,KAAK,CAAC,cAAc,EAAE;;QAGxB,IAAI,WAAW,IAAI,WAAW,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,GAAG,EAAE;YAC1E,KAAK,CAAC,cAAc,EAAE;YACtB,OAAO,KAAK;;;QAId,IAAI,WAAW,IAAI,UAAU,IAAI,CAAC,UAAU,IAAI,YAAY,EAAE;YAC5D,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC;YACvD,MAAM,iBAAiB,GAAG,UAAU,IAAA,CAAK,IAAI,CAAC,GAAG,KAAK,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAElG,IAAI,iBAAiB,EAAE;gBACrB,IAAI,CAAC,UAAU,GAAG,IAAI;gBAEtB,QAAQ,CAAC,gBAAgB,CACvB,SAAS,EACT,MAAK;oBACH,IAAI,CAAC,UAAU,GAAG,KAAK;gBACzB,CAAC,EACD;oBAAE,IAAI,EAAE,IAAI;gBAAA,CAAE,CACf;gBAED,QAAQ,CAAC,gBAAgB,CACvB,MAAM,EACN,MAAK;oBACH,IAAI,CAAC,UAAU,GAAG,KAAK;gBACzB,CAAC,EACD;oBAAE,IAAI,EAAE,IAAI;gBAAA,CAAE,CACf;gBAED,QAAQ,CAAC,gBAAgB,CACvB,SAAS,EACT,MAAK;oBACH,IAAI,CAAC,UAAU,GAAG,KAAK;gBACzB,CAAC,EACD;oBAAE,IAAI,EAAE,IAAI;gBAAA,CAAE,CACf;;;;QAKL,IACE,cACG,eACA,eACA,gBACA,cACC,YAAY,IAAI,YAAY,CAAC,CACjC;YACA,OAAO,KAAK;;QAGd,OAAO,IAAI;;IAGb;;;;KAIG,GACH,cAAc,CAAC,QAA4B,EAAA;QACzC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACjC,OAAO,IAAI;;QAGb,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,UAAU,EAAE;YACrD,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;gBAAE,QAAQ;YAAA,CAAE,CAAC;;;;QAKlD,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACxC,OAAO,IAAI;;;QAIb,IAAI,QAAQ,CAAC,IAAI,KAAK,WAAW,EAAE;YACjC,OAAO,KAAK;;;;;;;QAQd,IACE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,KAC9B,QAAQ,CAAC,IAAI,KAAK,eAClB,CAAC,KAAK,EAAE,IAAI,SAAS,EAAE,KACvB,IAAI,CAAC,MAAM,CAAC,SAAS,EACxB;YACA,MAAM,YAAY,GAAG;mBAChB,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;mBAC/B,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;aACpB;;;YAIlB,IAAI,YAAY,CAAC,KAAK,EAAC,IAAI,GAAI,IAAI,CAAC,iBAAiB,CAAC,EAAE;gBACtD,OAAO,KAAK;;;;;QAMhB,IAAI,IAAI,CAAC,UAAU,KAAK,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,YAAY,EAAE;YACzE,OAAO,IAAI;;;QAIb,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC7C,OAAO,KAAK;;QAGd,OAAO,IAAI;;IAGb;;KAEG,GACH,gBAAgB,CAAC,UAA+B,EAAA;QAC9C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,KAAI;YACtC,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE;YAEzB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;gBAC3B,OAAO,KAAK;;YAGd,EAAE,CAAC,aAAa,CAAC,GAAG,EAAE,SAAS,EAAE;gBAC/B,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;gBAClB,GAAG,UAAU;YACd,CAAA,CAAC;YAEF,OAAO,IAAI;QACb,CAAC,CAAC;;IAGJ;;KAEG,GACH,UAAU,GAAA;QACR,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE;QAE1B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B;;QAEF,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;QAEpC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC;YAAE,IAAI;YAAE,EAAE;QAAA,CAAE,CAAC;;AAEjD;AC7SD;;;;CAIG,GACG,SAAU,aAAa,CAAC,MAQ7B,EAAA;IACC,OAAO,IAAI,SAAS,CAAC;QACnB,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,OAAO,EAAE,CAAC,EACR,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAChC,KAAI;YACH,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,CAAC;YAEnF,IAAI,UAAU,KAAK,KAAK,IAAI,UAAU,KAAK,IAAI,EAAE;gBAC/C,OAAO,IAAI;;YAGb,MAAM,EAAE,EAAE,EAAE,GAAG,KAAK;YACpB,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;YAC5C,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC;YAC1B,IAAI,OAAO,GAAG,KAAK,CAAC,EAAE;YAEtB,IAAI,YAAY,EAAE;gBAChB,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;gBAC1C,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC;gBAC9D,MAAM,OAAO,GAAG,SAAS,GAAG,YAAY,CAAC,MAAM;gBAE/C,MAAM,aAAa,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,EAClE,MAAM,EAAC,IAAI,IAAG;;oBAEb,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAsB;oBAEtD,OAAO,QAAQ,CAAC,IAAI,EAAC,IAAI,GAAI,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC/E,CAAC,EACA,MAAM,EAAC,IAAI,GAAI,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC;gBAEtC,IAAI,aAAa,CAAC,MAAM,EAAE;oBACxB,OAAO,IAAI;;gBAGb,IAAI,OAAO,GAAG,KAAK,CAAC,EAAE,EAAE;oBACtB,EAAE,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC;;gBAG9B,IAAI,SAAS,GAAG,KAAK,CAAC,IAAI,EAAE;oBAC1B,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,WAAW,EAAE,SAAS,CAAC;;gBAGhD,OAAO,GAAG,KAAK,CAAC,IAAI,GAAG,WAAW,GAAG,YAAY,CAAC,MAAM;gBAExD,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAA,CAAE,CAAC,CAAC;gBAEnF,EAAE,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC;;SAEnC;IACF,CAAA,CAAC;AACJ;ACvEA,8CAAA;AACM,SAAU,cAAc,CAAC,MAAc,EAAA;IAC3C,OAAO,MAAM,CAAC,OAAO,CAAC,uBAAuB,EAAE,MAAM,CAAC;AACxD;ACHM,SAAU,QAAQ,CAAC,KAAU,EAAA;IACjC,OAAO,OAAO,KAAK,KAAK,QAAQ;AAClC;ACIA;;;;CAIG,GACG,SAAU,aAAa,CAAC,MAa7B,EAAA;IACC,OAAO,IAAI,SAAS,CAAC;QACnB,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,OAAO,EAAC,EACN,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAChC,EAAA;YACC,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,CAAC;YACnF,MAAM,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC;YAEtE,IAAI,UAAU,KAAK,KAAK,IAAI,UAAU,KAAK,IAAI,EAAE;gBAC/C,OAAO,IAAI;;YAGb,MAAM,IAAI,GAAG;gBAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI;gBAAE,KAAK,EAAE,UAAU;YAAA,CAAiB;YAEzE,IAAI,OAAO,EAAE;gBACX,IAAI,CAAC,OAAO,GAAG,OAAO;;YAGxB,IAAI,KAAK,CAAC,KAAK,EAAE;gBACf,KAAK,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;;SAE/D;IACF,CAAA,CAAC;AACJ;AC9CA;;;;CAIG,GACG,SAAU,aAAa,CAAC,MAG7B,EAAA;IACC,OAAO,IAAI,SAAS,CAAC;QACnB,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAI;YACnC,IAAI,MAAM,GAAG,MAAM,CAAC,OAAO;YAC3B,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI;YACtB,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE;YAEpB,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;gBACZ,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAE7C,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;gBAClD,KAAK,IAAI,MAAM;gBAEf,MAAM,MAAM,GAAG,KAAK,GAAG,GAAG;gBAE1B,IAAI,MAAM,GAAG,CAAC,EAAE;oBACd,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,EAAE,MAAM,CAAC,GAAG,MAAM;oBACzD,KAAK,GAAG,GAAG;;;YAIf,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC;SACxC;IACF,CAAA,CAAC;AACJ;MC5Ba,OAAO,CAAA;IAKlB,WAAA,CAAY,WAAwB,CAAA;QAClC,IAAI,CAAC,WAAW,GAAG,WAAW;QAC9B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM;;IAGlD,GAAG,CAAC,QAAgB,EAAA;QAClB,IAAI,OAAO,GAAG,KAAK;QAEnB,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,KAAA,CACrC,KAAK,CAAC,IAAI,CAAC,WAAW,EACtB,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI,KAAI;YAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC;YAEtD,IAAI,SAAS,CAAC,OAAO,EAAE;gBACrB,OAAO,GAAG,IAAI;;YAGhB,OAAO,SAAS,CAAC,GAAG;SACrB,EAAE,QAAQ,CAAC;QAEd,OAAO;YACL,QAAQ,EAAE,cAAc;YACxB,OAAO;SACR;;AAEJ", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155], "debugId": null}}]}