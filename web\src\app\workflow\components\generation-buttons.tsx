"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { FileText, Image, Video, Subtitles, Loader2, Play } from "lucide-react";
import { useVideoAPI } from "../hooks/use-video-api";
import { ProgressBar } from "./progress-bar";

interface GenerationButtonsProps {
  creativeId: number;
  onRefresh: () => void;
}

export function GenerationButtons({ creativeId, onRefresh }: GenerationButtonsProps) {
  const t = useTranslations("workflow.detail.generation");
  const [loading, setLoading] = useState<string | null>(null);
  const [taskId, setTaskId] = useState<string | null>(null);

  // 确认对话框状态
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    title: string;
    description: string;
    onConfirm: () => void;
  }>({
    open: false,
    title: "",
    description: "",
    onConfirm: () => {}
  });

  const { generateScenes, generateImages, generateVideos, generateSubtitles } = useVideoAPI();

  // 确认对话框辅助函数
  const showConfirmDialog = (title: string, description: string, onConfirm: () => void) => {
    setConfirmDialog({
      open: true,
      title,
      description,
      onConfirm
    });
  };

  const closeConfirmDialog = () => {
    setConfirmDialog(prev => ({ ...prev, open: false }));
  };

  // 处理生成操作
  const handleGenerate = async (type: string, generateFn: () => Promise<any>) => {
    try {
      setLoading(type);
      setTaskId(null); // 重置任务ID

      const result = await generateFn();
      console.log(`${type} generation completed:`, result);

      // 如果返回了任务ID，设置它以显示进度条
      if (result.task_id) {
        setTaskId(result.task_id);
      } else {
        // 如果没有任务ID，直接刷新数据
        onRefresh();
        setLoading(null);
      }
    } catch (error) {
      console.error(`Failed to generate ${type}:`, error);
      setLoading(null);
      setTaskId(null);
      // 可以在这里添加错误提示
    }
  };

  // 进度完成回调
  const handleProgressComplete = () => {
    setLoading(null);
    setTaskId(null);
    onRefresh();
  };

  // 进度错误回调
  const handleProgressError = (error: string) => {
    setLoading(null);
    setTaskId(null);
    console.error('Progress error:', error);
  };



  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Play className="h-5 w-5" />
          {t("title")}
        </CardTitle>
        <CardDescription>
          {t("description")}
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {/* 进度条 */}
        {taskId && (
          <div className="mb-6">
            <ProgressBar
              taskId={taskId}
              onComplete={handleProgressComplete}
              onError={handleProgressError}
            />
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* 生成场景按钮 */}
          <Button
            variant="outline"
            className="h-auto flex-col gap-2 p-4"
            onClick={() => showConfirmDialog(
              "生成场景",
              "确定要生成场景吗？这将为创意生成新的场景描述。",
              () => {
                closeConfirmDialog();
                handleGenerate("scenes", () => generateScenes(creativeId));
              }
            )}
            disabled={loading !== null}
          >
            {loading === "scenes" ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              <FileText className="h-6 w-6" />
            )}
            <div className="text-center">
              <div className="font-medium">{t("generateScenes")}</div>
              <div className="text-xs text-muted-foreground">{t("generateScenesDesc")}</div>
            </div>
          </Button>

          {/* 生成图片按钮 */}
          <Button
            variant="outline"
            className="h-auto flex-col gap-2 p-4"
            onClick={() => showConfirmDialog(
              "生成图片",
              "确定要生成图片吗？这将为所有场景生成对应的图片。",
              () => {
                closeConfirmDialog();
                handleGenerate("images", () => generateImages(creativeId));
              }
            )}
            disabled={loading !== null}
          >
            {loading === "images" ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              <Image className="h-6 w-6" />
            )}
            <div className="text-center">
              <div className="font-medium">{t("generateImages")}</div>
              <div className="text-xs text-muted-foreground">{t("generateImagesDesc")}</div>
            </div>
          </Button>

          {/* 生成视频按钮 */}
          <Button
            variant="outline"
            className="h-auto flex-col gap-2 p-4"
            onClick={() => showConfirmDialog(
              "生成视频",
              "确定要生成视频吗？这将基于场景图片生成对应的视频。",
              () => {
                closeConfirmDialog();
                handleGenerate("videos", () => generateVideos(creativeId));
              }
            )}
            disabled={loading !== null}
          >
            {loading === "videos" ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              <Video className="h-6 w-6" />
            )}
            <div className="text-center">
              <div className="font-medium">{t("generateVideos")}</div>
              <div className="text-xs text-muted-foreground">{t("generateVideosDesc")}</div>
            </div>
          </Button>

          {/* 生成字幕按钮 */}
          <Button
            variant="outline"
            className="h-auto flex-col gap-2 p-4"
            onClick={() => showConfirmDialog(
              "生成字幕",
              "确定要生成字幕吗？这将为所有视频生成对应的字幕和音频。",
              () => {
                closeConfirmDialog();
                handleGenerate("subtitles", () => generateSubtitles(creativeId));
              }
            )}
            disabled={loading !== null}
          >
            {loading === "subtitles" ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              <Subtitles className="h-6 w-6" />
            )}
            <div className="text-center">
              <div className="font-medium">{t("generateSubtitles")}</div>
              <div className="text-xs text-muted-foreground">{t("generateSubtitlesDesc")}</div>
            </div>
          </Button>
        </div>
      </CardContent>

      {/* 确认对话框 */}
      <Dialog open={confirmDialog.open} onOpenChange={closeConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{confirmDialog.title}</DialogTitle>
            <DialogDescription>{confirmDialog.description}</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={closeConfirmDialog}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmDialog.onConfirm}>
              确认
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
