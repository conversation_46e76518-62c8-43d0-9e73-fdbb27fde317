{"common": {"cancel": "Cancel", "save": "Save", "settings": "Settings", "getStarted": "Start a Agent", "getWorkflow": "Start a Workflow", "starOnGitHub": "Star on GitHub", "send": "Send", "stop": "Stop", "linkNotReliable": "This link might be a hallucination from AI model and may not be reliable.", "noResult": "No result"}, "messageInput": {"placeholder": "What can I do for you?", "placeholderWithRag": "What can I do for you? \nYou may refer to RAG resources by using @."}, "header": {"title": "VideoFlow"}, "hero": {"title": "Video Generation", "subtitle": "at Your Fingertips", "description": "Meet <PERSON><PERSON><PERSON>, your personal AI video generation assistant. With powerful tools for creative ideation, scene generation, and multimedia production, it delivers instant video content creation and comprehensive workflow management.", "footnote": "* VideoFlow stands for intelligent Video production workFlow."}, "settings": {"title": "VideoFlow Settings", "description": "Manage your VideoFlow settings here.", "addServers": "Add Servers", "cancel": "Cancel", "addNewMCPServers": "Add New MCP Servers", "mcpConfigDescription": "VideoFlow uses the standard JSON MCP config to create a new server.", "pasteConfigBelow": "Paste your config below and click \"Add\" to add new servers.", "add": "Add", "general": {"title": "General", "autoAcceptPlan": "Allow automatic acceptance of plans", "maxPlanIterations": "Max plan iterations", "maxPlanIterationsDescription": "Set to 1 for single-step planning. Set to 2 or more to enable re-planning.", "maxStepsOfPlan": "Max steps of a research plan", "maxStepsDescription": "By default, each research plan has 3 steps.", "maxSearchResults": "Max search results", "maxSearchResultsDescription": "By default, each search step has 3 results."}, "mcp": {"title": "MCP Servers", "description": "The Model Context Protocol boosts VideoFlow by integrating external tools for tasks like private domain searches, web browsing, food ordering, and more. Click here to", "learnMore": "learn more about MCP.", "enableDisable": "Enable/disable server", "deleteServer": "Delete server", "disabled": "Disabled", "new": "New"}, "about": {"title": "About"}, "reportStyle": {"writingStyle": "Writing Style", "chooseTitle": "<PERSON>ose Writing Style", "chooseDesc": "Select the writing style for your research reports. Different styles are optimized for different audiences and purposes.", "academic": "Academic", "academicDesc": "Formal, objective, and analytical with precise terminology", "popularScience": "Popular Science", "popularScienceDesc": "Engaging and accessible for general audience", "news": "News", "newsDesc": "Factual, concise, and impartial journalistic style", "socialMedia": "Social Media", "socialMediaDesc": "Concise, attention-grabbing, and shareable"}}, "footer": {"quote": "Originated from Open Source, give back to Open Source.", "license": "Licensed under MIT License", "copyright": "VideoFlow"}, "chat": {"page": {"loading": "Loading VideoFlow...", "welcomeUser": "Welcome, {username}", "starOnGitHub": "Star VideoFlow on GitHub"}, "welcome": {"greeting": "👋 Hello, there!", "description": "Welcome to 🎬 VideoFlow, an AI video generation assistant built on cutting-edge language models, helps you create videos, generate scenes, and handle multimedia production tasks."}, "conversationStarters": ["Create a 30-second promotional video for a tech startup", "Generate a cinematic scene of a futuristic city at sunset", "Design a video tutorial explaining machine learning concepts", "Create an animated story about environmental conservation"], "inputBox": {"deepThinking": "Deep Thinking", "deepThinkingTooltip": {"title": "Deep Thinking Mode: {status}", "description": "When enabled, VideoFlow will use reasoning model ({model}) to generate more thoughtful plans."}, "investigation": "Investigation", "investigationTooltip": {"title": "Investigation Mode: {status}", "description": "When enabled, VideoFlow will perform a quick search before planning. This is useful for video content related to ongoing events and trends."}, "enhancePrompt": "Enhance prompt with AI", "on": "On", "off": "Off"}, "research": {"deepResearch": "Deep Research", "researching": "Researching...", "generatingReport": "Generating report...", "reportGenerated": "Report generated", "open": "Open", "close": "Close", "deepThinking": "Deep Thinking", "report": "Report", "activities": "Activities", "generatePodcast": "Generate podcast", "edit": "Edit", "copy": "Copy", "downloadReport": "Download report as markdown", "searchingFor": "Searching for", "reading": "Reading", "runningPythonCode": "Running Python code", "errorExecutingCode": "Error when executing the above code", "executionOutput": "Execution output", "retrievingDocuments": "Retrieving documents from RAG", "running": "Running", "generatingPodcast": "Generating podcast...", "nowPlayingPodcast": "Now playing podcast...", "podcast": "Podcast", "errorGeneratingPodcast": "Error when generating podcast. Please try again.", "downloadPodcast": "Download podcast"}, "messages": {"replaying": "Replaying", "replayDescription": "VideoFlow is now replaying the conversation...", "replayHasStopped": "The replay has been stopped.", "replayModeDescription": "You're now in VideoFlow's replay mode. Click the \"Play\" button on the right to start.", "play": "Play", "fastForward": "Fast Forward", "demoNotice": "* This site is for demo purposes only. If you want to try your own question, please", "clickHere": "click here", "cloneLocally": "to clone it locally and run it."}, "multiAgent": {"moveToPrevious": "Move to the previous step", "playPause": "Play / Pause", "moveToNext": "Move to the next step", "toggleFullscreen": "Toggle fullscreen"}}, "landing": {"caseStudies": {"title": "Case Studies", "description": "See Video<PERSON>low in action through replays.", "clickToWatch": "Click to watch replay", "cases": [{"title": "How tall is Eiffel Tower compared to tallest building?", "description": "The research compares the heights and global significance of the Eiffel Tower and Burj Khalifa, and uses Python code to calculate the multiples."}, {"title": "What are the top trending repositories on GitHub?", "description": "The research utilized MCP services to identify the most popular GitHub repositories and documented them in detail using search engines."}, {"title": "Write an article about Nanjing's traditional dishes", "description": "The study vividly showcases Nanjing's famous dishes through rich content and imagery, uncovering their hidden histories and cultural significance."}, {"title": "How to decorate a small rental apartment?", "description": "The study provides readers with practical and straightforward methods for decorating apartments, accompanied by inspiring images."}, {"title": "Introduce the movie 'Léon: The Professional'", "description": "The research provides a comprehensive introduction to the movie 'Léon: The Professional', including its plot, characters, and themes."}, {"title": "How do you view the takeaway war in China? (in Chinese)", "description": "The research analyzes the intensifying competition between JD and Meituan, highlighting their strategies, technological innovations, and challenges."}, {"title": "Are ultra-processed foods linked to health?", "description": "The research examines the health risks of rising ultra-processed food consumption, urging more research on long-term effects and individual differences."}, {"title": "Write an article on \"Would you insure your AI twin?\"", "description": "The research explores the concept of insuring AI twins, highlighting their benefits, risks, ethical considerations, and the evolving regulatory."}]}, "coreFeatures": {"title": "Core Features", "description": "Find out what makes VideoFlow effective.", "features": [{"name": "Creative Video Generation", "description": "Transform ideas into compelling videos with advanced AI tools. Our powerful scene generation and multimedia production capabilities deliver professional-quality content."}, {"name": "Human-in-the-loop", "description": "Refine your video concepts, adjust creative direction, or modify scenes all through simple natural language."}, {"name": "<PERSON>", "description": "Build with confidence using the LangChain and LangGraph frameworks."}, {"name": "MCP Integrations", "description": "Supercharge your video production workflow and expand your toolkit with seamless MCP integrations."}, {"name": "Multi-format Output", "description": "Generate videos, images, audio, and subtitles. Perfect for comprehensive multimedia content creation."}]}, "multiAgent": {"title": "Multi-Agent Architecture", "description": "Experience the agent teamwork with our Supervisor + Handoffs design pattern."}, "joinCommunity": {"title": "Join the VideoFlow Community", "description": "Contribute brilliant ideas to shape the future of VideoFlow. Collaborate, innovate, and make impacts.", "contributeNow": "Contribute Now"}}, "workflow": {"title": "Video Workflow", "creatives": {"title": "Video Creatives", "search": "Search creatives...", "refresh": "Refresh", "newCreative": "New Creative", "noCreatives": "No creatives yet", "noCreativesDescription": "Create your first video creative to get started", "noMatchingCreatives": "No matching creatives found", "noMatchingDescription": "Try adjusting your search terms", "clearSearch": "Clear search", "viewDetails": "View Details", "items": "items", "create": {"title": "Create New Video Creative", "description": "Add a new creative idea for your video project. Describe your concept and optionally specify an art style.", "creativeLabel": "Creative Description", "creativePlaceholder": "Describe your video creative idea...", "artStyleLabel": "Art Style (Optional)", "artStylePlaceholder": "e.g., Cyberpunk, Realistic, Cartoon, etc.", "cancel": "Cancel", "create": "Create Creative", "creating": "Creating...", "required": "*", "errorRequired": "Please enter a creative description", "errorFailed": "Failed to create creative, please try again later"}}, "detail": {"backToList": "Back to List", "overview": "Overview", "images": "Images", "scenes": "Scenes", "subtitles": "Subtitles", "videos": "Videos", "creativeDetails": "Creative Details", "description": "Description", "artStyle": "Art Style", "lastUpdated": "Last Updated", "noPrompts": "No prompts yet", "noPromptsDescription": "This creative doesn't have any prompts yet.", "noImages": "No images yet", "noImagesDescription": "This creative doesn't have any images yet.", "noScenes": "No scenes yet", "noScenesDescription": "This creative doesn't have any scenes yet.", "noSubtitles": "No subtitles yet", "noSubtitlesDescription": "This creative doesn't have any subtitles yet.", "noVideos": "No videos yet", "noVideosDescription": "This creative doesn't have any videos yet.", "prompt": "Prompt", "image": "Image", "video": "Video", "subtitleText": "Subtitle Text", "audioFile": "Audio File", "from": "From", "scene": "Scene", "addScene": "Add Scene", "create": {"title": "Add New Scene", "description": "Add a new scene to this creative. Please provide a scene title and detailed prompt description.", "sceneLabel": "Scene Title", "scenePlaceholder": "Enter scene title, e.g., Beautiful Mountain Landscape", "promptLabel": "Scene Prompt", "promptPlaceholder": "Enter detailed scene description, e.g., Majestic mountains with clouds, sunlight streaming through clouds onto the earth, showcasing the magnificence of nature...", "required": "*", "cancel": "Cancel", "create": "Create Scene", "creating": "Creating...", "errorSceneRequired": "Scene title is required", "errorPromptRequired": "Scene prompt is required", "errorFailed": "Failed to create scene. Please try again."}, "hasVideo": "Has Video", "hasImage": "Has Image", "imageNotAvailable": "Image not available", "videoNotAvailable": "Video not available", "audioNotAvailable": "Audio not available", "audioPlayer": "Audio Player", "audioFilePath": "Audio File Path", "edit": "Edit", "save": "Save", "cancel": "Cancel", "saveChanges": "Save Changes", "saving": "Saving...", "notSpecified": "Not specified", "enterArtStyle": "Enter art style...", "sceneTitle": "Scene Title", "enterSubtitleText": "Enter subtitle text...", "noSubtitleText": "No subtitle text", "generation": {"title": "Generation Tools", "description": "Generate content for your video creative using AI tools", "generateScenes": "Generate Scenes", "generateScenesDesc": "Create scene prompts", "generateImages": "Generate Images", "generateImagesDesc": "Create scene images", "generateVideos": "Generate Videos", "generateVideosDesc": "Convert to videos", "generateSubtitles": "Generate Subtitles", "generateSubtitlesDesc": "Add audio & subtitles", "progress": {"scenes": {"title": "Generating Scenes", "description": "Creating scene prompts based on your creative idea..."}, "images": {"title": "Generating Images", "description": "Creating images for your scenes..."}, "videos": {"title": "Generating Videos", "description": "Converting images to videos..."}, "subtitles": {"title": "Generating Subtitles", "description": "Creating subtitles and audio for your scenes..."}, "status": {"pending": "Pending", "running": "Running", "completed": "Completed", "failed": "Failed"}, "progress": "Progress", "taskId": "Task ID", "close": "Close", "cancel": "Cancel"}}}}}