import time
from openai import OpenAI
import json
import mimetypes
import logging
import os
import asyncio
import aiohttp
import base64
import traceback
import uuid
import oss2
from datetime import datetime
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage

from dotenv import load_dotenv
load_dotenv()
from src.config.video import VideoSettings
# 初始化日志
logger = logging.getLogger(__name__)

# LLM API 配置
QWEN_BASE_URL = os.getenv("QWEN_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
QWEN_API_KEY = os.getenv("QWEN_API_KEY", "sk-9502dc0b92784939b2f1986d897a41ea")
QWEN_IMAGE_BASE_URL = os.getenv("QWEN_IMAGE_BASE_URL", "https://dashscope.aliyuncs.com/api/v1")

# Google Gemini API 配置
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
if not GOOGLE_API_KEY:
    logger.warning("GOOGLE_API_KEY not found in environment variables")


TEXT2IMAGE_SUBMIT_URL = f"{QWEN_IMAGE_BASE_URL}/services/aigc/text2image/image-synthesis"
IMAGE2VIDEO_SUBMIT_URL = f"{QWEN_IMAGE_BASE_URL}/services/aigc/video-generation/video-synthesis"
TASK_STATUS_URL_TEMPLATE = QWEN_IMAGE_BASE_URL + "/tasks/{}"  # Placeholder for task_id
# MODEL_NAME = "flux-dev" # Or "flux-dev"
IMAGE_MODEL_NAME = "wan2.2-t2i-flash"
VIDEO_MODEL_NAME = "wan2.2-i2v-plus"
POLL_INTERVAL_SECONDS = 3  # How often to check task status
MAX_POLL_ATTEMPTS = 120     # Max attempts (e.g., 120 * 3s = 120s = 6 minutes timeout)

def push_to_oss(oss_prefix, data, file_name):
    settings = VideoSettings()
    image_filename = str(uuid.uuid4()) + "_" + file_name
    oss_path = f"{oss_prefix}/{image_filename}"
    
    bucket = oss2.Bucket(oss2.Auth(settings.oss_secretkey, settings.oss_secretpass), settings.oss_endpoint, settings.oss_bucket)
    bucket.put_object(oss_path, data)
    return oss_path



def delete_from_oss(oss_path):
    
    # 不删除默认图片
    if oss_path.find("default") >= 0:
        return
    
    settings = VideoSettings()
    oss_file = oss_path.replace(f"{settings.oss_http_prefix}/" , "")
    settings = VideoSettings()
    bucket = oss2.Bucket(oss2.Auth(settings.oss_secretkey, settings.oss_secretpass), settings.oss_endpoint, settings.oss_bucket)
    bucket.delete_object(oss_file)


def fetch_from_oss(oss_path: str) -> tuple[str | None, str | None]:
    """
    从 OSS 获取文件，并返回 Base64 编码的内容和 MIME 类型

    Args:
        oss_path (str): 文件的完整 OSS URL

    Returns:
        tuple[str, str]: (Base64 编码的文件内容, MIME 类型) or (None, None) if failed
    """
    try:
        settings = VideoSettings()

        # 从完整的 URL 中提取 OSS object key
        if not oss_path.startswith(settings.oss_http_prefix):
            logger.error(f"Invalid OSS path format: {oss_path}")
            return None, None

        oss_key = oss_path.replace(f"{settings.oss_http_prefix}/", "")

        # 初始化 OSS Bucket
        bucket = oss2.Bucket(oss2.Auth(settings.oss_secretkey, settings.oss_secretpass), settings.oss_endpoint, settings.oss_bucket)

        # 获取文件对象并读取内容（二进制）
        file_content = bucket.get_object(oss_key).read()

        # Base64 编码
        base64_content = base64.b64encode(file_content).decode('utf-8')

        # 获取 MIME 类型
        mime_type, _ = mimetypes.guess_type(oss_path)
        return base64_content, mime_type or 'application/octet-stream'

    except oss2.exceptions.NoSuchKey:
        logger.error(f"File not found in OSS: {oss_path}")
        return None, None
    except Exception as e:
        logger.error(f"Failed to fetch from OSS: {oss_path}, Error: {e}")
        traceback.print_exc()
        return None, None

# ==================== API 专用函数 ====================

async def generate_scenes_for_api(creative_idea: str, art_style: str = None, scene_cnt: int = 5, character_prompt: str = None):
    """
    为 API 调用生成场景，不依赖 Gradio

    参数:
        creative_idea (str): 用户输入的创意
        art_style (str): 艺术风格（可选）
        scene_cnt (int): 场景数量（默认5个）
        character_prompt (str): 主角形象描述提示词（可选）

    返回:
        list[dict]: 包含场景描述和提示词的字典列表
    """
    try:
        # 构建提示词
        style_text = f"，艺术风格为{art_style}" if art_style else ""
        character_text = f"\n\n主角形象描述：{character_prompt}" if character_prompt else ""
        prompt = f"""
        请根据以下创意{style_text}，生成{scene_cnt}个短视频场景。每个场景应该包含场景标题和详细的提示词。
        务必保证场景间角色的连续和一致性。
        提示词要求描写一个静态的镜头视角下场景，不要包含角色的动态行为描述。
        场景描述要求包含镜头视角，周围环境，角色，面部表情，情绪气氛。
        {character_text}

        创意：{creative_idea}

        请以JSON格式返回，格式如下：
        [
            {{"scene": "场景标题", "prompt": "详细的场景描述提示词"}},
            {{"scene": "场景标题", "prompt": "详细的场景描述提示词"}}
        ]
        """

        # 调用 LLM API
        client = OpenAI(
            api_key=QWEN_API_KEY,
            base_url=QWEN_BASE_URL,
        )

        response = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {"role": "system", "content": "你是一个专业的视频创意助手，擅长将创意扩展为具体的场景描述。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=2000
        )

        # 解析响应
        content = response.choices[0].message.content.strip()

        # 尝试解析 JSON
        try:
            # 提取 JSON 部分
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1
            if start_idx != -1 and end_idx != 0:
                json_str = content[start_idx:end_idx]
                scenes_data = json.loads(json_str)
                return scenes_data
            else:
                # 如果没有找到 JSON，返回默认场景
                return [
                    {"scene": "开场场景", "prompt": f"基于创意'{creative_idea}'的开场场景"},
                    {"scene": "发展场景", "prompt": f"基于创意'{creative_idea}'的发展场景"},
                    {"scene": "高潮场景", "prompt": f"基于创意'{creative_idea}'的高潮场景"},
                    {"scene": "结尾场景", "prompt": f"基于创意'{creative_idea}'的结尾场景"}
                ]
        except json.JSONDecodeError as e:
            logger.error(f"解析 LLM JSON 响应时出错: {e}")
            # 返回默认场景
            return [
                {"scene": "场景1", "prompt": f"基于创意'{creative_idea}'的场景描述"},
                {"scene": "场景2", "prompt": f"基于创意'{creative_idea}'的场景描述"},
                {"scene": "场景3", "prompt": f"基于创意'{creative_idea}'的场景描述"}
            ]

    except Exception as e:
        logger.error(f"生成场景时出错: {e}")
        # 返回默认场景
        return [
            {"scene": "默认场景", "prompt": f"基于创意'{creative_idea}'的场景描述"}
        ]

async def generate_images_for_api(prompt: str, scene_id:str, size: str = "1280*720"):
    """
    为 API 调用生成图片，使用阿里云 Qwen 图片生成 API

    参数:
        prompt (str): 图片生成提示词
        size (str): 图片分辨率，默认 "1024*1024"

    返回:
        str: 图片路径或错误信息
    """

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {QWEN_API_KEY}",
        "X-DashScope-Async": "enable",
    }
    payload = {
        "model": IMAGE_MODEL_NAME,
        "input": {
            "prompt": prompt
        },
        "parameters": {
            "size": size
        }
    }
    settings = VideoSettings()

    date_prefix = datetime.now().strftime("video-flow/%Y/%m/%d")
    default_image = f"{settings.oss_http_prefix}/video-flow/default.png"

    task_id = None
    try:
        async with aiohttp.ClientSession() as session:
            # 1. Submit the image generation task
            logger.info(f"Submitting image task for prompt: '{prompt}'")
            async with session.post(TEXT2IMAGE_SUBMIT_URL, headers=headers, json=payload) as response:
                if response.status == 200:
                    submit_result = await response.json()
                    task_id = submit_result.get("output", {}).get("task_id")
                    task_status = submit_result.get("output", {}).get("task_status")
                    logger.info(f"Task submitted. ID: {task_id}, Initial Status: {task_status}")
                else:
                    error_text = await response.text()
                    logger.error(f"Error submitting task: HTTP {response.status}, Response: {error_text}")
                    return default_image

            if not task_id:
                error_msg = submit_result.get("message", "Failed to get task_id from submission response.")
                logger.error(f"Error submitting task: {error_msg}")
                return default_image

            # 2. Poll for task status and result
            task_url = TASK_STATUS_URL_TEMPLATE.format(task_id)
            logger.info(f"Polling task status for ID: {task_id}")
            for attempt in range(MAX_POLL_ATTEMPTS):
                await asyncio.sleep(POLL_INTERVAL_SECONDS)  # Wait before checking
                logger.info(f"Polling attempt {attempt + 1}/{MAX_POLL_ATTEMPTS}...")
                async with session.get(task_url, headers={"Authorization": f"Bearer {QWEN_API_KEY}"}) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Error polling task status: HTTP {response.status}, Response: {error_text}")
                        continue  # Continue polling for transient errors

                    status_result = await response.json()
                    task_status = status_result.get("output", {}).get("task_status")
                    logger.info(f"Current task status: {task_status}")

                    if task_status == "SUCCEEDED":
                        results = status_result.get("output", {}).get("results")
                        if results and isinstance(results, list) and len(results) > 0:
                            image_url = results[0].get("url")
                            if image_url:
                                logger.info(f"Task succeeded. Image URL: {image_url}")
                                # 3. Download and save the image
                                async with session.get(image_url) as img_response:
                                    if img_response.status == 200:
                                        image_bytes = await img_response.read()
                                        logger.info(f"Image downloaded successfully ({len(image_bytes)} bytes).")

                                        filename = f"image_{scene_id}.jpg"
                                        oss_filename = push_to_oss(date_prefix, image_bytes, filename)
                                        return f"{settings.oss_http_prefix}/{oss_filename}"
                                    else:
                                        error_text = await img_response.text()
                                        logger.error(f"Failed to download image: HTTP {img_response.status}, Response: {error_text}")
                                        return default_image
                        else:
                            logger.error("Task succeeded but no image URL found in results.")
                            return default_image
                        break  # Exit polling loop on success

                    elif task_status == "FAILED":
                        error_code = status_result.get("output", {}).get("code", "UnknownCode")
                        error_message = status_result.get("output", {}).get("message", "Unknown error.")
                        logger.error(f"Task failed. Code: {error_code}, Message: {error_message}")
                        return default_image

                    elif task_status in ["PENDING", "RUNNING"]:
                        # Continue polling
                        pass
                    else:  # UNKNOWN or other unexpected status
                        logger.error(f"Unknown or unexpected task status: {task_status}")
                        return default_image

            else:  # Loop finished without success or explicit failure (timeout)
                logger.error("Polling timed out.")
                return default_image

    except aiohttp.ClientError as e:
        logger.error(f"Network error during image generation: {e}")
        traceback.print_exc()
        return default_image
    except asyncio.TimeoutError:
        logger.error("Asyncio operation timed out.")
        traceback.print_exc()
        return default_image
    except Exception as e:
        logger.error(f"An unexpected error occurred: {traceback.format_exc()}")
        traceback.print_exc()
        return default_image

async def generate_videos_for_api(prompt: str, img_url: str,  video_id:str, size: str = "720P"):
    """
    为 API 调用生成视频，使用阿里云 Qwen 图生视频 API

    参数:
        prompt (str): 视频生成提示词
        img_url (str): 图片URL
        size (str): 视频分辨率，默认 "720P"

    返回:
        str: 视频路径或错误信息
    """

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {QWEN_API_KEY}",
        "X-DashScope-Async": "enable",
    }
    payload = {
        "model": VIDEO_MODEL_NAME,
        "input": {
            "img_url": img_url,
            "prompt": prompt
        },
        "parameters": {
            "resolution": size,
            "prompt_extend": True
        }
    }
    
    settings = VideoSettings()
    logger.info(f"{settings}")

    date_prefix = datetime.now().strftime("video-flow/video/%Y/%m/%d")
    default_video = f"{settings.oss_http_prefix}/video-flow/video/default.mp4"

    task_id = None
    try:
        async with aiohttp.ClientSession() as session:
            # 1. Submit the video generation task
            logger.info(f"Submitting video task for prompt: '{prompt}' with image: '{img_url}'")
            async with session.post(IMAGE2VIDEO_SUBMIT_URL, headers=headers, json=payload) as response:
                if response.status == 200:
                    submit_result = await response.json()
                    task_id = submit_result.get("output", {}).get("task_id")
                    task_status = submit_result.get("output", {}).get("task_status")
                    logger.info(f"Task submitted. ID: {task_id}, Initial Status: {task_status}")
                else:
                    error_text = await response.text()
                    logger.error(f"Error submitting task: HTTP {response.status}, Response: {error_text}")
                    return default_video

            if not task_id:
                error_msg = submit_result.get("message", "Failed to get task_id from submission response.")
                logger.error(f"Error submitting task: {error_msg}")
                return default_video

            # 2. Poll for task status and result
            task_url = TASK_STATUS_URL_TEMPLATE.format(task_id)
            logger.info(f"Polling task status for ID: {task_id}")
            for attempt in range(MAX_POLL_ATTEMPTS):
                await asyncio.sleep(POLL_INTERVAL_SECONDS)  # Wait before checking
                logger.info(f"Polling attempt {attempt + 1}/{MAX_POLL_ATTEMPTS}...")
                async with session.get(task_url, headers={"Authorization": f"Bearer {QWEN_API_KEY}"}) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Error polling task status: HTTP {response.status}, Response: {error_text}")
                        continue  # Continue polling for transient errors

                    status_result = await response.json()
                    task_status = status_result.get("output", {}).get("task_status")
                    logger.info(f"Current task status: {task_status}")

                    if task_status == "SUCCEEDED":
                        results = status_result.get("output", {})
                        if results and isinstance(results, dict):
                            video_url = results.get("video_url")
                            if video_url:
                                logger.info(f"Task succeeded. Video URL: {video_url}")
                                # 3. Download and save the video
                                async with session.get(video_url) as video_response:
                                    if video_response.status == 200:
                                        video_bytes = await video_response.read()
                                        logger.info(f"Video downloaded successfully ({len(video_bytes)} bytes).")
                                        
                                        filename = f"video_{video_id}.mp4"
                                        oss_filename = push_to_oss(date_prefix, video_bytes, filename)
                                        return f"{settings.oss_http_prefix}/{oss_filename}"
                                    else:
                                        error_text = await video_response.text()
                                        logger.error(f"Failed to download video: HTTP {video_response.status}, Response: {error_text}")
                                        return default_video
                        else:
                            logger.error("Task succeeded but no video URL found in results.")
                            return default_video
                        break  # Exit polling loop on success

                    elif task_status == "FAILED":
                        error_code = status_result.get("output", {}).get("code", "UnknownCode")
                        error_message = status_result.get("output", {}).get("message", "Unknown error.")
                        logger.error(f"Task failed. Code: {error_code}, Message: {error_message}")
                        return default_video

                    elif task_status in ["PENDING", "RUNNING"]:
                        # Continue polling
                        pass
                    else:  # UNKNOWN or other unexpected status
                        logger.error(f"Unknown or unexpected task status: {task_status}")
                        return default_video

            else:  # Loop finished without success or explicit failure (timeout)
                logger.error("Polling timed out.")
                return default_video

    except aiohttp.ClientError as e:
        logger.error(f"Network error during image generation: {e}")
        traceback.print_exc()
        return default_video
    except asyncio.TimeoutError:
        logger.error("Asyncio operation timed out.")
        traceback.print_exc()
        return default_video
    except Exception as e:
        logger.error(f"An unexpected error occurred: {traceback.format_exc()}")
        traceback.print_exc()
        return default_video


async def generate_subtitle_with_gemini(video_prompt: str, scene_description: str = "", scene_prompt: str = "", video_url: str = "") -> str:
    """
    使用 Google Gemini 生成字幕文本

    Args:
        video_prompt (str): 视频生成提示词
        scene_description (str): 场景描述
        scene_prompt (str): 场景提示词
        video_url (str): 视频URL

    Returns:
        str: 生成的字幕文本
    """
    try:


        # 构建提示词
        prompt = f"""
请总结内容生成简洁、吸引人的字幕文本：

场景信息：{scene_description}
场景图片: {scene_prompt}
场景视频:{video_prompt}
要求：
1. 基于场景信息,从编剧的角度推理场景和视频涉及的内容,生成字幕
2. 适合短视频平台展示
3. 突出视频的核心内容和亮点
4. 使用中文
5. 字幕文本不超过500字

请直接返回字幕文本，不需要其他说明, 使用中文回答：
"""
        
        # if not GOOGLE_API_KEY:
        #     logger.error("Google API Key not configured")
        #     return f"为视频生成的字幕：{video_prompt[:50]}..."

        # # 初始化 Gemini 模型
        # llm = ChatGoogleGenerativeAI(
        #     model="gemini-2.5-flash",
        #     google_api_key=GOOGLE_API_KEY,
        #     temperature=0.7
        # )

        # encoded_video, video_mime_type = fetch_from_oss(video_url)
        # 调用 Gemini API
        # message = HumanMessage(content=[{
        #     "type": "text",
        #     "text": prompt},{
        #     "type": "media",
        #     "data": encoded_video,  # Use base64 string directly
        #     "mime_type": video_mime_type,
        # }])
        # 调用 Gemini API
        # message = HumanMessage(content=[prompt])
        # response = await asyncio.to_thread(llm.invoke, [message])
        # subtitle_text = response.content.strip()        

        # 调用 LLM API
        client = OpenAI(
            api_key=QWEN_API_KEY,
            base_url=QWEN_BASE_URL,
        )

        response = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {"role": "system", "content": "你是一个专业的视频创意助手，负责从具体的场景描述信息中提取内容连贯，吸引人的字幕。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=2000
        )

        # 解析响应
        subtitle_text = response.choices[0].message.content.strip()

        logger.info(f"Generated subtitle with Gemini: {subtitle_text}")
        return subtitle_text

    except Exception as e:
        logger.error(f"Failed to generate subtitle with Gemini: {str(e)}")
        # 返回默认字幕
        return f"精彩视频内容：{video_prompt[:30]}..."


async def generate_audio_from_text(text: str, audio_id: int) -> str:
    """
    将文本转换为音频文件并上传到 OSS
    使用本地 SPARK TTS HTTP API

    Args:
        text (str): 要转换的文本
        audio_id (int): 音频ID，用于文件命名

    Returns:
        str: 音频文件的 OSS URL
    """
    try:
        settings = VideoSettings()
        default_audio = f"{settings.oss_http_prefix}/video-flow/audio/default.wav"
        # 获取 SPARK TTS API 配置
        spark_tts_prefix = os.getenv("SPARK_TTS_PREFIX")
        if not spark_tts_prefix:
            logger.error("SPARK_TTS_PREFIX not configured")
            return default_audio

        # 生成音频文件名，使用 audio_id
        audio_filename = f"audio_{audio_id}.wav"
        date_prefix = datetime.now().strftime("video-flow/audio/%Y/%m/%d")

        logger.info(f"Generating audio for text: {text[:50]}... using audio_id: {audio_id}")

        # 调用本地 SPARK TTS API
        tts_url = spark_tts_prefix
        payload = {"text": text}

        async with aiohttp.ClientSession() as session:
            async with session.post(tts_url, json=payload, timeout=30) as response:
                if response.status != 200:
                    logger.error(f"TTS API failed with status {response.status}")
                    return default_audio

                # 获取音频数据
                audio_data = await response.read()

                if not audio_data:
                    logger.error("TTS API returned empty audio data")
                    return default_audio


        oss_file_path = push_to_oss(date_prefix, audio_data, audio_filename)
        # 构建完整的 OSS URL
        audio_url = f"{settings.oss_http_prefix}/{oss_file_path}"

        logger.info(f"Successfully generated and uploaded audio: {audio_url}")
        return audio_url

    except asyncio.TimeoutError:
        logger.error("TTS API request timeout")
        return default_audio
    except Exception as e:
        logger.error(f"Failed to generate audio: {str(e)}")
        logger.error(f"Error traceback: {traceback.format_exc()}")
        # 返回默认音频 URL
        return default_audio


async def generate_character_image_for_api(character_prompt: str, creative_id: int, size: str = "1280*720"):
    """
    为主角生成图片

    参数:
        character_prompt (str): 主角形象描述提示词
        creative_id (int): 创意ID，用于文件命名

    返回:
        str: 生成的图片URL
    """

    logger.info(f"Starting character image generation for creative {creative_id}")

    # 构建图片生成提示词
    enhanced_prompt = f"""
    {character_prompt}

    要求：
    - 高质量的人物肖像
    - 清晰的面部特征
    - 专业的摄影效果
    - 适合作为视频主角的形象
    - 正面照
    """

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {QWEN_API_KEY}",
        "X-DashScope-Async": "enable",
    }
    payload = {
        "model": IMAGE_MODEL_NAME,
        "input": {
            "prompt": enhanced_prompt
        },
        "parameters": {
            "size": size
        }
    }
    settings = VideoSettings()

    date_prefix = datetime.now().strftime("video-flow/%Y/%m/%d")
    default_image = f"{settings.oss_http_prefix}/video-flow/default.png"

    task_id = None
    try:
        async with aiohttp.ClientSession() as session:
            # 1. Submit the image generation task
            logger.info(f"Submitting image task for prompt: '{enhanced_prompt}'")
            async with session.post(TEXT2IMAGE_SUBMIT_URL, headers=headers, json=payload) as response:
                if response.status == 200:
                    submit_result = await response.json()
                    task_id = submit_result.get("output", {}).get("task_id")
                    task_status = submit_result.get("output", {}).get("task_status")
                    logger.info(f"Task submitted. ID: {task_id}, Initial Status: {task_status}")
                else:
                    error_text = await response.text()
                    logger.error(f"Error submitting task: HTTP {response.status}, Response: {error_text}")
                    return default_image

            if not task_id:
                error_msg = submit_result.get("message", "Failed to get task_id from submission response.")
                logger.error(f"Error submitting task: {error_msg}")
                return default_image

            # 2. Poll for task status and result
            task_url = TASK_STATUS_URL_TEMPLATE.format(task_id)
            logger.info(f"Polling task status for ID: {task_id}")
            for attempt in range(MAX_POLL_ATTEMPTS):
                await asyncio.sleep(POLL_INTERVAL_SECONDS)  # Wait before checking
                logger.info(f"Polling attempt {attempt + 1}/{MAX_POLL_ATTEMPTS}...")
                async with session.get(task_url, headers={"Authorization": f"Bearer {QWEN_API_KEY}"}) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Error polling task status: HTTP {response.status}, Response: {error_text}")
                        continue  # Continue polling for transient errors

                    status_result = await response.json()
                    task_status = status_result.get("output", {}).get("task_status")
                    logger.info(f"Current task status: {task_status}")

                    if task_status == "SUCCEEDED":
                        results = status_result.get("output", {}).get("results")
                        if results and isinstance(results, list) and len(results) > 0:
                            image_url = results[0].get("url")
                            if image_url:
                                logger.info(f"Task succeeded. Image URL: {image_url}")
                                # 3. Download and save the image
                                async with session.get(image_url) as img_response:
                                    if img_response.status == 200:
                                        image_bytes = await img_response.read()
                                        logger.info(f"Image downloaded successfully ({len(image_bytes)} bytes).")

                                        filename = f"character_{creative_id}.jpg"
                                        oss_filename = push_to_oss(date_prefix, image_bytes, filename)
                                        return f"{settings.oss_http_prefix}/{oss_filename}"
                                    else:
                                        error_text = await img_response.text()
                                        logger.error(f"Failed to download image: HTTP {img_response.status}, Response: {error_text}")
                                        return default_image
                        else:
                            logger.error("Task succeeded but no image URL found in results.")
                            return default_image
                        break  # Exit polling loop on success

                    elif task_status == "FAILED":
                        error_code = status_result.get("output", {}).get("code", "UnknownCode")
                        error_message = status_result.get("output", {}).get("message", "Unknown error.")
                        logger.error(f"Task failed. Code: {error_code}, Message: {error_message}")
                        return default_image

                    elif task_status in ["PENDING", "RUNNING"]:
                        # Continue polling
                        pass
                    else:  # UNKNOWN or other unexpected status
                        logger.error(f"Unknown or unexpected task status: {task_status}")
                        return default_image

            else:  # Loop finished without success or explicit failure (timeout)
                logger.error("Polling timed out.")
                return default_image

    except aiohttp.ClientError as e:
        logger.error(f"Network error during image generation: {e}")
        traceback.print_exc()
        return default_image
    except asyncio.TimeoutError:
        logger.error("Asyncio operation timed out.")
        traceback.print_exc()
        return default_image
    except Exception as e:
        logger.error(f"An unexpected error occurred: {traceback.format_exc()}")
        traceback.print_exc()
        return default_image

