{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/app/favicon--route-entry.js"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nconst contentType = \"image/x-icon\"\nconst cacheControl = \"public, max-age=0, must-revalidate\"\nconst buffer = Buffer.from(\"iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAAAXNSR0IArs4c6QAABT5JREFUeJztne1RGzEQht9k8j/XQZwO3AHXAe4g7oB0AB2EDqAD6AA6IKnAdGBTAfkhezg8d6eP09dq32dGQ2Yczjr0arVarSSAEEJaoS9dAVKOOwDvALalK0Lyc2r8U6EIFNHjc+NTBArZgiJQD0WgiClvnyJQgM3bpwgaxtXbnxLBJkMdSSJ8vf0xEezSV5OkxMe8j/3f1zzVJClxEQGHgMaZEwGdQCVMNTQbXxEuImDjV0IP4AFAF/j5FHMiYONXQA/gCR+NcuP5uQv3YONXyw0+N8wen3u57XMb3fF3hs8IERFJRAfggOkGsn1u41xAB/gPIyQxqawAe78QUlkB9n5BxLYC7P3CGOvlMQt7fwBfM37XAcBtwuffHr+DVExKK8DeL4QNzLJsbAGQAL6UrsACzhtd8rsUI6cPQCqEAlAOBaAcCkA5FIByavWcL44/+4mfobyB8YJPfCv0vaka2MbfxM8XRwkBlAzaPBf8bnLkGekWhGyLRasM7yeKEk5gqQWbG3BnUBVM7fFLWR6zvBlx5hH5zP7vTO9EPBlL5Y7V6M8wJn+d7W1IEFvEswYkgFoCQR1Mb+2PZQ3gu+czankXUdT8R/Pt1TW/S7VwLUA5FIByKADlUADKoQCUQwEohwJQTksC4G0hAbQkAK71B0ABlOeudAVqxXcxSGq61zvMIVlMVh2wRtiKoEQrcKr7S+mK1MQtwgRwX6KyCxnWP+X5CaLYITwvQFoSyHn91c9mQs3/qTzlr/IipNc/OqHmf1ius9c6nFZ8mWgsMf/DImV6NVZ3taedLTX/EkUwVu9sU9raAkGxD3jewkyvLiM/NxbSHFYrFzDj7zU+NoD6EMv8j5UdgCvUFXCZO+peFBtMN94DzB/e5tjENv9z5QHhIo3J3N4IMfhc3bID8AfjJjmG9x9anmAEcYl8FmJui5yYbexLe+3QOqQ0/77lBUaov5BmStZZ3lfMukauPX6lyw5mRnGF5Y5bD7vYxYSEa+q1OcseH36EiyDWMNbk/OrbqZLtupulu2kO8N/CRebJeo7R0jiAGGdFEFnN/1IBiHFWhPAGYQLgyRvxeINxEMXdeeB69HsPE/hIeWuI5CL2nkPXAM4p0aGDedm/jr/XejlA+K3mrsGgsbN61tBtFW5Q19pEMC7DwJxzo80q3KOxpA+XYcB1xpDq8KjS5RUN9fhzXIaBvcfzWrMEKuIlLsOAq/o7tCWCneN7ZydmRpBLTMB1IeUAM2v4F16dqmhqvJ/CZRh4gf/V8K1YAhW4DAN7+C2pdmhj2VmFFXANCu3hH/zokebCyVxFxY4f3wwh3/Bnh7KpY0uKmkOrfXIE3wO/4xQ4kjQ0VLn7N9Xxqj1M47gkiyytQwcznKxgLFB3/PePhc9NwU8ourRiDTcPXgoxrrpRt/Gzg/0PJ4UV4ixaiV75C6WJDRAwjbdUAL7T4GaYcg6lEWOxSq0ITilPkgXQIU48IiQW0gTnzqFE1oiXxCI2FWwJQ+dQKjFFIOEcgyRIPNVrSEwRPKDRZJHWWSPeGoXvaimphJgrlmpnCC2wQZwhQe0MoQU6xEtsVTlDaIUV4giBCKeDSQcPHRpIQ2zg7yySBulgMoRaWi4ngdhiCEnjArWdFKoR236KpDEBCqA8NgGoSCfXztwModptZSQetpmBij0FmrHtp+D6gAJsUcM9aAmaZgW3wNATKIRm8Vk7IA3isx2eNIprmhlpmLnLJCgAJdh2WRMFjG2koQCUscK4Y0gUMZZnSBQyzDomSjntQSCEEEIIIYSQAP4DE/E+Zauw6CcAAAAASUVORK5CYII=\", 'base64')\n\nif (false || false) {\n    const fileSizeInMB = buffer.byteLength / 1024 / 1024\n    if (fileSizeInMB > 8) {\n        throw new Error('File size for Open Graph image \"[project]/src/app/favicon.ico\" exceeds 8MB. ' +\n        `(Current: ${fileSizeInMB.toFixed(2)}MB)\\n` +\n        'Read more: https://nextjs.org/docs/app/api-reference/file-conventions/metadata/opengraph-image#image-files-jpg-png-gif'\n        )\n    }\n}\n\nexport function GET() {\n    return new NextResponse(buffer, {\n        headers: {\n            'Content-Type': contentType,\n            'Cache-Control': cacheControl,\n        },\n    })\n}\n\nexport const dynamic = 'force-static'\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,SAAS,OAAO,IAAI,CAAC,g2DAAg2D;AAE33D,uCAAoB;;AAQpB;AAEO,SAAS;IACZ,OAAO,IAAI,uOAAA,CAAA,eAAY,CAAC,QAAQ;QAC5B,SAAS;YACL,gBAAgB;YAChB,iBAAiB;QACrB;IACJ;AACJ;AAEO,MAAM,UAAU", "debugId": null}}]}