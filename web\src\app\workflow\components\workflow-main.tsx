"use client";

import { useState, useEffect } from "react";
import { CreativesList } from "./creatives-list";
import { CreativeDetail } from "./creative-detail";
import { useVideoAPI } from "../hooks/use-video-api";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { Loader2 } from "lucide-react";

export interface Creative {
  id: number;
  creative: string;
  art_style?: string;
  scene_cnt?: number;
  character_prompt?: string;
  character_image?: string;
  update_time: string;
}

export interface Scene {
  id: number;
  creative_id: number;
  scene: string;
  prompt: string;
  image?: string;
  update_time: string;
}

export interface VideoItem {
  id: number;
  scene_id: number;
  prompt?: string;
  video?: string;
  update_time: string;
}

export interface Subtitle {
  id: number;
  video_id: number;
  subtitle?: string;
  audio?: string;
  update_time: string;
}

export default function WorkflowMain() {
  const [selectedCreative, setSelectedCreative] = useState<Creative | null>(null);
  const [creatives, setCreatives] = useState<Creative[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { fetchCreatives } = useVideoAPI();

  // 加载创意列表
  useEffect(() => {
    const loadCreatives = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await fetchCreatives();
        setCreatives(data.items || []);
      } catch (err) {
        console.error("Failed to load creatives:", err);
        setError("Failed to load creatives. Please check your API connection.");
      } finally {
        setLoading(false);
      }
    };

    loadCreatives();
  }, [fetchCreatives]);

  // 处理创意选择
  const handleCreativeSelect = (creative: Creative) => {
    setSelectedCreative(creative);
  };

  // 处理返回列表
  const handleBackToList = () => {
    setSelectedCreative(null);
  };

  // 处理创意创建成功
  const handleCreativeCreated = async () => {
    try {
      setError(null);
      const data = await fetchCreatives();
      setCreatives(data.items || []);
    } catch (err) {
      console.error("Failed to refresh creatives after creation:", err);
      setError("Failed to refresh creatives list.");
    }
  };

  // 处理创意更新成功
  const handleCreativeUpdated = (updatedCreative: Creative) => {
    // 更新创意列表中的对应项
    setCreatives(prev =>
      prev.map(creative =>
        creative.id === updatedCreative.id ? updatedCreative : creative
      )
    );

    // 更新当前选中的创意
    setSelectedCreative(updatedCreative);
  };

  if (loading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="text-lg">Loading creatives...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-full w-full items-center justify-center p-6">
        <Alert className="max-w-md">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="h-full w-full">
      {selectedCreative ? (
        <CreativeDetail
          creative={selectedCreative}
          onBack={handleBackToList}
          onCreativeUpdated={handleCreativeUpdated}
        />
      ) : (
        <CreativesList
          creatives={creatives}
          onCreativeSelect={handleCreativeSelect}
          onRefresh={() => window.location.reload()}
          onCreativeCreated={handleCreativeCreated}
        />
      )}
    </div>
  );
}
