"use client";

import { useEffect, useState } from "react";
import { Progress } from "~/components/ui/progress";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { CheckCircle, XCircle, Loader2 } from "lucide-react";
import { env } from "~/env";

// API 基础 URL
const getApiUrl = () => {
  return env.NEXT_PUBLIC_API_URL || "http://localhost:8000/api";
};

interface ProgressData {
  step: number;
  total: number;
  message: string;
  timestamp: string;
  type?: "complete";
  error?: string;
}

interface ProgressBarProps {
  taskId: string | null;
  onComplete?: () => void;
  onError?: (error: string) => void;
}

export function ProgressBar({ taskId, onComplete, onError }: ProgressBarProps) {
  const [progress, setProgress] = useState<ProgressData | null>(null);
  const [isComplete, setIsComplete] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!taskId) {
      setProgress(null);
      setIsComplete(false);
      setError(null);
      return;
    }

    let eventSource: EventSource | null = null;
    let timeoutId: NodeJS.Timeout | null = null;

    const connectToProgress = () => {
      try {
        eventSource = new EventSource(`${getApiUrl()}/video/progress/${taskId}`);

        eventSource.onmessage = (event) => {
          try {
            const data: ProgressData = JSON.parse(event.data);
            setProgress(data);

            // 检查是否完成
            if (data.type === "complete") {
              setIsComplete(true);
              eventSource?.close();

              // 延迟调用完成回调，让用户看到完成状态
              setTimeout(() => {
                onComplete?.();
              }, 1000);
            }

            // 检查是否有错误
            if (data.error) {
              setError(data.error);
              eventSource?.close();
              onError?.(data.error);
            }
          } catch (err) {
            console.error("Failed to parse progress data:", err);
          }
        };

        eventSource.onerror = (event) => {
          console.error("EventSource error:", event);
          eventSource?.close();

          // 如果还没有完成，设置超时重连
          if (!isComplete && !error) {
            timeoutId = setTimeout(() => {
              connectToProgress();
            }, 2000);
          }
        };

        // 设置总体超时（10分钟）
        timeoutId = setTimeout(() => {
          eventSource?.close();
          setError("任务执行超时");
          onError?.("任务执行超时");
        }, 10 * 60 * 1000);

      } catch (err) {
        console.error("Failed to create EventSource:", err);
        setError("无法连接到进度服务");
        onError?.("无法连接到进度服务");
      }
    };

    connectToProgress();

    // 清理函数
    return () => {
      eventSource?.close();
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [taskId, onComplete, onError, isComplete, error]);

  // 如果没有任务ID，不显示进度条
  if (!taskId) {
    return null;
  }

  // 计算进度百分比
  const progressPercentage = progress ? Math.round((progress.step / progress.total) * 100) : 0;

  return (
    <div className="space-y-3 p-4 border rounded-lg bg-muted/50">
      {/* 进度条 */}
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span className="font-medium">
            {isComplete ? "任务完成" : error ? "任务失败" : "正在处理..."}
          </span>
          <span className="text-muted-foreground">
            {progress ? `${progress.step}/${progress.total}` : "0/1"}
          </span>
        </div>

        <Progress
          value={progressPercentage}
          className="h-2"
        />

        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>{progressPercentage}%</span>
          <span>任务ID: {taskId.slice(0, 8)}...</span>
        </div>
      </div>

      {/* 状态消息 */}
      <div className="flex items-center gap-2 text-sm">
        {isComplete ? (
          <CheckCircle className="h-4 w-4 text-green-500" />
        ) : error ? (
          <XCircle className="h-4 w-4 text-red-500" />
        ) : (
          <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
        )}

        <span className={`${
          isComplete ? "text-green-700" :
          error ? "text-red-700" :
          "text-blue-700"
        }`}>
          {progress?.message || "等待任务开始..."}
        </span>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* 完成提示 */}
      {isComplete && !error && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            任务已成功完成！页面将自动刷新...
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
