module.exports = {

"[project]/.next-internal/server/app/favicon.ico/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/favicon--route-entry.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "dynamic": (()=>dynamic)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/server.js [app-rsc] (ecmascript)");
;
const contentType = "image/x-icon";
const cacheControl = "public, max-age=0, must-revalidate";
const buffer = Buffer.from("iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAAAXNSR0IArs4c6QAABT5JREFUeJztne1RGzEQht9k8j/XQZwO3AHXAe4g7oB0AB2EDqAD6AA6IKnAdGBTAfkhezg8d6eP09dq32dGQ2Yczjr0arVarSSAEEJaoS9dAVKOOwDvALalK0Lyc2r8U6EIFNHjc+NTBArZgiJQD0WgiClvnyJQgM3bpwgaxtXbnxLBJkMdSSJ8vf0xEezSV5OkxMe8j/3f1zzVJClxEQGHgMaZEwGdQCVMNTQbXxEuImDjV0IP4AFAF/j5FHMiYONXQA/gCR+NcuP5uQv3YONXyw0+N8wen3u57XMb3fF3hs8IERFJRAfggOkGsn1u41xAB/gPIyQxqawAe78QUlkB9n5BxLYC7P3CGOvlMQt7fwBfM37XAcBtwuffHr+DVExKK8DeL4QNzLJsbAGQAL6UrsACzhtd8rsUI6cPQCqEAlAOBaAcCkA5FIByavWcL44/+4mfobyB8YJPfCv0vaka2MbfxM8XRwkBlAzaPBf8bnLkGekWhGyLRasM7yeKEk5gqQWbG3BnUBVM7fFLWR6zvBlx5hH5zP7vTO9EPBlL5Y7V6M8wJn+d7W1IEFvEswYkgFoCQR1Mb+2PZQ3gu+czankXUdT8R/Pt1TW/S7VwLUA5FIByKADlUADKoQCUQwEohwJQTksC4G0hAbQkAK71B0ABlOeudAVqxXcxSGq61zvMIVlMVh2wRtiKoEQrcKr7S+mK1MQtwgRwX6KyCxnWP+X5CaLYITwvQFoSyHn91c9mQs3/qTzlr/IipNc/OqHmf1ius9c6nFZ8mWgsMf/DImV6NVZ3taedLTX/EkUwVu9sU9raAkGxD3jewkyvLiM/NxbSHFYrFzDj7zU+NoD6EMv8j5UdgCvUFXCZO+peFBtMN94DzB/e5tjENv9z5QHhIo3J3N4IMfhc3bID8AfjJjmG9x9anmAEcYl8FmJui5yYbexLe+3QOqQ0/77lBUaov5BmStZZ3lfMukauPX6lyw5mRnGF5Y5bD7vYxYSEa+q1OcseH36EiyDWMNbk/OrbqZLtupulu2kO8N/CRebJeo7R0jiAGGdFEFnN/1IBiHFWhPAGYQLgyRvxeINxEMXdeeB69HsPE/hIeWuI5CL2nkPXAM4p0aGDedm/jr/XejlA+K3mrsGgsbN61tBtFW5Q19pEMC7DwJxzo80q3KOxpA+XYcB1xpDq8KjS5RUN9fhzXIaBvcfzWrMEKuIlLsOAq/o7tCWCneN7ZydmRpBLTMB1IeUAM2v4F16dqmhqvJ/CZRh4gf/V8K1YAhW4DAN7+C2pdmhj2VmFFXANCu3hH/zokebCyVxFxY4f3wwh3/Bnh7KpY0uKmkOrfXIE3wO/4xQ4kjQ0VLn7N9Xxqj1M47gkiyytQwcznKxgLFB3/PePhc9NwU8ourRiDTcPXgoxrrpRt/Gzg/0PJ4UV4ixaiV75C6WJDRAwjbdUAL7T4GaYcg6lEWOxSq0ITilPkgXQIU48IiQW0gTnzqFE1oiXxCI2FWwJQ+dQKjFFIOEcgyRIPNVrSEwRPKDRZJHWWSPeGoXvaimphJgrlmpnCC2wQZwhQe0MoQU6xEtsVTlDaIUV4giBCKeDSQcPHRpIQ2zg7yySBulgMoRaWi4ngdhiCEnjArWdFKoR236KpDEBCqA8NgGoSCfXztwModptZSQetpmBij0FmrHtp+D6gAJsUcM9aAmaZgW3wNATKIRm8Vk7IA3isx2eNIprmhlpmLnLJCgAJdh2WRMFjG2koQCUscK4Y0gUMZZnSBQyzDomSjntQSCEEEIIIYSQAP4DE/E+Zauw6CcAAAAASUVORK5CYII=", 'base64');
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
function GET() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NextResponse"](buffer, {
        headers: {
            'Content-Type': contentType,
            'Cache-Control': cacheControl
        }
    });
}
const dynamic = 'force-static';
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__48b32c58._.js.map