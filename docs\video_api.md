# 视频流程 REST API 文档

本文档描述了基于 `docs/databases.sql` 中定义的数据库表结构实现的视频流程 REST API。

## 数据库表结构

API 基于以下四个表：

1. **video_creative** - 视频创意表
2. **video_prompts** - 视频提示词表
3. **video_scenes** - 视频场景表
4. **video_subtitles** - 视频字幕表

## API 端点

所有API端点都以 `/api/video` 为前缀。

### 1. 视频创意 (Video Creative) API

#### 创建视频创意
```http
POST /api/video/creatives
Content-Type: application/json

{
    "creative": "一个关于未来城市的科幻短片",
    "art_style": "赛博朋克"
}
```

#### 获取视频创意列表（分页）
```http
GET /api/video/creatives?page=1&size=10
```

#### 获取单个视频创意
```http
GET /api/video/creatives/{creative_id}
```

#### 更新视频创意
```http
PUT /api/video/creatives/{creative_id}
Content-Type: application/json

{
    "art_style": "写实风格"
}
```

#### 删除视频创意
```http
DELETE /api/video/creatives/{creative_id}
```

### 2. 视频提示词 (Video Prompts) API

#### 创建视频提示词
```http
POST /api/video/prompts
Content-Type: application/json

{
    "creative_id": 1,
    "scene": "开场场景",
    "prompt": "一个繁华的未来城市，霓虹灯闪烁，飞行汽车穿梭在高楼大厦之间"
}
```

#### 获取视频提示词列表（分页）
```http
GET /api/video/prompts?creative_id=1&page=1&size=10
```

#### 获取单个视频提示词
```http
GET /api/video/prompts/{prompt_id}
```

#### 更新视频提示词
```http
PUT /api/video/prompts/{prompt_id}
Content-Type: application/json

{
    "scene": "修改后的场景描述"
}
```

#### 删除视频提示词
```http
DELETE /api/video/prompts/{prompt_id}
```

### 3. 视频场景 (Video Scenes) API

#### 创建视频场景
```http
POST /api/video/scenes
Content-Type: application/json

{
    "prompt_id": 1,
    "image": "/path/to/scene_image.jpg",
    "video": "/path/to/scene_video.mp4"
}
```

#### 获取视频场景列表（分页）
```http
GET /api/video/scenes?prompt_id=1&page=1&size=10
```

#### 获取单个视频场景
```http
GET /api/video/scenes/{scene_id}
```

#### 更新视频场景
```http
PUT /api/video/scenes/{scene_id}
Content-Type: application/json

{
    "video": "/path/to/updated_video.mp4"
}
```

#### 删除视频场景
```http
DELETE /api/video/scenes/{scene_id}
```

### 4. 视频字幕 (Video Subtitles) API

#### 创建视频字幕
```http
POST /api/video/subtitles
Content-Type: application/json

{
    "scene_id": 1,
    "subtitle": "欢迎来到2077年的新东京",
    "audio": "/path/to/subtitle_audio.mp3"
}
```

#### 获取视频字幕列表（分页）
```http
GET /api/video/subtitles?scene_id=1&page=1&size=10
```

#### 获取单个视频字幕
```http
GET /api/video/subtitles/{subtitle_id}
```

#### 更新视频字幕
```http
PUT /api/video/subtitles/{subtitle_id}
Content-Type: application/json

{
    "subtitle": "修改后的字幕内容"
}
```

#### 删除视频字幕
```http
DELETE /api/video/subtitles/{subtitle_id}
```

## 响应格式

### 成功响应
所有成功的响应都包含相应的数据对象，例如：

```json
{
    "id": 1,
    "creative": "一个关于未来城市的科幻短片",
    "art_style": "赛博朋克",
    "update_time": "2025-01-23T10:30:00"
}
```

### 分页响应
列表API返回分页格式：

```json
{
    "items": [...],
    "total": 100,
    "page": 1,
    "size": 10,
    "pages": 10
}
```

### 错误响应
错误响应格式：

```json
{
    "detail": "错误描述信息"
}
```

## 数据关系

- 一个创意可以有多个提示词
- 一个提示词可以有多个场景
- 一个场景可以有多个字幕
- 删除父级记录会级联删除所有子级记录

## 环境配置

确保在 `.env` 文件中配置了正确的数据库连接信息：

```env
VIDEOFLOW_SERVER=your_mysql_server
VIDEOFLOW_PORT=3306
VIDEOFLOW_USER=your_username
VIDEOFLOW_PWD=your_password
VIDEOFLOW_DB=video_flow
```

## 安装和运行

### 1. 安装依赖

```bash
# 安装新的数据库依赖
pip install sqlalchemy pymysql cryptography
```

### 2. 配置数据库

确保MySQL数据库正在运行，并创建数据库：

```sql
CREATE DATABASE video_flow CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 运行应用

```bash
python server.py
```

应用启动时会自动创建数据库表。

### 4. 访问API文档

启动后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 测试

运行测试脚本：

```bash
python test_video_api.py
```

这将测试所有API端点的基本功能。

## 注意事项

1. 确保数据库连接配置正确
2. 所有外键关系都有级联删除约束
3. 分页查询默认每页10条记录，最大100条
4. 所有时间字段都会自动更新
5. 支持按关联ID筛选子级资源
