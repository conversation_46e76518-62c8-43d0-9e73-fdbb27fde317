"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Plus, Loader2 } from "lucide-react";
import { useVideoAPI } from "../hooks/use-video-api";
import { Alert, AlertDescription } from "~/components/ui/alert";

interface CreateCreativeDialogProps {
  onCreativeCreated: () => void;
}

export function CreateCreativeDialog({ onCreativeCreated }: CreateCreativeDialogProps) {
  const t = useTranslations("workflow.creatives.create");
  const tCreatives = useTranslations("workflow.creatives");
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    creative: "",
    art_style: "",
    scene_cnt: 5,
    character_prompt: "",
  });

  const { createCreative } = useVideoAPI();

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.creative.trim()) {
      setError(t("errorRequired"));
      return;
    }

    if (!formData.character_prompt.trim()) {
      setError("主角提示词不能为空");
      return;
    }

    if (formData.scene_cnt < 1 || formData.scene_cnt > 20) {
      setError("场景数量必须在 1-20 之间");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const data = {
        creative: formData.creative.trim(),
        art_style: formData.art_style.trim() || undefined,
        scene_cnt: formData.scene_cnt,
        character_prompt: formData.character_prompt.trim(),
      };

      await createCreative(data);
      
      // 成功后重置表单并关闭对话框
      setFormData({ creative: "", art_style: "", scene_cnt: 5, character_prompt: "" });
      setOpen(false);
      onCreativeCreated();
    } catch (err) {
      console.error("Failed to create creative:", err);
      setError(t("errorFailed"));
    } finally {
      setLoading(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除错误信息
    if (error) setError(null);
  };

  // 重置表单
  const resetForm = () => {
    setFormData({ creative: "", art_style: "", scene_cnt: 5, character_prompt: "" });
    setError(null);
  };

  return (
    <Dialog open={open} onOpenChange={(newOpen) => {
      setOpen(newOpen);
      if (!newOpen) {
        resetForm();
      }
    }}>
      <DialogTrigger asChild>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          {tCreatives("newCreative")}
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t("title")}</DialogTitle>
          <DialogDescription>
            {t("description")}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 创意描述 */}
          <div className="space-y-2">
            <Label htmlFor="creative">{t("creativeLabel")} {t("required")}</Label>
            <Textarea
              id="creative"
              placeholder={t("creativePlaceholder")}
              value={formData.creative}
              onChange={(e) => handleInputChange("creative", e.target.value)}
              rows={4}
              disabled={loading}
              className="resize-none"
            />
          </div>

          {/* 艺术风格 */}
          <div className="space-y-2">
            <Label htmlFor="art_style">{t("artStyleLabel")}</Label>
            <Input
              id="art_style"
              placeholder={t("artStylePlaceholder")}
              value={formData.art_style}
              onChange={(e) => handleInputChange("art_style", e.target.value)}
              disabled={loading}
            />
          </div>

          {/* 场景数量 */}
          <div className="space-y-2">
            <Label htmlFor="scene_cnt">场景数量 *</Label>
            <Input
              id="scene_cnt"
              type="number"
              min="1"
              max="10"
              placeholder="1-10"
              value={formData.scene_cnt}
              onChange={(e) => handleInputChange("scene_cnt", parseInt(e.target.value) || 5)}
              disabled={loading}
            />
            <p className="text-xs text-muted-foreground">
              设置将创意切分为多少个场景（1-10个）
            </p>
          </div>

          {/* 主角提示词 */}
          <div className="space-y-2">
            <Label htmlFor="character_prompt">主角提示词 *</Label>
            <Textarea
              id="character_prompt"
              placeholder="请描述主角的外观、特征、服装等，如：一个身穿银色太空服的年轻宇航员，有着坚毅的眼神和短发"
              value={formData.character_prompt}
              onChange={(e) => handleInputChange("character_prompt", e.target.value)}
              disabled={loading}
              rows={4}
              className="resize-none"
            />
            <p className="text-xs text-muted-foreground">
              详细描述主角的形象，用于生成主角图片和场景描述
            </p>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              {t("cancel")}
            </Button>
            <Button type="submit" disabled={loading || !formData.creative.trim() || !formData.character_prompt.trim()}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  {t("creating")}
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  {t("create")}
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
