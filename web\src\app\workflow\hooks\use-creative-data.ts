"use client";

import { useState, useEffect, useCallback } from 'react';
import { useVideoAPI } from './use-video-api';
import type { Creative, Scene, VideoItem, Subtitle } from "../components/workflow-main";

interface CreativeData {
  creative: Creative;
  scenes: Scene[];
  videos: VideoItem[];
  subtitles: Subtitle[];
}

interface UseCreativeDataReturn {
  data: CreativeData | null;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

export function useCreativeData(creativeId: number): UseCreativeDataReturn {
  const [data, setData] = useState<CreativeData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { fetchCreativeComplete } = useVideoAPI();

  const loadData = useCallback(async () => {
    if (!creativeId) return;

    try {
      setLoading(true);
      setError(null);

      const completeData = await fetchCreativeComplete(creativeId);
      setData(completeData);
    } catch (err) {
      console.error("Failed to load creative data:", err);
      setError("Failed to load creative data. Please try again.");
    } finally {
      setLoading(false);
    }
  }, [creativeId, fetchCreativeComplete]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const refresh = useCallback(async () => {
    await loadData();
  }, [loadData]);

  return {
    data,
    loading,
    error,
    refresh,
  };
}

// 辅助 hooks 用于获取特定类型的数据

export function useCreativeScenes(creativeId: number) {
  const { data, loading, error, refresh } = useCreativeData(creativeId);
  
  return {
    scenes: data?.scenes || [],
    loading,
    error,
    refresh,
  };
}

export function useCreativeVideos(creativeId: number) {
  const { data, loading, error, refresh } = useCreativeData(creativeId);

  return {
    videos: data?.videos || [],
    loading,
    error,
    refresh,
  };
}

export function useCreativeSubtitles(creativeId: number) {
  const { data, loading, error, refresh } = useCreativeData(creativeId);
  
  return {
    subtitles: data?.subtitles || [],
    loading,
    error,
    refresh,
  };
}

// 根据场景ID筛选视频
export function useSceneVideos(creativeId: number, sceneId?: number) {
  const { videos, loading, error, refresh } = useCreativeVideos(creativeId);

  const filteredVideos = sceneId
    ? videos.filter(video => video.scene_id === sceneId)
    : videos;

  return {
    videos: filteredVideos,
    loading,
    error,
    refresh,
  };
}

// 根据视频ID筛选字幕
export function useVideoSubtitles(creativeId: number, videoId?: number) {
  const { subtitles, loading, error, refresh } = useCreativeSubtitles(creativeId);

  const filteredSubtitles = videoId
    ? subtitles.filter(subtitle => subtitle.video_id === videoId)
    : subtitles;

  return {
    subtitles: filteredSubtitles,
    loading,
    error,
    refresh,
  };
}

// 根据场景ID获取相关的视频和字幕
export function useSceneData(creativeId: number, sceneId: number) {
  const { data, loading, error, refresh } = useCreativeData(creativeId);

  const sceneVideos = data?.videos.filter(video => video.scene_id === sceneId) || [];
  const videoIds = sceneVideos.map(video => video.id);
  const sceneSubtitles = data?.subtitles.filter(subtitle =>
    videoIds.includes(subtitle.video_id)
  ) || [];

  return {
    scene: data?.scenes.find(scene => scene.id === sceneId) || null,
    videos: sceneVideos,
    subtitles: sceneSubtitles,
    loading,
    error,
    refresh,
  };
}

export default useCreativeData;
