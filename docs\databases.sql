use video_flow;

-- 1. 创建 video_creative 表
CREATE TABLE IF NOT EXISTS `video_creative` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID，自增',
    `creative` TEXT NOT NULL COMMENT '创意描述',
    `art_style` VARCHAR(128) COMMENT '艺术风格（如：写实、卡通等）',
	`scene_cnt` INT(10) NULL DEFAULT '5' COMMENT '一个创意切分的场景数',
    `character_prompt` TEXT NULL DEFAULT NULL COMMENT '主角的提示词' COLLATE 'utf8mb4_general_ci',
	`character_image` VARCHAR(256) NULL DEFAULT NULL COMMENT '主角的图片地址' COLLATE 'utf8mb4_general_ci',
    `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录创建或更新的时间'
) COMMENT='存储视频创意描述';

---

-- 2. 创建 video_scenes 表
CREATE TABLE IF NOT EXISTS `video_scenes` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID，自增',
    `creative_id` INT NOT NULL COMMENT '关联的创意ID',
    `scene` TEXT NOT NULL COMMENT '场景描述或名称',
    `prompt` TEXT NOT NULL COMMENT '生成场景图像或视频的提示词',
    `image` VARCHAR(256) COMMENT '生成的图片存储路径或URL',
    `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录创建或更新的时间',
    -- 外键约束，当关联的 creative 被删除时，对应的 scenes 也删除
    FOREIGN KEY (`creative_id`) REFERENCES `video_creative`(`id`) ON DELETE CASCADE
) COMMENT='存储视频创意下的各个场景提示词';

---

-- 3. 创建 video_videos 表
CREATE TABLE IF NOT EXISTS `video_videos` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID，自增',
    `scene_id` INT NOT NULL COMMENT '关联的场景ID',
    `prompt` TEXT COMMENT '图生视频的提示词',
    `video` VARCHAR(512) COMMENT '生成的视频存储路径或URL',
    `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录创建或更新的时间',
    -- 外键约束，当关联的 scene 被删除时，对应的 videos 也删除
    FOREIGN KEY (`scene_id`) REFERENCES `video_scenes`(`id`) ON DELETE CASCADE
) COMMENT='存储由场景生成的具体视频';

---

-- 4. 创建 video_subtitles 表
CREATE TABLE IF NOT EXISTS `video_subtitles` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID，自增',
    `video_id` INT NOT NULL COMMENT '关联的视频ID',
    `subtitle` TEXT COMMENT '字幕文本内容',
    `audio` VARCHAR(512) COMMENT '对应字幕的音频文件存储路径或URL',
    `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录创建或更新的时间',
    -- 外键约束，当关联的 video 被删除时，对应的 subtitles 也删除
    FOREIGN KEY (`video_id`) REFERENCES `video_videos`(`id`) ON DELETE CASCADE
) COMMENT='存储视频的字幕和音频信息';
