import asyncio
import base64
import json
import logging
import os
from typing import Annotated, List, cast, Optional
from math import ceil

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import Response, StreamingResponse
from sqlalchemy.orm import Session
from sqlalchemy import func

from .database import get_db
from .models import VideoCreative, VideoScene, VideoVideo, VideoSubtitle
from .schemas import (
    VideoCreativeCreate, VideoCreativeUpdate, VideoCreativeResponse,
    VideoSceneCreate, VideoSceneUpdate, VideoSceneResponse,
    VideoVideoCreate, VideoVideoUpdate, VideoVideoResponse,
    VideoSubtitleCreate, VideoSubtitleUpdate, VideoSubtitleResponse,
    PaginatedResponse, GenerateRequest
)

from .llm_api import delete_from_oss 
from .task_queue import task_queue, progress_store, TaskType

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/video", tags=["video"])

# ==================== 进度管理 ====================

@router.get("/progress/{task_id}")
async def get_progress_stream(task_id: str):
    """获取任务进度的 Server-Sent Events 流"""
    async def event_stream():
        try:
            while True:
                if task_id in progress_store:
                    progress_data = progress_store[task_id]
                    yield f"data: {json.dumps(progress_data)}\n\n"

                    # 如果任务完成，发送完成事件并结束流
                    if progress_data.get("step") >= progress_data.get("total", 1):
                        yield f"data: {json.dumps({'type': 'complete'})}\n\n"
                        break
                else:
                    # 如果任务不存在，发送初始状态
                    yield f"data: {json.dumps({'step': 0, 'total': 4, 'message': '等待开始...'})}\n\n"

                await asyncio.sleep(0.5)  # 每0.5秒检查一次
        except Exception as e:
            logger.error(f"Progress stream error: {e}")
            yield f"data: {json.dumps({'error': str(e)})}\n\n"

    return StreamingResponse(
        event_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )


# ==================== VideoCreative CRUD ====================

@router.get("/creatives/{creative_id}/complete", summary="获取创意完整信息")
async def get_creative_complete(
    creative_id: int,
    db: Session = Depends(get_db)
):
    """获取创意及其所有相关的场景、图片、字幕信息"""
    # 检查创意是否存在
    creative = db.query(VideoCreative).filter(VideoCreative.id == creative_id).first()
    if not creative:
        raise HTTPException(status_code=404, detail="视频创意不存在")

    # 获取所有场景
    scenes = db.query(VideoScene).filter(VideoScene.creative_id == creative_id).all()

    # 获取所有视频（通过场景关联）
    scene_ids = [scene.id for scene in scenes]
    videos = []
    if scene_ids:
        videos = db.query(VideoVideo).filter(VideoVideo.scene_id.in_(scene_ids)).all()

    # 获取所有字幕（通过视频关联）
    video_ids = [video.id for video in videos]
    subtitles = []
    if video_ids:
        subtitles = db.query(VideoSubtitle).filter(VideoSubtitle.video_id.in_(video_ids)).all()

    return {
        "creative": VideoCreativeResponse.model_validate(creative).model_dump(),
        "scenes": [VideoSceneResponse.model_validate(scene).model_dump() for scene in scenes],
        "videos": [VideoVideoResponse.model_validate(video).model_dump() for video in videos],
        "subtitles": [VideoSubtitleResponse.model_validate(subtitle).model_dump() for subtitle in subtitles]
    }

@router.post("/creatives", response_model=VideoCreativeResponse, summary="创建视频创意")
async def create_creative(
    creative: VideoCreativeCreate,
    db: Session = Depends(get_db)
):
    """创建新的视频创意"""
    db_creative = VideoCreative(**creative.model_dump())
    db.add(db_creative)
    db.commit()
    db.refresh(db_creative)
    return db_creative


@router.get("/creatives", response_model=PaginatedResponse, summary="获取视频创意列表")
async def get_creatives(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    db: Session = Depends(get_db)
):
    """获取视频创意列表（分页）"""
    offset = (page - 1) * size

    # 获取总数
    total = db.query(func.count(VideoCreative.id)).scalar()

    # 获取数据
    creatives = db.query(VideoCreative).offset(offset).limit(size).all()

    # 计算总页数
    pages = ceil(total / size) if total > 0 else 0

    return PaginatedResponse(
        items=[VideoCreativeResponse.model_validate(creative).model_dump() for creative in creatives],
        total=total,
        page=page,
        size=size,
        pages=pages
    )

@router.get("/creatives/{creative_id}", response_model=VideoCreativeResponse, summary="获取单个视频创意")
async def get_creative(
    creative_id: int,
    db: Session = Depends(get_db)
):
    """根据ID获取视频创意"""
    creative = db.query(VideoCreative).filter(VideoCreative.id == creative_id).first()
    if not creative:
        raise HTTPException(status_code=404, detail="视频创意不存在")
    return creative


@router.put("/creatives/{creative_id}", response_model=VideoCreativeResponse, summary="更新视频创意")
async def update_creative(
    creative_id: int,
    creative_update: VideoCreativeUpdate,
    db: Session = Depends(get_db)
):
    """更新视频创意"""
    creative = db.query(VideoCreative).filter(VideoCreative.id == creative_id).first()
    if not creative:
        raise HTTPException(status_code=404, detail="视频创意不存在")

    # 更新字段
    update_data = creative_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(creative, field, value)

    db.commit()
    db.refresh(creative)
    return creative


@router.delete("/creatives/{creative_id}", summary="删除视频创意")
async def delete_creative(
    creative_id: int,
    db: Session = Depends(get_db)
):
    """删除视频创意"""
    creative = db.query(VideoCreative).filter(VideoCreative.id == creative_id).first()
    if not creative:
        raise HTTPException(status_code=404, detail="视频创意不存在")

    db.delete(creative)
    db.commit()
    return {"message": "视频创意删除成功"}


# ==================== VideoScene CRUD ====================

@router.post("/scenes", response_model=VideoSceneResponse, summary="创建视频场景")
async def create_scene(
    scene: VideoSceneCreate,
    db: Session = Depends(get_db)
):
    """创建新的视频场景"""
    # 检查关联的创意是否存在
    creative = db.query(VideoCreative).filter(VideoCreative.id == scene.creative_id).first()
    if not creative:
        raise HTTPException(status_code=404, detail="关联的视频创意不存在")

    db_scene = VideoScene(**scene.model_dump())
    db.add(db_scene)
    db.commit()
    db.refresh(db_scene)
    return db_scene


@router.get("/scenes", response_model=PaginatedResponse, summary="获取视频场景列表")
async def get_scenes(
    creative_id: Optional[int] = Query(None, description="筛选指定创意的场景"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    db: Session = Depends(get_db)
):
    """获取视频场景列表（分页）"""
    offset = (page - 1) * size

    # 构建查询
    query = db.query(VideoScene)
    if creative_id:
        query = query.filter(VideoScene.creative_id == creative_id)

    # 获取总数
    total = query.count()

    # 获取数据
    scenes = query.offset(offset).limit(size).all()

    # 计算总页数
    pages = ceil(total / size) if total > 0 else 0

    return PaginatedResponse(
        items=[VideoSceneResponse.model_validate(scene).model_dump() for scene in scenes],
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/scenes/{scene_id}", response_model=VideoSceneResponse, summary="获取单个视频场景")
async def get_scene(
    scene_id: int,
    db: Session = Depends(get_db)
):
    """根据ID获取视频场景"""
    scene = db.query(VideoScene).filter(VideoScene.id == scene_id).first()
    if not scene:
        raise HTTPException(status_code=404, detail="视频场景不存在")
    return scene


@router.put("/scenes/{scene_id}", response_model=VideoSceneResponse, summary="更新视频场景")
async def update_scene(
    scene_id: int,
    scene_update: VideoSceneUpdate,
    db: Session = Depends(get_db)
):
    """更新视频场景"""
    scene = db.query(VideoScene).filter(VideoScene.id == scene_id).first()
    if not scene:
        raise HTTPException(status_code=404, detail="视频场景不存在")

    # 如果要更新creative_id，检查新的创意是否存在
    update_data = scene_update.model_dump(exclude_unset=True)
    if 'creative_id' in update_data:
        creative = db.query(VideoCreative).filter(VideoCreative.id == update_data['creative_id']).first()
        if not creative:
            raise HTTPException(status_code=404, detail="关联的视频创意不存在")

    # 更新字段
    for field, value in update_data.items():
        setattr(scene, field, value)

    db.commit()
    db.refresh(scene)
    return scene


@router.delete("/scenes/{scene_id}", summary="删除视频场景")
async def delete_scene(
    scene_id: int,
    db: Session = Depends(get_db)
):
    """删除视频场景（级联删除相关视频和字幕）"""
    scene = db.query(VideoScene).filter(VideoScene.id == scene_id).first()
    if not scene:
        raise HTTPException(status_code=404, detail="视频场景不存在")

    # 获取所有相关的视频和字幕，用于删除文件
    videos = db.query(VideoVideo).filter(VideoVideo.scene_id == scene_id).all()
    subtitles = db.query(VideoSubtitle).join(VideoVideo).filter(VideoVideo.scene_id == scene_id).all()

    # 删除相关文件
    try:
        # 删除场景图片
        if scene.image:
            delete_from_oss(scene.image)

        # 删除视频文件
        for video in videos:
            if video.video:
                delete_from_oss(video.video)

        # 删除字幕音频文件
        for subtitle in subtitles:
            if subtitle.audio:
                delete_from_oss(subtitle.audio)

    except Exception as e:
        logger.warning(f"删除文件时出错: {str(e)}")
        # 继续删除数据库记录，即使文件删除失败

    # 删除数据库记录（会级联删除相关的视频和字幕）
    db.delete(scene)
    db.commit()
    return {"message": "视频场景及相关内容删除成功"}


# ==================== VideoVideo CRUD ====================

@router.post("/videos", response_model=VideoVideoResponse, summary="创建视频")
async def create_video(
    video: VideoVideoCreate,
    db: Session = Depends(get_db)
):
    """创建新的视频"""
    # 检查关联的场景是否存在
    scene = db.query(VideoScene).filter(VideoScene.id == video.scene_id).first()
    if not scene:
        raise HTTPException(status_code=404, detail="关联的视频场景不存在")

    db_video = VideoVideo(**video.model_dump())
    db.add(db_video)
    db.commit()
    db.refresh(db_video)
    return db_video


@router.get("/videos", response_model=PaginatedResponse, summary="获取视频列表")
async def get_videos(
    creative_id: Optional[int] = Query(None, description="筛选指定创意的视频"),
    scene_id: Optional[int] = Query(None, description="筛选指定场景的视频"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    db: Session = Depends(get_db)
):
    """获取视频列表（分页）"""
    offset = (page - 1) * size

    # 构建查询
    query = db.query(VideoVideo)

    # 根据创意ID筛选（通过场景关联）
    if creative_id:
        query = query.join(VideoScene).filter(VideoScene.creative_id == creative_id)

    # 根据场景ID筛选
    if scene_id:
        query = query.filter(VideoVideo.scene_id == scene_id)

    # 获取总数
    total = query.count()

    # 获取数据
    videos = query.offset(offset).limit(size).all()

    # 计算总页数
    pages = ceil(total / size) if total > 0 else 0

    return PaginatedResponse(
        items=[VideoVideoResponse.model_validate(video).model_dump() for video in videos],
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/videos/{video_id}", response_model=VideoVideoResponse, summary="获取单个视频")
async def get_video(
    video_id: int,
    db: Session = Depends(get_db)
):
    """根据ID获取视频"""
    video = db.query(VideoVideo).filter(VideoVideo.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="视频不存在")
    return video


@router.put("/videos/{video_id}", response_model=VideoVideoResponse, summary="更新视频")
async def update_video(
    video_id: int,
    video_update: VideoVideoUpdate,
    db: Session = Depends(get_db)
):
    """更新视频"""
    video = db.query(VideoVideo).filter(VideoVideo.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="视频不存在")

    # 如果要更新scene_id，检查新的场景是否存在
    update_data = video_update.model_dump(exclude_unset=True)
    if 'scene_id' in update_data:
        scene = db.query(VideoScene).filter(VideoScene.id == update_data['scene_id']).first()
        if not scene:
            raise HTTPException(status_code=404, detail="关联的视频场景不存在")

    # 更新字段
    for field, value in update_data.items():
        setattr(video, field, value)

    db.commit()
    db.refresh(video)
    return video


@router.delete("/videos/{video_id}", summary="删除视频")
async def delete_video(
    video_id: int,
    db: Session = Depends(get_db)
):
    """删除视频"""
    video = db.query(VideoVideo).filter(VideoVideo.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="视频不存在")

    db.delete(video)
    db.commit()
    return {"message": "视频删除成功"}


# ==================== VideoSubtitle CRUD ====================

@router.post("/subtitles", response_model=VideoSubtitleResponse, summary="创建视频字幕")
async def create_subtitle(
    subtitle: VideoSubtitleCreate,
    db: Session = Depends(get_db)
):
    """创建新的视频字幕"""
    # 检查关联的视频是否存在
    video = db.query(VideoVideo).filter(VideoVideo.id == subtitle.video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="关联的视频不存在")

    db_subtitle = VideoSubtitle(**subtitle.model_dump())
    db.add(db_subtitle)
    db.commit()
    db.refresh(db_subtitle)
    return db_subtitle


@router.get("/subtitles", response_model=PaginatedResponse, summary="获取视频字幕列表")
async def get_subtitles(
    creative_id: Optional[int] = Query(None, description="筛选指定创意的字幕"),
    video_id: Optional[int] = Query(None, description="筛选指定视频的字幕"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    db: Session = Depends(get_db)
):
    """获取视频字幕列表（分页）"""
    offset = (page - 1) * size

    # 构建查询
    query = db.query(VideoSubtitle)

    # 根据创意ID筛选（通过视频和场景关联）
    if creative_id:
        query = query.join(VideoVideo).join(VideoScene).filter(VideoScene.creative_id == creative_id)

    # 根据视频ID筛选
    if video_id:
        query = query.filter(VideoSubtitle.video_id == video_id)

    # 获取总数
    total = query.count()

    # 获取数据
    subtitles = query.offset(offset).limit(size).all()

    # 计算总页数
    pages = ceil(total / size) if total > 0 else 0

    return PaginatedResponse(
        items=[VideoSubtitleResponse.model_validate(subtitle).model_dump() for subtitle in subtitles],
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/subtitles/{subtitle_id}", response_model=VideoSubtitleResponse, summary="获取单个视频字幕")
async def get_subtitle(
    subtitle_id: int,
    db: Session = Depends(get_db)
):
    """根据ID获取视频字幕"""
    subtitle = db.query(VideoSubtitle).filter(VideoSubtitle.id == subtitle_id).first()
    if not subtitle:
        raise HTTPException(status_code=404, detail="视频字幕不存在")
    return subtitle


@router.put("/subtitles/{subtitle_id}", response_model=VideoSubtitleResponse, summary="更新视频字幕")
async def update_subtitle(
    subtitle_id: int,
    subtitle_update: VideoSubtitleUpdate,
    db: Session = Depends(get_db)
):
    """更新视频字幕"""
    subtitle = db.query(VideoSubtitle).filter(VideoSubtitle.id == subtitle_id).first()
    if not subtitle:
        raise HTTPException(status_code=404, detail="视频字幕不存在")

    # 如果要更新video_id，检查新的视频是否存在
    update_data = subtitle_update.model_dump(exclude_unset=True)
    if 'video_id' in update_data:
        video = db.query(VideoVideo).filter(VideoVideo.id == update_data['video_id']).first()
        if not video:
            raise HTTPException(status_code=404, detail="关联的视频不存在")

    # 更新字段
    for field, value in update_data.items():
        setattr(subtitle, field, value)

    db.commit()
    db.refresh(subtitle)
    return subtitle


@router.delete("/subtitles/{subtitle_id}", summary="删除视频字幕")
async def delete_subtitle(
    subtitle_id: int,
    db: Session = Depends(get_db)
):
    """删除视频字幕"""
    subtitle = db.query(VideoSubtitle).filter(VideoSubtitle.id == subtitle_id).first()
    if not subtitle:
        raise HTTPException(status_code=404, detail="视频字幕不存在")

    db.delete(subtitle)
    db.commit()
    return {"message": "视频字幕删除成功"}


# ==================== Generation APIs ====================

@router.post("/generate/scenes", summary="生成场景")
async def generate_scenes(
    request: GenerateRequest,
    db: Session = Depends(get_db)
):
    """基于创意生成场景和提示词"""
    # 检查创意是否存在
    creative = db.query(VideoCreative).filter(VideoCreative.id == request.creative_id).first()
    if not creative:
        raise HTTPException(status_code=404, detail="视频创意不存在")

    try:
        # 提交任务到队列
        task_id = await task_queue.submit_task(
            task_type=TaskType.GENERATE_SCENES,
            params={"creative_id": request.creative_id}
        )

        return {
            "message": "场景生成任务已提交",
            "task_id": task_id
        }

    except Exception as e:
        logger.error(f"提交场景生成任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交场景生成任务失败: {str(e)}")


@router.post("/generate/images/scene/{scene_id}", summary="为单个场景重新生成图片")
async def regenerate_scene_image(
    scene_id: int,
    db: Session = Depends(get_db)
):
    """为指定场景重新生成图片"""
    # 检查场景是否存在
    scene = db.query(VideoScene).filter(VideoScene.id == scene_id).first()
    if not scene:
        raise HTTPException(status_code=404, detail="场景不存在")

    try:
        # 提交任务到队列
        task_id = await task_queue.submit_task(
            task_type=TaskType.REGENERATE_SCENE_IMAGE,
            params={"scene_id": scene_id}
        )

        return {
            "message": "图片重新生成任务已提交",
            "task_id": task_id
        }

    except Exception as e:
        logger.error(f"提交图片重新生成任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交图片重新生成任务失败: {str(e)}")

@router.post("/generate/images", summary="生成图片")
async def generate_images(
    request: GenerateRequest,
    db: Session = Depends(get_db)
):
    """为创意的所有场景生成图片"""
    # 检查创意是否存在
    creative = db.query(VideoCreative).filter(VideoCreative.id == request.creative_id).first()
    if not creative:
        raise HTTPException(status_code=404, detail="视频创意不存在")

    # 获取该创意的所有场景
    scenes = db.query(VideoScene).filter(VideoScene.creative_id == request.creative_id).all()
    if not scenes:
        raise HTTPException(status_code=400, detail="该创意还没有场景，请先生成场景")

    try:
        # 提交任务到队列
        task_id = await task_queue.submit_task(
            task_type=TaskType.GENERATE_IMAGES,
            params={"creative_id": request.creative_id}
        )

        return {
            "message": "图片生成任务已提交",
            "task_id": task_id
        }

    except Exception as e:
        logger.error(f"提交图片生成任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交图片生成任务失败: {str(e)}")



@router.post("/generate/videos", summary="生成视频")
async def generate_videos(
    request: GenerateRequest,
    db: Session = Depends(get_db)
):
    """为创意的所有场景生成视频"""
    # 检查创意是否存在
    creative = db.query(VideoCreative).filter(VideoCreative.id == request.creative_id).first()
    if not creative:
        raise HTTPException(status_code=404, detail="视频创意不存在")

    # 获取该创意的所有场景（需要有图片路径）
    scenes = db.query(VideoScene).filter(
        VideoScene.creative_id == request.creative_id,
        VideoScene.image.isnot(None)
    ).all()

    if not scenes:
        raise HTTPException(status_code=400, detail="该创意还没有图片，请先生成图片")

    try:
        # 提交任务到队列
        task_id = await task_queue.submit_task(
            task_type=TaskType.GENERATE_VIDEOS,
            params={"creative_id": request.creative_id}
        )

        return {
            "message": "视频生成任务已提交",
            "task_id": task_id
        }

    except Exception as e:
        logger.error(f"提交视频生成任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交视频生成任务失败: {str(e)}")




@router.post("/generate/videos/video/{video_id}", summary="为单个视频重新生成")
async def regenerate_video(
    video_id: int,
    db: Session = Depends(get_db)
):
    """为指定视频重新生成"""
    # 检查视频是否存在
    video = db.query(VideoVideo).filter(VideoVideo.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="视频不存在")

    # 检查关联的场景是否存在
    scene = db.query(VideoScene).filter(VideoScene.id == video.scene_id).first()
    if not scene:
        raise HTTPException(status_code=404, detail="关联的场景不存在")

    # 检查场景是否有图片
    if not scene.image:
        raise HTTPException(status_code=400, detail="关联的场景还没有图片，请先生成图片")

    try:
        # 提交任务到队列
        task_id = await task_queue.submit_task(
            task_type=TaskType.REGENERATE_VIDEO,
            params={"video_id": video_id}
        )

        return {
            "message": "视频重新生成任务已提交",
            "task_id": task_id
        }

    except Exception as e:
        logger.error(f"提交视频重新生成任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交视频重新生成任务失败: {str(e)}")




@router.post("/generate/subtitles", summary="生成字幕")
async def generate_subtitles(
    request: GenerateRequest,
    db: Session = Depends(get_db)
):
    """为创意的所有场景生成字幕和音频"""
    # 检查创意是否存在
    creative = db.query(VideoCreative).filter(VideoCreative.id == request.creative_id).first()
    if not creative:
        raise HTTPException(status_code=404, detail="视频创意不存在")

    # 获取该创意的所有视频
    videos = db.query(VideoVideo).join(VideoScene).filter(
        VideoScene.creative_id == request.creative_id
    ).all()

    if not videos:
        raise HTTPException(status_code=400, detail="该创意还没有视频，请先生成视频")

    try:
        # 提交任务到队列
        task_id = await task_queue.submit_task(
            task_type=TaskType.GENERATE_SUBTITLES,
            params={"creative_id": request.creative_id}
        )

        return {
            "message": "字幕生成任务已提交",
            "task_id": task_id
        }

    except Exception as e:
        logger.error(f"提交字幕生成任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交字幕生成任务失败: {str(e)}")


@router.post("/generate/video/subtitle/{video_id}", summary="为单个视频重新生成字幕")
async def regenerate_video_subtitle(
    video_id: int,
    db: Session = Depends(get_db)
):
    """为指定视频重新生成字幕"""
    # 检查视频是否存在
    video = db.query(VideoVideo).filter(VideoVideo.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="视频不存在")

    try:
        # 提交任务到队列
        task_id = await task_queue.submit_task(
            task_type=TaskType.GENERATE_SUBTITLES, # 复用GENERATE_SUBTITLES任务类型
            params={"creative_id": video.scene.creative_id} # 传入创意ID，让任务处理所有相关字幕
        )

        return {
            "message": "字幕重新生成任务已提交",
            "task_id": task_id
        }

    except Exception as e:
        logger.error(f"提交字幕重新生成任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交字幕重新生成任务失败: {str(e)}")

@router.post("/generate/audios/subtitle/{subtitle_id}", summary="为单个字幕重新生成音频")
async def regenerate_audio(
    subtitle_id: int,
    db: Session = Depends(get_db)
):
    """为指定字幕重新生成音频"""
    # 检查字幕是否存在
    subtitle = db.query(VideoSubtitle).filter(VideoSubtitle.id == subtitle_id).first()
    if not subtitle:
        raise HTTPException(status_code=404, detail="字幕不存在")

    try:
        # 提交任务到队列
        task_id = await task_queue.submit_task(
            task_type=TaskType.REGENERATE_AUDIO,
            params={"subtitle_id": subtitle_id}
        )

        return {
            "message": "音频重新生成任务已提交",
            "task_id": task_id
        }

    except Exception as e:
        logger.error(f"提交音频重新生成任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交音频重新生成任务失败: {str(e)}")


@router.post("/generate/character-image/{creative_id}", summary="生成主角图片")
async def generate_character_image(
    creative_id: int,
    db: Session = Depends(get_db)
):
    """为指定创意生成主角图片"""
    # 检查创意是否存在
    creative = db.query(VideoCreative).filter(VideoCreative.id == creative_id).first()
    if not creative:
        raise HTTPException(status_code=404, detail="视频创意不存在")

    # 检查是否有主角提示词
    if not creative.character_prompt:
        raise HTTPException(status_code=400, detail="主角提示词不能为空，请先设置主角提示词")

    try:
        # 提交任务到队列
        task_id = await task_queue.submit_task(
            task_type=TaskType.GENERATE_CHARACTER_IMAGE,
            params={"creative_id": creative_id}
        )

        return {
            "message": "主角图片生成任务已提交",
            "task_id": task_id
        }

    except Exception as e:
        logger.error(f"提交主角图片生成任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交主角图片生成任务失败: {str(e)}")


