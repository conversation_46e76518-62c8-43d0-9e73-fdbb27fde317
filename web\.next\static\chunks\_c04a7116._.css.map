{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/video-flow/rainbow-text.module.css"], "sourcesContent": [".animated {\r\n  background: linear-gradient(\r\n    to right,\r\n    rgb(from var(--card-foreground) r g b / 0.3) 15%,\r\n    rgb(from var(--card-foreground) r g b / 0.75) 35%,\r\n    rgb(from var(--card-foreground) r g b / 0.75) 65%,\r\n    rgb(from var(--card-foreground) r g b / 0.3) 85%\r\n  );\r\n  -webkit-background-clip: text;\r\n  background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  text-fill-color: transparent;\r\n  background-size: 500% auto;\r\n  animation: textShine 2s ease-in-out infinite alternate;\r\n}\r\n\r\n@keyframes textShine {\r\n  0% {\r\n    background-position: 0% 50%;\r\n  }\r\n  100% {\r\n    background-position: 100% 50%;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;AAgBA", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css"], "sourcesContent": [".root {\n  width: 100%;\n  min-width: 250px;\n  max-width: 550px;\n  overflow: hidden;\n  /* Base font styles */\n  color: var(--tweet-font-color);\n  font-family: var(--tweet-font-family);\n  font-weight: 400;\n  box-sizing: border-box;\n  border: var(--tweet-border);\n  border-radius: 12px;\n  margin: var(--tweet-container-margin);\n  background-color: var(--tweet-bg-color);\n  transition-property: background-color, box-shadow;\n  transition-duration: 0.2s;\n}\n.root:hover {\n  background-color: var(--tweet-bg-color-hover);\n}\n.article {\n  position: relative;\n  box-sizing: inherit;\n  padding: 0.75rem 1rem;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;AAiBA;;;;AAGA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/theme.css"], "sourcesContent": [".react-tweet-theme {\n  --tweet-container-margin: 1.5rem 0;\n\n  /* Header */\n  --tweet-header-font-size: 0.9375rem;\n  --tweet-header-line-height: 1.25rem;\n\n  /* Text */\n  --tweet-body-font-size: 1.25rem;\n  --tweet-body-font-weight: 400;\n  --tweet-body-line-height: 1.5rem;\n  --tweet-body-margin: 0;\n\n  /* Quoted Tweet */\n  --tweet-quoted-container-margin: 0.75rem 0;\n  --tweet-quoted-body-font-size: 0.938rem;\n  --tweet-quoted-body-font-weight: 400;\n  --tweet-quoted-body-line-height: 1.25rem;\n  --tweet-quoted-body-margin: 0.25rem 0 0.75rem 0;\n\n  /* Info */\n  --tweet-info-font-size: 0.9375rem;\n  --tweet-info-line-height: 1.25rem;\n\n  /* Actions like the like, reply and copy buttons */\n  --tweet-actions-font-size: 0.875rem;\n  --tweet-actions-line-height: 1rem;\n  --tweet-actions-font-weight: 700;\n  --tweet-actions-icon-size: 1.25em;\n  --tweet-actions-icon-wrapper-size: calc(\n    var(--tweet-actions-icon-size) + 0.75em\n  );\n\n  /* Reply button */\n  --tweet-replies-font-size: 0.875rem;\n  --tweet-replies-line-height: 1rem;\n  --tweet-replies-font-weight: 700;\n}\n\n:where(.react-tweet-theme) * {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n:is([data-theme='light'], .light) :where(.react-tweet-theme),\n:where(.react-tweet-theme) {\n  --tweet-skeleton-gradient: linear-gradient(\n    270deg,\n    #fafafa,\n    #eaeaea,\n    #eaeaea,\n    #fafafa\n  );\n  --tweet-border: 1px solid rgb(207, 217, 222);\n  --tweet-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,\n    Helvetica, Arial, sans-serif;\n  --tweet-font-color: rgb(15, 20, 25);\n  --tweet-font-color-secondary: rgb(83, 100, 113);\n  --tweet-bg-color: #fff;\n  --tweet-bg-color-hover: rgb(247, 249, 249);\n  --tweet-quoted-bg-color-hover: rgba(0, 0, 0, 0.03);\n  --tweet-color-blue-primary: rgb(29, 155, 240);\n  --tweet-color-blue-primary-hover: rgb(26, 140, 216);\n  --tweet-color-blue-secondary: rgb(0, 111, 214);\n  --tweet-color-blue-secondary-hover: rgba(0, 111, 214, 0.1);\n  --tweet-color-red-primary: rgb(249, 24, 128);\n  --tweet-color-red-primary-hover: rgba(249, 24, 128, 0.1);\n  --tweet-color-green-primary: rgb(0, 186, 124);\n  --tweet-color-green-primary-hover: rgba(0, 186, 124, 0.1);\n  --tweet-twitter-icon-color: var(--tweet-font-color);\n  --tweet-verified-old-color: rgb(130, 154, 171);\n  --tweet-verified-blue-color: var(--tweet-color-blue-primary);\n}\n\n:is([data-theme='dark'], .dark) :where(.react-tweet-theme) {\n  --tweet-skeleton-gradient: linear-gradient(\n    270deg,\n    #15202b,\n    rgb(30, 39, 50),\n    rgb(30, 39, 50),\n    rgb(21, 32, 43)\n  );\n  --tweet-border: 1px solid rgb(66, 83, 100);\n  --tweet-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,\n    Helvetica, Arial, sans-serif;\n  --tweet-font-color: rgb(247, 249, 249);\n  --tweet-font-color-secondary: rgb(139, 152, 165);\n  --tweet-bg-color: rgb(21, 32, 43);\n  --tweet-bg-color-hover: rgb(30, 39, 50);\n  --tweet-quoted-bg-color-hover: rgba(255, 255, 255, 0.03);\n  --tweet-color-blue-primary: rgb(29, 155, 240);\n  --tweet-color-blue-primary-hover: rgb(26, 140, 216);\n  --tweet-color-blue-secondary: rgb(107, 201, 251);\n  --tweet-color-blue-secondary-hover: rgba(107, 201, 251, 0.1);\n  --tweet-color-red-primary: rgb(249, 24, 128);\n  --tweet-color-red-primary-hover: rgba(249, 24, 128, 0.1);\n  --tweet-color-green-primary: rgb(0, 186, 124);\n  --tweet-color-green-primary-hover: rgba(0, 186, 124, 0.1);\n  --tweet-twitter-icon-color: var(--tweet-font-color);\n  --tweet-verified-old-color: rgb(130, 154, 171);\n  --tweet-verified-blue-color: #fff;\n}\n\n@media (prefers-color-scheme: dark) {\n  :where(.react-tweet-theme) {\n    --tweet-skeleton-gradient: linear-gradient(\n      270deg,\n      #15202b,\n      rgb(30, 39, 50),\n      rgb(30, 39, 50),\n      rgb(21, 32, 43)\n    );\n    --tweet-border: 1px solid rgb(66, 83, 100);\n    --tweet-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,\n      Helvetica, Arial, sans-serif;\n    --tweet-font-color: rgb(247, 249, 249);\n    --tweet-font-color-secondary: rgb(139, 152, 165);\n    --tweet-bg-color: rgb(21, 32, 43);\n    --tweet-bg-color-hover: rgb(30, 39, 50);\n    --tweet-color-blue-primary: rgb(29, 155, 240);\n    --tweet-color-blue-primary-hover: rgb(26, 140, 216);\n    --tweet-color-blue-secondary: rgb(107, 201, 251);\n    --tweet-color-blue-secondary-hover: rgba(107, 201, 251, 0.1);\n    --tweet-color-red-primary: rgb(249, 24, 128);\n    --tweet-color-red-primary-hover: rgba(249, 24, 128, 0.1);\n    --tweet-color-green-primary: rgb(0, 186, 124);\n    --tweet-color-green-primary-hover: rgba(0, 186, 124, 0.1);\n    --tweet-twitter-icon-color: var(--tweet-font-color);\n    --tweet-verified-old-color: rgb(130, 154, 171);\n    --tweet-verified-blue-color: #fff;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA;;;;;;AAMA;;;;;;;;;;;;;;;;;;;;;;AA8BA;;;;;;;;;;;;;;;;;;;;;;AA6BA;EACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css"], "sourcesContent": [".header {\n  display: flex;\n  padding-bottom: 0.75rem;\n  line-height: var(--tweet-header-line-height);\n  font-size: var(--tweet-header-font-size);\n  white-space: nowrap;\n  overflow-wrap: break-word;\n  overflow: hidden;\n}\n\n.avatar {\n  position: relative;\n  height: 48px;\n  width: 48px;\n}\n.avatarOverflow {\n  height: 100%;\n  width: 100%;\n  position: absolute;\n  overflow: hidden;\n  border-radius: 9999px;\n}\n.avatarSquare {\n  border-radius: 4px;\n}\n.avatarShadow {\n  height: 100%;\n  width: 100%;\n  transition-property: background-color;\n  transition-duration: 0.2s;\n  box-shadow: rgb(0 0 0 / 3%) 0px 0px 2px inset;\n}\n.avatarShadow:hover {\n  background-color: rgba(26, 26, 26, 0.15);\n}\n\n.author {\n  max-width: calc(100% - 84px);\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  margin: 0 0.5rem;\n}\n.authorLink {\n  text-decoration: none;\n  color: inherit;\n  display: flex;\n  align-items: center;\n}\n.authorLink:hover {\n  text-decoration-line: underline;\n}\n.authorVerified {\n  display: inline-flex;\n}\n.authorLinkText {\n  font-weight: 700;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n}\n\n.authorMeta {\n  display: flex;\n}\n.authorFollow {\n  display: flex;\n}\n.username {\n  color: var(--tweet-font-color-secondary);\n  text-decoration: none;\n  text-overflow: ellipsis;\n}\n.follow {\n  color: var(--tweet-color-blue-secondary);\n  text-decoration: none;\n  font-weight: 700;\n}\n.follow:hover {\n  text-decoration-line: underline;\n}\n.separator {\n  padding: 0 0.25rem;\n}\n\n.brand {\n  margin-inline-start: auto;\n}\n\n.twitterIcon {\n  width: 23.75px;\n  height: 23.75px;\n  color: var(--tweet-twitter-icon-color);\n  fill: currentColor;\n  user-select: none;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;AAUA;;;;;;AAKA;;;;;;;;AAOA;;;;AAGA;;;;;;;;AAOA;;;;AAIA;;;;;;;;AAOA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;;;;AAOA;;;;AAMA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAIA;;;;AAIA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css"], "sourcesContent": [".verified {\n  margin-left: 0.125rem;\n  max-width: 20px;\n  max-height: 20px;\n  height: 1.25em;\n  fill: currentColor;\n  user-select: none;\n  vertical-align: text-bottom;\n}\n"], "names": [], "mappings": "AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/verified-badge.module.css"], "sourcesContent": [".verifiedOld {\n  color: var(--tweet-verified-old-color);\n}\n.verifiedBlue {\n  color: var(--tweet-verified-blue-color);\n}\n.verifiedGovernment {\n  /* color: var(--tweet-verified-government-color); */\n  color: rgb(130, 154, 171);\n}\n"], "names": [], "mappings": "AAAA;;;;AAGA;;;;AAGA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css"], "sourcesContent": [".root {\n  text-decoration: none;\n  color: var(--tweet-font-color-secondary);\n  font-size: 0.9375rem;\n  line-height: 1.25rem;\n  margin-bottom: 0.25rem;\n  overflow-wrap: break-word;\n  white-space: pre-wrap;\n}\n.root:hover {\n  text-decoration-thickness: 1px;\n  text-decoration-line: underline;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;AASA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-link.module.css"], "sourcesContent": [".root {\n  font-weight: inherit;\n  color: var(--tweet-color-blue-secondary);\n  text-decoration: none;\n  cursor: pointer;\n}\n.root:hover {\n  text-decoration-thickness: 1px;\n  text-decoration-line: underline;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;AAMA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-body.module.css"], "sourcesContent": [".root {\n  font-size: var(--tweet-body-font-size);\n  font-weight: var(--tweet-body-font-weight);\n  line-height: var(--tweet-body-line-height);\n  margin: var(--tweet-body-margin);\n  overflow-wrap: break-word;\n  white-space: pre-wrap;\n}\n"], "names": [], "mappings": "AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css"], "sourcesContent": [".root {\n  margin-top: 0.75rem;\n  overflow: hidden;\n  position: relative;\n}\n.rounded {\n  border: var(--tweet-border);\n  border-radius: 12px;\n}\n.mediaWrapper {\n  display: grid;\n  grid-auto-rows: 1fr;\n  gap: 2px;\n  height: 100%;\n  width: 100%;\n}\n.grid2Columns {\n  grid-template-columns: repeat(2, 1fr);\n}\n.grid3 > a:first-child {\n  grid-row: span 2;\n}\n.grid2x2 {\n  grid-template-rows: repeat(2, 1fr);\n}\n.mediaContainer {\n  position: relative;\n  height: 100%;\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.mediaLink {\n  text-decoration: none;\n  outline-style: none;\n}\n.skeleton {\n  padding-bottom: 56.25%;\n  width: 100%;\n  display: block;\n}\n.image {\n  position: absolute;\n  top: 0px;\n  left: 0px;\n  bottom: 0px;\n  height: 100%;\n  width: 100%;\n  margin: 0;\n  object-fit: cover;\n  object-position: center;\n}\n"], "names": [], "mappings": "AAAA;;;;;;AAKA;;;;;AAIA;;;;;;;;AAOA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;AAQA;;;;;AAIA;;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css"], "sourcesContent": [".anchor {\n  display: flex;\n  align-items: center;\n  color: white;\n  padding: 0 1rem;\n  border: 1px solid transparent;\n  border-radius: 9999px;\n  font-weight: 700;\n  transition: background-color 0.2s;\n  cursor: pointer;\n  user-select: none;\n  outline-style: none;\n  text-decoration: none;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.videoButton {\n  position: relative;\n  height: 67px;\n  width: 67px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: var(--tweet-color-blue-primary);\n  transition-property: background-color;\n  transition-duration: 0.2s;\n  border: 4px solid #fff;\n  border-radius: 9999px;\n  cursor: pointer;\n}\n.videoButton:hover,\n.videoButton:focus-visible {\n  background-color: var(--tweet-color-blue-primary-hover);\n}\n.videoButtonIcon {\n  margin-left: 3px;\n  width: calc(50% + 4px);\n  height: calc(50% + 4px);\n  max-width: 100%;\n  color: #fff;\n  fill: currentColor;\n  user-select: none;\n}\n.watchOnTwitter {\n  position: absolute;\n  top: 12px;\n  right: 8px;\n}\n.watchOnTwitter > a {\n  min-width: 2rem;\n  min-height: 2rem;\n  font-size: 0.875rem;\n  line-height: 1rem;\n  backdrop-filter: blur(4px);\n  background-color: rgba(15, 20, 25, 0.75);\n}\n.watchOnTwitter > a:hover {\n  background-color: rgba(39, 44, 48, 0.75);\n}\n.viewReplies {\n  position: relative;\n  min-height: 2rem;\n  background-color: var(--tweet-color-blue-primary);\n  border-color: var(--tweet-color-blue-primary);\n  font-size: 0.9375rem;\n  line-height: 1.25rem;\n}\n.viewReplies:hover {\n  background-color: var(--tweet-color-blue-primary-hover);\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;;;;;AAcA;;;;AAIA;;;;;;;;;;AASA;;;;;;AAKA;;;;;;;;;AAQA;;;;AAGA;;;;;;;;;AAQA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.module.css"], "sourcesContent": [".root {\n  color: inherit;\n  text-decoration: none;\n  font-size: var(--tweet-info-font-size);\n  line-height: var(--tweet-info-line-height);\n}\n.root:hover {\n  text-decoration-thickness: 1px;\n  text-decoration-line: underline;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;AAMA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-info.module.css"], "sourcesContent": [".info {\n  display: flex;\n  align-items: center;\n  color: var(--tweet-font-color-secondary);\n  margin-top: 0.125rem;\n  overflow-wrap: break-word;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n.infoLink {\n  color: inherit;\n  text-decoration: none;\n}\n.infoLink {\n  height: var(--tweet-actions-icon-wrapper-size);\n  width: var(--tweet-actions-icon-wrapper-size);\n  font: inherit;\n  margin-left: auto;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  margin-right: -4px;\n  border-radius: 9999px;\n  transition-property: background-color;\n  transition-duration: 0.2s;\n}\n.infoLink:hover {\n  background-color: var(--tweet-color-blue-secondary-hover);\n}\n.infoIcon {\n  color: inherit;\n  fill: currentColor;\n  height: var(--tweet-actions-icon-size);\n  user-select: none;\n}\n.infoLink:hover > .infoIcon {\n  color: var(--tweet-color-blue-secondary);\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;AASA;;;;;;;;;;;;;;;;AAiBA;;;;AAGA;;;;;;;AAMA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css"], "sourcesContent": [".actions {\n  display: flex;\n  align-items: center;\n  color: var(--tweet-font-color-secondary);\n  padding-top: 0.25rem;\n  margin-top: 0.25rem;\n  border-top: var(--tweet-border);\n  overflow-wrap: break-word;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n.like,\n.reply,\n.copy {\n  text-decoration: none;\n  color: inherit;\n  display: flex;\n  align-items: center;\n  margin-right: 1.25rem;\n}\n.like:hover,\n.reply:hover,\n.copy:hover {\n  background-color: rgba(0, 0, 0, 0);\n}\n.like:hover > .likeIconWrapper {\n  background-color: var(--tweet-color-red-primary-hover);\n}\n.like:hover > .likeCount {\n  color: var(--tweet-color-red-primary);\n  text-decoration-line: underline;\n}\n.likeIconWrapper,\n.replyIconWrapper,\n.copyIconWrapper {\n  width: var(--tweet-actions-icon-wrapper-size);\n  height: var(--tweet-actions-icon-wrapper-size);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  margin-left: -0.25rem;\n  border-radius: 9999px;\n}\n.likeIcon,\n.replyIcon,\n.copyIcon {\n  height: var(--tweet-actions-icon-size);\n  fill: currentColor;\n  user-select: none;\n}\n.likeIcon {\n  color: var(--tweet-color-red-primary);\n}\n.likeCount,\n.replyText,\n.copyText {\n  font-size: var(--tweet-actions-font-size);\n  font-weight: var(--tweet-actions-font-weight);\n  line-height: var(--tweet-actions-line-height);\n  margin-left: 0.25rem;\n}\n\n.reply:hover > .replyIconWrapper {\n  background-color: var(--tweet-color-blue-secondary-hover);\n}\n.reply:hover > .replyText {\n  color: var(--tweet-color-blue-secondary);\n  text-decoration-line: underline;\n}\n.replyIcon {\n  color: var(--tweet-color-blue-primary);\n}\n\n.copy {\n  font: inherit;\n  background: none;\n  border: none;\n  cursor: pointer;\n}\n.copy:hover > .copyIconWrapper {\n  background-color: var(--tweet-color-green-primary-hover);\n}\n.copy:hover .copyIcon {\n  color: var(--tweet-color-green-primary);\n}\n.copy:hover > .copyText {\n  color: var(--tweet-color-green-primary);\n  text-decoration-line: underline;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;AAYA;;;;;;;;AASA;;;;AAKA;;;;AAGA;;;;;AAIA;;;;;;;;;;AAWA;;;;;;AAOA;;;;AAGA;;;;;;;AASA;;;;AAGA;;;;;AAIA;;;;AAIA;;;;;;;AAMA;;;;AAGA;;;;AAGA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 625, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css"], "sourcesContent": [".replies {\n  padding: 0.25rem 0;\n}\n.link {\n  text-decoration: none;\n  color: var(--tweet-color-blue-secondary);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 32px;\n  min-height: 32px;\n  user-select: none;\n  outline-style: none;\n  transition-property: background-color;\n  transition-duration: 0.2s;\n  padding: 0 1rem;\n  border: var(--tweet-border);\n  border-radius: 9999px;\n}\n.link:hover {\n  background-color: var(--tweet-color-blue-secondary-hover);\n}\n.text {\n  font-weight: var(--tweet-replies-font-weight);\n  font-size: var(--tweet-replies-font-size);\n  line-height: var(--tweet-replies-line-height);\n  overflow-wrap: break-word;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  overflow: hidden;\n}\n"], "names": [], "mappings": "AAAA;;;;AAGA;;;;;;;;;;;;;;;;;AAgBA;;;;AAGA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 662, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css"], "sourcesContent": [".root {\n  width: 100%;\n  overflow: hidden;\n  border: var(--tweet-border);\n  border-radius: 12px;\n  margin: var(--tweet-quoted-container-margin);\n  transition-property: background-color, box-shadow;\n  transition-duration: 0.2s;\n  cursor: pointer;\n}\n\n.root:hover {\n  background-color: var(--tweet-quoted-bg-color-hover);\n}\n\n.article {\n  position: relative;\n  box-sizing: inherit;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;AAWA;;;;AAIA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 684, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css"], "sourcesContent": [".header {\n  display: flex;\n  padding: 0.75rem 0.75rem 0 0.75rem;\n  line-height: var(--tweet-header-line-height);\n  font-size: var(--tweet-header-font-size);\n  white-space: nowrap;\n  overflow-wrap: break-word;\n  overflow: hidden;\n}\n\n.avatar {\n  position: relative;\n  height: 20px;\n  width: 20px;\n}\n\n.avatarSquare {\n  border-radius: 4px;\n}\n\n.author {\n  display: flex;\n  margin: 0 0.5rem;\n}\n\n.authorText {\n  font-weight: 700;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n}\n\n.username {\n  color: var(--tweet-font-color-secondary);\n  text-decoration: none;\n  text-overflow: ellipsis;\n  margin-left: 0.125rem;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;AAUA;;;;;;AAMA;;;;AAIA;;;;;AAKA;;;;;;;AAOA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 725, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.module.css"], "sourcesContent": [".root {\n  font-size: var(--tweet-quoted-body-font-size);\n  font-weight: var(--tweet-quoted-body-font-weight);\n  line-height: var(--tweet-quoted-body-line-height);\n  margin: var(--tweet-quoted-body-margin);\n  overflow-wrap: break-word;\n  white-space: pre-wrap;\n  padding: 0 0.75rem;\n}\n"], "names": [], "mappings": "AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 737, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-not-found.module.css"], "sourcesContent": [".root {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding-bottom: 0.75rem;\n}\n.root > h3 {\n  font-size: 1.25rem;\n  margin-bottom: 0.5rem;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;AAMA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 751, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/skeleton.module.css"], "sourcesContent": [".skeleton {\n  display: block;\n  width: 100%;\n  border-radius: 5px;\n  background-image: var(--tweet-skeleton-gradient);\n  background-size: 400% 100%;\n  animation: loading 8s ease-in-out infinite;\n}\n\n@media (prefers-reduced-motion: reduce) {\n  .skeleton {\n    animation: none;\n    background-position: 200% 0;\n  }\n}\n\n@keyframes loading {\n  0% {\n    background-position: 200% 0;\n  }\n  100% {\n    background-position: -200% 0;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;AASA;EACE;;;;;;AAMF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 779, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.module.css"], "sourcesContent": [".root {\n  pointer-events: none;\n  padding-bottom: 0.25rem;\n}\n"], "names": [], "mappings": "AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 786, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/styles/prosemirror.css"], "sourcesContent": [".prose {\r\n  max-width: inherit;\r\n}\r\n\r\n.prose.inline-editor * {\r\n  margin: 0;\r\n}\r\n\r\n.prose.inline-editor .is-empty {\r\n  display: none;\r\n}\r\n\r\n.prose.inline-editor .is-empty.placeholder {\r\n  display: block;\r\n  opacity: 0.65;\r\n  font-size: 14px;\r\n}\r\n\r\n.ProseMirror {\r\n  line-height: 1.75;\r\n}\r\n\r\n.ProseMirror .is-editor-empty:first-child::before {\r\n  content: attr(data-placeholder);\r\n  float: left;\r\n  color: hsl(var(--muted-foreground));\r\n  pointer-events: none;\r\n  height: 0;\r\n}\r\n\r\n.ProseMirror p.is-empty::before {\r\n  content: attr(data-placeholder);\r\n  float: left;\r\n  color: hsl(var(--muted-foreground));\r\n  pointer-events: none;\r\n  height: 0;\r\n}\r\n\r\n.ProseMirror .mention {\r\n  background-color: var(--purple-light);\r\n  border-radius: 0.4rem;\r\n  box-decoration-break: clone;\r\n  color: var(--brand);\r\n  padding: 0.1rem 0.3rem;\r\n}\r\n\r\n/* Custom image styles */\r\n\r\n.ProseMirror img {\r\n  transition: filter 0.1s ease-in-out;\r\n\r\n  &:hover {\r\n    cursor: pointer;\r\n    filter: brightness(90%);\r\n  }\r\n\r\n  &.ProseMirror-selectednode {\r\n    outline: 3px solid #5abbf7;\r\n    filter: brightness(90%);\r\n  }\r\n}\r\n\r\n.img-placeholder {\r\n  position: relative;\r\n\r\n  &:before {\r\n    content: \"\";\r\n    box-sizing: border-box;\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    width: 36px;\r\n    height: 36px;\r\n    border-radius: 50%;\r\n    border: 3px solid var(--novel-stone-200);\r\n    border-top-color: var(--novel-stone-800);\r\n    animation: spinning 0.6s linear infinite;\r\n  }\r\n}\r\n\r\n.ProseMirror pre {\r\n  background: #0d0d0d;\r\n  border-radius: 0.5rem;\r\n  color: #fff;\r\n  font-family: \"JetBrainsMono\", monospace;\r\n  padding: 0.75rem 1rem;\r\n\r\n  code {\r\n    background: none;\r\n    color: inherit;\r\n    font-size: 0.8rem;\r\n    padding: 0;\r\n  }\r\n\r\n  .hljs-comment,\r\n  .hljs-quote {\r\n    color: #616161;\r\n  }\r\n\r\n  .hljs-variable,\r\n  .hljs-template-variable,\r\n  .hljs-attribute,\r\n  .hljs-tag,\r\n  .hljs-name,\r\n  .hljs-regexp,\r\n  .hljs-link,\r\n  .hljs-name,\r\n  .hljs-selector-id,\r\n  .hljs-selector-class {\r\n    color: #f98181;\r\n  }\r\n\r\n  .hljs-number,\r\n  .hljs-meta,\r\n  .hljs-built_in,\r\n  .hljs-builtin-name,\r\n  .hljs-literal,\r\n  .hljs-type,\r\n  .hljs-params {\r\n    color: #fbbc88;\r\n  }\r\n\r\n  .hljs-string,\r\n  .hljs-symbol,\r\n  .hljs-bullet {\r\n    color: #b9f18d;\r\n  }\r\n\r\n  .hljs-title,\r\n  .hljs-section {\r\n    color: #faf594;\r\n  }\r\n\r\n  .hljs-keyword,\r\n  .hljs-selector-tag {\r\n    color: #70cff8;\r\n  }\r\n\r\n  .hljs-emphasis {\r\n    font-style: italic;\r\n  }\r\n\r\n  .hljs-strong {\r\n    font-weight: 700;\r\n  }\r\n}\r\n\r\n@keyframes spinning {\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* Custom TODO list checkboxes – shoutout to this awesome tutorial: https://moderncss.dev/pure-css-custom-checkbox-style/ */\r\n\r\nul[data-type=\"taskList\"] li > label {\r\n  margin-right: 0.2rem;\r\n  user-select: none;\r\n}\r\n\r\n@media screen and (max-width: 768px) {\r\n  ul[data-type=\"taskList\"] li > label {\r\n    margin-right: 0.5rem;\r\n  }\r\n}\r\n\r\nul[data-type=\"taskList\"] li > label input[type=\"checkbox\"] {\r\n  -webkit-appearance: none;\r\n  appearance: none;\r\n  background-color: hsl(var(--background));\r\n  margin: 0;\r\n  cursor: pointer;\r\n  width: 1.2em;\r\n  height: 1.2em;\r\n  position: relative;\r\n  top: 5px;\r\n  border: 2px solid hsl(var(--border));\r\n  margin-right: 0.3rem;\r\n  display: grid;\r\n  place-content: center;\r\n\r\n  &:hover {\r\n    background-color: hsl(var(--accent));\r\n  }\r\n\r\n  &:active {\r\n    background-color: hsl(var(--accent));\r\n  }\r\n\r\n  &::before {\r\n    content: \"\";\r\n    width: 0.65em;\r\n    height: 0.65em;\r\n    transform: scale(0);\r\n    transition: 120ms transform ease-in-out;\r\n    box-shadow: inset 1em 1em;\r\n    transform-origin: center;\r\n    clip-path: polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0%, 43% 62%);\r\n  }\r\n\r\n  &:checked::before {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\nul[data-type=\"taskList\"] li[data-checked=\"true\"] > div > p {\r\n  color: var(--muted-foreground);\r\n  text-decoration: line-through;\r\n  text-decoration-thickness: 2px;\r\n}\r\n\r\n/* Overwrite tippy-box original max-width */\r\n\r\n.tippy-box {\r\n  max-width: 400px !important;\r\n}\r\n\r\n.ProseMirror:not(.dragging) .ProseMirror-selectednode {\r\n  outline: none !important;\r\n  background-color: var(--novel-highlight-blue);\r\n  transition: background-color 0.2s;\r\n  box-shadow: none;\r\n}\r\n\r\n.drag-handle {\r\n  position: fixed;\r\n  opacity: 1;\r\n  transition: opacity ease-in 0.2s;\r\n  border-radius: 0.25rem;\r\n\r\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 10' style='fill: rgba(0, 0, 0, 0.5)'%3E%3Cpath d='M3,2 C2.44771525,2 2,1.55228475 2,1 C2,0.44771525 2.44771525,0 3,0 C3.55228475,0 4,0.44771525 4,1 C4,1.55228475 3.55228475,2 3,2 Z M3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 C3.55228475,4 4,4.44771525 4,5 C4,5.55228475 3.55228475,6 3,6 Z M3,10 C2.44771525,10 2,9.55228475 2,9 C2,8.44771525 2.44771525,8 3,8 C3.55228475,8 4,8.44771525 4,9 C4,9.55228475 3.55228475,10 3,10 Z M7,2 C6.44771525,2 6,1.55228475 6,1 C6,0.44771525 6.44771525,0 7,0 C7.55228475,0 8,0.44771525 8,1 C8,1.55228475 7.55228475,2 7,2 Z M7,6 C6.44771525,6 6,5.55228475 6,5 C6,4.44771525 6.44771525,4 7,4 C7.55228475,4 8,4.44771525 8,5 C8,5.55228475 7.55228475,6 7,6 Z M7,10 C6.44771525,10 6,9.55228475 6,9 C6,8.44771525 6.44771525,8 7,8 C7.55228475,8 8,8.44771525 8,9 C8,9.55228475 7.55228475,10 7,10 Z'%3E%3C/path%3E%3C/svg%3E\");\r\n  background-size: calc(0.5em + 0.375rem) calc(0.5em + 0.375rem);\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  width: 1.2rem;\r\n  height: 1.5rem;\r\n  z-index: 50;\r\n  cursor: grab;\r\n\r\n  &:hover {\r\n    background-color: var(--novel-stone-100);\r\n    transition: background-color 0.2s;\r\n  }\r\n\r\n  &:active {\r\n    background-color: var(--novel-stone-200);\r\n    transition: background-color 0.2s;\r\n    cursor: grabbing;\r\n  }\r\n\r\n  &.hide {\r\n    opacity: 0;\r\n    pointer-events: none;\r\n  }\r\n\r\n  @media screen and (max-width: 600px) {\r\n    display: none;\r\n    pointer-events: none;\r\n  }\r\n}\r\n\r\n.dark .drag-handle {\r\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 10' style='fill: rgba(255, 255, 255, 0.5)'%3E%3Cpath d='M3,2 C2.44771525,2 2,1.55228475 2,1 C2,0.44771525 2.44771525,0 3,0 C3.55228475,0 4,0.44771525 4,1 C4,1.55228475 3.55228475,2 3,2 Z M3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 C3.55228475,4 4,4.44771525 4,5 C4,5.55228475 3.55228475,6 3,6 Z M3,10 C2.44771525,10 2,9.55228475 2,9 C2,8.44771525 2.44771525,8 3,8 C3.55228475,8 4,8.44771525 4,9 C4,9.55228475 3.55228475,10 3,10 Z M7,2 C6.44771525,2 6,1.55228475 6,1 C6,0.44771525 6.44771525,0 7,0 C7.55228475,0 8,0.44771525 8,1 C8,1.55228475 7.55228475,2 7,2 Z M7,6 C6.44771525,6 6,5.55228475 6,5 C6,4.44771525 6.44771525,4 7,4 C7.55228475,4 8,4.44771525 8,5 C8,5.55228475 7.55228475,6 7,6 Z M7,10 C6.44771525,10 6,9.55228475 6,9 C6,8.44771525 6.44771525,8 7,8 C7.55228475,8 8,8.44771525 8,9 C8,9.55228475 7.55228475,10 7,10 Z'%3E%3C/path%3E%3C/svg%3E\");\r\n}\r\n\r\n/* Custom Youtube Video CSS */\r\niframe {\r\n  border: 8px solid #ffd00027;\r\n  border-radius: 4px;\r\n  min-width: 200px;\r\n  min-height: 200px;\r\n  display: block;\r\n  outline: 0px solid transparent;\r\n}\r\n\r\ndiv[data-youtube-video] > iframe {\r\n  cursor: move;\r\n  aspect-ratio: 16 / 9;\r\n  width: 100%;\r\n}\r\n\r\n.ProseMirror-selectednode iframe {\r\n  transition: outline 0.15s;\r\n  outline: 6px solid #fbbf24;\r\n}\r\n\r\n@media only screen and (max-width: 480px) {\r\n  div[data-youtube-video] > iframe {\r\n    max-height: 50px;\r\n  }\r\n}\r\n\r\n@media only screen and (max-width: 720px) {\r\n  div[data-youtube-video] > iframe {\r\n    max-height: 100px;\r\n  }\r\n}\r\n\r\n/* CSS for bold coloring and highlighting issue*/\r\nspan[style] > strong {\r\n  color: inherit;\r\n}\r\n\r\nmark[style] > strong {\r\n  color: inherit;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;;;AAgBA;;;;;;;;AAUA;;;;AAGE;;;;;AAKA;;;;;AAMF;;;;AAGE;;;;;;;;;;;;;;AAeF;;;;;;;;AAOE;;;;;;;AAOA;;;;AAKA;;;;AAaA;;;;AAUA;;;;AAMA;;;;AAKA;;;;AAKA;;;;AAIA;;;;AAKF;;;;;;AAQA;;;;;AAKA;EACE;;;;;AAKF;;;;;;;;;;;;;;;AAeE;;;;AAQA;;;;;;;;;;;AAWA;;;;AAKF;;;;;AAQA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;;;;;;AAeE;;;;;AAKA;;;;;;AAMA;;;;;AAKA;EAAsC;;;;;;AAMxC;;;;AAKA;;;;;;;;;AASA;;;;;;AAMA;;;;;AAKA;EACE;;;;;AAKF;EACE;;;;;AAMF", "debugId": null}}, {"offset": {"line": 1050, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/video-flow/loading-animation.module.css"], "sourcesContent": ["@keyframes bouncing-animation {\r\n  to {\r\n    opacity: 0.1;\r\n    transform: translateY(-8px);\r\n  }\r\n}\r\n\r\n.loadingAnimation {\r\n  display: flex;\r\n}\r\n\r\n.loadingAnimation > div {\r\n  width: 8px;\r\n  height: 8px;\r\n  margin: 2px 4px;\r\n  border-radius: 50%;\r\n  background-color: #a3a1a1;\r\n  opacity: 1;\r\n  animation: bouncing-animation 0.5s infinite alternate;\r\n}\r\n\r\n.loadingAnimation.sm > div {\r\n  width: 6px;\r\n  height: 6px;\r\n  margin: 1px 2px;\r\n}\r\n\r\n.loadingAnimation > div:nth-child(2) {\r\n  animation-delay: 0.2s;\r\n}\r\n\r\n.loadingAnimation > div:nth-child(3) {\r\n  animation-delay: 0.4s;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;AAIA;;;;;;;;;;AAUA;;;;;;AAMA;;;;AAIA", "debugId": null}}]}