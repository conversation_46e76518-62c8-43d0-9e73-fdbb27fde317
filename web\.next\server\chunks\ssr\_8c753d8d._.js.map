{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/video-flow/language-switcher.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LanguageSwitcher = registerClientReference(\n    function() { throw new Error(\"Attempted to call LanguageSwitcher() from the server but LanguageSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/video-flow/language-switcher.tsx <module evaluation>\",\n    \"LanguageSwitcher\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iFACA", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/video-flow/language-switcher.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LanguageSwitcher = registerClientReference(\n    function() { throw new Error(\"Attempted to call LanguageSwitcher() from the server but LanguageSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/video-flow/language-switcher.tsx\",\n    \"LanguageSwitcher\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6DACA", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/magicui/number-ticker.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const NumberTicker = registerClientReference(\n    function() { throw new Error(\"Attempted to call NumberTicker() from the server but NumberTicker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/magicui/number-ticker.tsx <module evaluation>\",\n    \"NumberTicker\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0EACA", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/magicui/number-ticker.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const NumberTicker = registerClientReference(\n    function() { throw new Error(\"Attempted to call NumberTicker() from the server but NumberTicker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/magicui/number-ticker.tsx\",\n    \"NumberTicker\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,sDACA", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/lib/utils.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAE/B;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/ui/button.tsx"], "sourcesContent": ["import { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  },\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(\r\n        buttonVariants({ variant, size, className }),\r\n        \"cursor-pointer active:scale-105\",\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAGA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,eAAe;YAAE;YAAS;YAAM;QAAU,IAC1C;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/app/chat/components/site-header.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { StarFilledIcon, GitHubLogoIcon } from \"@radix-ui/react-icons\";\r\nimport Link from \"next/link\";\r\nimport { useTranslations } from 'next-intl';\r\n\r\nimport { LanguageSwitcher } from \"~/components/video-flow/language-switcher\";\r\nimport { NumberTicker } from \"~/components/magicui/number-ticker\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { env } from \"~/env\";\r\n\r\nexport function SiteHeader() {\r\n  const t = useTranslations('common');\r\n\r\n  return (\r\n    <header className=\"supports-backdrop-blur:bg-background/80 bg-background/40 sticky top-0 left-0 z-40 flex h-15 w-full flex-col items-center backdrop-blur-lg\">\r\n      <div className=\"container flex h-15 items-center justify-between px-3\">\r\n        <div className=\"text-xl font-medium\">\r\n          <span className=\"mr-1 text-2xl\">🦌</span>\r\n          <span>VideoFlow</span>\r\n        </div>\r\n        <div className=\"relative flex items-center gap-2\">\r\n          <LanguageSwitcher />\r\n          <div\r\n            className=\"pointer-events-none absolute inset-0 z-0 h-full w-full rounded-full opacity-60 blur-2xl\"\r\n            style={{\r\n              background: \"linear-gradient(90deg, #ff80b5 0%, #9089fc 100%)\",\r\n              filter: \"blur(32px)\",\r\n            }}\r\n          />\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            asChild\r\n            className=\"group relative z-10\"\r\n          >\r\n            <Link href=\"https://github.com/bytedance/deer-flow\" target=\"_blank\">\r\n              <GitHubLogoIcon className=\"size-4\" />\r\n              {t('starOnGitHub')}\r\n              {env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY &&\r\n                env.GITHUB_OAUTH_TOKEN && <StarCounter />}\r\n            </Link>\r\n          </Button>\r\n        </div>\r\n      </div>\r\n      <hr className=\"from-border/0 via-border/70 to-border/0 m-0 h-px w-full border-none bg-gradient-to-r\" />\r\n    </header>\r\n  );\r\n}\r\n\r\nexport async function StarCounter() {\r\n  let stars = 1000; // Default value\r\n\r\n  try {\r\n    const response = await fetch(\r\n      \"https://api.github.com/repos/bytedance/deer-flow\",\r\n      {\r\n        headers: env.GITHUB_OAUTH_TOKEN\r\n          ? {\r\n              Authorization: `Bearer ${env.GITHUB_OAUTH_TOKEN}`,\r\n              \"Content-Type\": \"application/json\",\r\n            }\r\n          : {},\r\n        next: {\r\n          revalidate: 3600,\r\n        },\r\n      },\r\n    );\r\n\r\n    if (response.ok) {\r\n      const data = await response.json();\r\n      stars = data.stargazers_count ?? stars; // Update stars if API response is valid\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error fetching GitHub stars:\", error);\r\n  }\r\n  return (\r\n    <>\r\n      <StarFilledIcon className=\"size-4 transition-colors duration-300 group-hover:text-yellow-500\" />\r\n      {stars && (\r\n        <NumberTicker className=\"font-mono tabular-nums\" value={stars} />\r\n      )}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;AAE/B;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;AAEO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,6VAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACE,uVAAC;QAAO,WAAU;;0BAChB,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBAAI,WAAU;;0CACb,uVAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,uVAAC;0CAAK;;;;;;;;;;;;kCAER,uVAAC;wBAAI,WAAU;;0CACb,uVAAC,2JAAA,CAAA,mBAAgB;;;;;0CACjB,uVAAC;gCACC,WAAU;gCACV,OAAO;oCACL,YAAY;oCACZ,QAAQ;gCACV;;;;;;0CAEF,uVAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,OAAO;gCACP,WAAU;0CAEV,cAAA,uVAAC,qQAAA,CAAA,UAAI;oCAAC,MAAK;oCAAyC,QAAO;;sDACzD,uVAAC,kRAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;wCACzB,EAAE;wCACF,0GAAA,CAAA,MAAG,CAAC,+BAA+B,IAClC,0GAAA,CAAA,MAAG,CAAC,kBAAkB,kBAAI,uVAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAKrC,uVAAC;gBAAG,WAAU;;;;;;;;;;;;AAGpB;AAEO,eAAe;IACpB,IAAI,QAAQ,MAAM,gBAAgB;IAElC,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,oDACA;YACE,SAAS,0GAAA,CAAA,MAAG,CAAC,kBAAkB,GAC3B;gBACE,eAAe,CAAC,OAAO,EAAE,0GAAA,CAAA,MAAG,CAAC,kBAAkB,EAAE;gBACjD,gBAAgB;YAClB,IACA,CAAC;YACL,MAAM;gBACJ,YAAY;YACd;QACF;QAGF,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,KAAK,gBAAgB,IAAI,OAAO,wCAAwC;QAClF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;IAChD;IACA,qBACE;;0BACE,uVAAC,kRAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;YACzB,uBACC,uVAAC,iJAAA,CAAA,eAAY;gBAAC,WAAU;gBAAyB,OAAO;;;;;;;;AAIhE", "debugId": null}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/magicui/aurora-text.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuroraText = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraText() from the server but AuroraText is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/magicui/aurora-text.tsx <module evaluation>\",\n    \"AuroraText\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,wEACA", "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/magicui/aurora-text.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuroraText = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraText() from the server but AuroraText is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/magicui/aurora-text.tsx\",\n    \"AuroraText\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,oDACA", "debugId": null}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/magicui/flickering-grid.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FlickeringGrid = registerClientReference(\n    function() { throw new Error(\"Attempted to call FlickeringGrid() from the server but FlickeringGrid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/magicui/flickering-grid.tsx <module evaluation>\",\n    \"FlickeringGrid\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,4EACA", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/components/magicui/flickering-grid.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FlickeringGrid = registerClientReference(\n    function() { throw new Error(\"Attempted to call FlickeringGrid() from the server but FlickeringGrid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/magicui/flickering-grid.tsx\",\n    \"FlickeringGrid\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,wDACA", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/app/landing/components/jumbotron.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { GithubFilled } from \"@ant-design/icons\";\r\nimport { ChevronRight } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { useTranslations } from 'next-intl';\r\n\r\nimport { AuroraText } from \"~/components/magicui/aurora-text\";\r\nimport { FlickeringGrid } from \"~/components/magicui/flickering-grid\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { env } from \"~/env\";\r\n\r\nexport function Jumbotron() {\r\n  const t = useTranslations('hero');\r\n  const tCommon = useTranslations('common');\r\n  \r\n  return (\r\n    <section className=\"flex h-[95vh] w-full flex-col items-center justify-center pb-15\">\r\n      <FlickeringGrid\r\n        id=\"deer-hero-bg\"\r\n        className={`absolute inset-0 z-0 [mask-image:radial-gradient(800px_circle_at_center,white,transparent)]`}\r\n        squareSize={4}\r\n        gridGap={4}\r\n        color=\"#60A5FA\"\r\n        maxOpacity={0.133}\r\n        flickerChance={0.1}\r\n      />\r\n      <FlickeringGrid\r\n        id=\"deer-hero\"\r\n        className=\"absolute inset-0 z-0 translate-y-[2vh] mask-[url(/images/deer-hero.svg)] mask-size-[100vw] mask-center mask-no-repeat md:mask-size-[72vh]\"\r\n        squareSize={3}\r\n        gridGap={6}\r\n        color=\"#60A5FA\"\r\n        maxOpacity={0.64}\r\n        flickerChance={0.12}\r\n      />\r\n      <div className=\"relative z-10 flex flex-col items-center justify-center gap-12\">\r\n        <h1 className=\"text-center text-4xl font-bold md:text-6xl\">\r\n          <span className=\"bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent\">\r\n            {t('title')}{\" \"}\r\n          </span>\r\n          <AuroraText>{t('subtitle')}</AuroraText>\r\n        </h1>\r\n        <p className=\"max-w-4xl p-2 text-center text-sm opacity-85 md:text-2xl\">\r\n          {t('description')}\r\n        </p>\r\n        <div className=\"flex gap-6\">\r\n          <Button className=\"hidden text-lg md:flex md:w-42\" size=\"lg\" asChild>\r\n            <Link\r\n              target={\r\n                env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY ? \"_blank\" : undefined\r\n              }\r\n              href={\r\n                env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY\r\n                  ? \"https://github.com/bytedance/deer-flow\"\r\n                  : \"/chat\"\r\n              }\r\n            >\r\n              {tCommon('getStarted')} <ChevronRight />\r\n            </Link>\r\n          </Button>\r\n                    <Button className=\"hidden text-lg md:flex md:w-50\" size=\"lg\" asChild>\r\n            <Link\r\n              target={\r\n                env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY ? \"_blank\" : undefined\r\n              }\r\n              href={\r\n                env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY\r\n                  ? \"https://github.com/bytedance/deer-flow\"\r\n                  : \"/workflow\"\r\n              }\r\n            >\r\n              {tCommon('getWorkflow')} <ChevronRight />\r\n            </Link>\r\n          </Button>\r\n        </div>\r\n      </div>\r\n      <div className=\"absolute bottom-8 flex text-xs opacity-50\">\r\n        <p>{t('footnote')}</p>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAG/B;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;AAEO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,6VAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,UAAU,CAAA,GAAA,6VAAA,CAAA,kBAAe,AAAD,EAAE;IAEhC,qBACE,uVAAC;QAAQ,WAAU;;0BACjB,uVAAC,mJAAA,CAAA,iBAAc;gBACb,IAAG;gBACH,WAAW,CAAC,2FAA2F,CAAC;gBACxG,YAAY;gBACZ,SAAS;gBACT,OAAM;gBACN,YAAY;gBACZ,eAAe;;;;;;0BAEjB,uVAAC,mJAAA,CAAA,iBAAc;gBACb,IAAG;gBACH,WAAU;gBACV,YAAY;gBACZ,SAAS;gBACT,OAAM;gBACN,YAAY;gBACZ,eAAe;;;;;;0BAEjB,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBAAG,WAAU;;0CACZ,uVAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAU;;;;;;;0CAEf,uVAAC,+IAAA,CAAA,aAAU;0CAAE,EAAE;;;;;;;;;;;;kCAEjB,uVAAC;wBAAE,WAAU;kCACV,EAAE;;;;;;kCAEL,uVAAC;wBAAI,WAAU;;0CACb,uVAAC,kIAAA,CAAA,SAAM;gCAAC,WAAU;gCAAiC,MAAK;gCAAK,OAAO;0CAClE,cAAA,uVAAC,qQAAA,CAAA,UAAI;oCACH,QACE,0GAAA,CAAA,MAAG,CAAC,+BAA+B,GAAG,WAAW;oCAEnD,MACE,0GAAA,CAAA,MAAG,CAAC,+BAA+B,GAC/B,2CACA;;wCAGL,QAAQ;wCAAc;sDAAC,uVAAC,0SAAA,CAAA,eAAY;;;;;;;;;;;;;;;;0CAG/B,uVAAC,kIAAA,CAAA,SAAM;gCAAC,WAAU;gCAAiC,MAAK;gCAAK,OAAO;0CAC5E,cAAA,uVAAC,qQAAA,CAAA,UAAI;oCACH,QACE,0GAAA,CAAA,MAAG,CAAC,+BAA+B,GAAG,WAAW;oCAEnD,MACE,0GAAA,CAAA,MAAG,CAAC,+BAA+B,GAC/B,2CACA;;wCAGL,QAAQ;wCAAe;sDAAC,uVAAC,0SAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK9C,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC;8BAAG,EAAE;;;;;;;;;;;;;;;;;AAId", "debugId": null}}, {"offset": {"line": 609, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/app/landing/components/ray.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport function Ray() {\r\n  return (\r\n    <svg\r\n      className=\"animate-spotlight pointer-events-none fixed -top-80 left-0 z-[99] h-[169%] w-[138%] opacity-0 md:-top-20 md:left-60 lg:w-[84%]\"\r\n      viewBox=\"0 0 3787 2842\"\r\n      fill=\"none\"\r\n    >\r\n      <g filter=\"url(#filter)\">\r\n        <ellipse\r\n          cx=\"1924.71\"\r\n          cy=\"273.501\"\r\n          rx=\"1924.71\"\r\n          ry=\"273.501\"\r\n          transform=\"matrix(-0.822377 -0.568943 -0.568943 0.822377 3631.88 2291.09)\"\r\n          fill=\"white\"\r\n          fillOpacity=\"0.21\"\r\n        ></ellipse>\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter\"\r\n          x=\"0.860352\"\r\n          y=\"0.838989\"\r\n          width=\"3785.16\"\r\n          height=\"2840.26\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\"></feFlood>\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"shape\"\r\n          ></feBlend>\r\n          <feGaussianBlur\r\n            stdDeviation=\"151\"\r\n            result=\"effect1_foregroundBlur_1065_8\"\r\n          ></feGaussianBlur>\r\n        </filter>\r\n      </defs>\r\n    </svg>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;AAExB,SAAS;IACd,qBACE,uVAAC;QACC,WAAU;QACV,SAAQ;QACR,MAAK;;0BAEL,uVAAC;gBAAE,QAAO;0BACR,cAAA,uVAAC;oBACC,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,WAAU;oBACV,MAAK;oBACL,aAAY;;;;;;;;;;;0BAGhB,uVAAC;0BACC,cAAA,uVAAC;oBACC,IAAG;oBACH,GAAE;oBACF,GAAE;oBACF,OAAM;oBACN,QAAO;oBACP,aAAY;oBACZ,2BAA0B;;sCAE1B,uVAAC;4BAAQ,cAAa;4BAAI,QAAO;;;;;;sCACjC,uVAAC;4BACC,MAAK;4BACL,IAAG;4BACH,KAAI;4BACJ,QAAO;;;;;;sCAET,uVAAC;4BACC,cAAa;4BACb,QAAO;;;;;;;;;;;;;;;;;;;;;;;AAMnB", "debugId": null}}, {"offset": {"line": 702, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/dataset/video/video-flow/web/src/app/page.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { useTranslations } from 'next-intl';\r\nimport { useMemo } from \"react\";\r\n\r\nimport { SiteHeader } from \"./chat/components/site-header\";\r\nimport { Jumbotron } from \"./landing/components/jumbotron\";\r\nimport { Ray } from \"./landing/components/ray\";\r\n\r\nexport default function HomePage() {\r\n  return (\r\n    <div className=\"flex flex-col items-center\">\r\n      <SiteHeader />\r\n      <main className=\"container flex flex-col items-center justify-center gap-56\">\r\n        <Jumbotron />\r\n      </main>\r\n      <Footer />\r\n      <Ray />\r\n    </div>\r\n  );\r\n}\r\nfunction Footer() {\r\n  const t = useTranslations('footer');\r\n  const year = useMemo(() => new Date().getFullYear(), []);\r\n  return (\r\n    <footer className=\"container mt-32 flex flex-col items-center justify-center\">\r\n      <hr className=\"from-border/0 via-border/70 to-border/0 m-0 h-px w-full border-none bg-gradient-to-r\" />\r\n      <div className=\"text-muted-foreground container flex h-20 flex-col items-center justify-center text-sm\">\r\n        <p className=\"text-center font-serif text-lg md:text-xl\">\r\n          &quot;{t('quote')}&quot;\r\n        </p>\r\n      </div>\r\n      <div className=\"text-muted-foreground container mb-8 flex flex-col items-center justify-center text-xs\">\r\n        <p>{t('license')}</p>\r\n        <p>&copy; {year} {t('copyright')}</p>\r\n      </div>\r\n    </footer>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AAEA;AACA;AACA;;;;;;;AAEe,SAAS;IACtB,qBACE,uVAAC;QAAI,WAAU;;0BACb,uVAAC,mJAAA,CAAA,aAAU;;;;;0BACX,uVAAC;gBAAK,WAAU;0BACd,cAAA,uVAAC,iJAAA,CAAA,YAAS;;;;;;;;;;0BAEZ,uVAAC;;;;;0BACD,uVAAC,2IAAA,CAAA,MAAG;;;;;;;;;;;AAGV;AACA,SAAS;IACP,MAAM,IAAI,CAAA,GAAA,6VAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,OAAO,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE,IAAM,IAAI,OAAO,WAAW,IAAI,EAAE;IACvD,qBACE,uVAAC;QAAO,WAAU;;0BAChB,uVAAC;gBAAG,WAAU;;;;;;0BACd,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC;oBAAE,WAAU;;wBAA4C;wBAChD,EAAE;wBAAS;;;;;;;;;;;;0BAGtB,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;kCAAG,EAAE;;;;;;kCACN,uVAAC;;4BAAE;4BAAQ;4BAAK;4BAAE,EAAE;;;;;;;;;;;;;;;;;;;AAI5B", "debugId": null}}]}