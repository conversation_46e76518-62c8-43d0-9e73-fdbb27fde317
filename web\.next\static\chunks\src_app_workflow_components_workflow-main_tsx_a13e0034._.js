(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/app/workflow/components/workflow-main.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_47e59e34._.js",
  "static/chunks/node_modules__pnpm_7d0530e6._.js",
  "static/chunks/src_app_workflow_components_workflow-main_tsx_11b6db1b._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/app/workflow/components/workflow-main.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);